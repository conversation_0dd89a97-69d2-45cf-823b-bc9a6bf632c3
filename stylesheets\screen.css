@charset "UTF-8";
/*! normalize.css v3.0.2 | MIT License | git.io/normalize */
/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 */
html {
  font-family: sans-serif;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */ }

/**
 * Remove default margin.
 */
body {
  margin: 0; }

/* HTML5 display definitions
   ========================================================================== */
/**
 * Correct `block` display not defined for any HTML5 element in IE 8/9.
 * Correct `block` display not defined for `details` or `summary` in IE 10/11
 * and Firefox.
 * Correct `block` display not defined for `main` in IE 11.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block; }

/**
 * 1. Correct `inline-block` display not defined in IE 8/9.
 * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.
 */
audio,
canvas,
progress,
video {
  display: inline-block;
  /* 1 */
  vertical-align: baseline;
  /* 2 */ }

/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */
audio:not([controls]) {
  display: none;
  height: 0; }

/**
 * Address `[hidden]` styling not present in IE 8/9/10.
 * Hide the `template` element in IE 8/9/11, Safari, and Firefox < 22.
 */
[hidden],
template {
  display: none; }

/* Links
   ========================================================================== */
/**
 * Remove the gray background color from active links in IE 10.
 */
a {
  background-color: transparent; }

/**
 * Improve readability when focused and also mouse hovered in all browsers.
 */
a:active,
a:hover {
  outline: 0; }

/* Text-level semantics
   ========================================================================== */
/**
 * Address styling not present in IE 8/9/10/11, Safari, and Chrome.
 */
abbr[title] {
  border-bottom: 1px dotted; }

/**
 * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.
 */
b,
strong {
  font-weight: bold; }

/**
 * Address styling not present in Safari and Chrome.
 */
dfn {
  font-style: italic; }

/**
 * Address variable `h1` font-size and margin within `section` and `article`
 * contexts in Firefox 4+, Safari, and Chrome.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0; }

/**
 * Address styling not present in IE 8/9.
 */
mark {
  background: #ff0;
  color: #000; }

/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%; }

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

sup {
  top: -0.5em; }

sub {
  bottom: -0.25em; }

/* Embedded content
   ========================================================================== */
/**
 * Remove border when inside `a` element in IE 8/9/10.
 */
img {
  border: 0; }

/**
 * Correct overflow not hidden in IE 9/10/11.
 */
svg:not(:root) {
  overflow: hidden; }

/* Grouping content
   ========================================================================== */
/**
 * Address margin not present in IE 8/9 and Safari.
 */
figure {
  margin: 1em 40px; }

/**
 * Address differences between Firefox and other browsers.
 */
hr {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  height: 0; }

/**
 * Contain overflow in all browsers.
 */
pre {
  overflow: auto; }

/**
 * Address odd `em`-unit font size rendering in all browsers.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em; }

/* Forms
   ========================================================================== */
/**
 * Known limitation: by default, Chrome and Safari on OS X allow very limited
 * styling of `select`, unless a `border` property is set.
 */
/**
 * 1. Correct color not being inherited.
 *    Known issue: affects color of disabled elements.
 * 2. Correct font properties not being inherited.
 * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.
 */
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  /* 1 */
  font: inherit;
  /* 2 */
  margin: 0;
  /* 3 */ }

/**
 * Address `overflow` set to `hidden` in IE 8/9/10/11.
 */
button {
  overflow: visible; }

/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.
 * Correct `select` style inheritance in Firefox.
 */
button,
select {
  text-transform: none; }

/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */ }

/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default; }

/**
 * Remove inner padding and border in Firefox 4+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0; }

/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
input {
  line-height: normal; }

/**
 * It's recommended that you don't attempt to style these elements.
 * Firefox's implementation doesn't respect box-sizing, padding, or width.
 *
 * 1. Address box sizing set to `content-box` in IE 8/9/10.
 * 2. Remove excess padding in IE 8/9/10.
 */
input[type="checkbox"],
input[type="radio"] {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */ }

/**
 * Fix the cursor style for Chrome's increment/decrement buttons. For certain
 * `font-size` values of the `input`, it causes the cursor style of the
 * decrement button to change from `default` to `text`.
 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto; }

/**
 * 1. Address `appearance` set to `searchfield` in Safari and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari and Chrome
 *    (include `-moz` to future-proof).
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  -webkit-box-sizing: content-box;
  /* 2 */
  box-sizing: content-box; }

/**
 * Remove inner padding and search cancel button in Safari and Chrome on OS X.
 * Safari (but not Chrome) clips the cancel button when the search input has
 * padding (and `textfield` appearance).
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em; }

/**
 * 1. Correct `color` not being inherited in IE 8/9/10/11.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  /* 2 */ }

/**
 * Remove default vertical scrollbar in IE 8/9/10/11.
 */
textarea {
  overflow: auto; }

/**
 * Don't inherit the `font-weight` (applied by a rule above).
 * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.
 */
optgroup {
  font-weight: bold; }

/* Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0; }

td,
th {
  padding: 0; }

/*
Copyright 2008-2013 Concur Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License"); you may
not use this file except in compliance with the License. You may obtain
a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
*/
html, body, .content h3, .content h4, .content h5, .content h6, .content h2, .content h1 {
  font-family: "Helvetica Neue", Helvetica, Arial, "Microsoft Yahei","微软雅黑", STXihei, "华文细黑", sans-serif;
  font-size: 13px; }

.content h3, .content h4, .content h5, .content h6, .content h2, .content h1 {
  font-weight: bold; }

.content pre, .content code {
  font-family: Consolas, Menlo, Monaco, "Lucida Console", "Liberation Mono", "DejaVu Sans Mono", "Bitstream Vera Sans Mono", "Courier New", monospace, serif;
  font-size: 12px;
  line-height: 1.5; }

.content code {
  word-break: break-all;
  -webkit-hyphens: auto;
      -ms-hyphens: auto;
          hyphens: auto; }

@font-face {
  font-family: 'slate';
  src: url("../fonts/slate.eot?-syv14m");
  src: url("../fonts/slate.eot?#iefix-syv14m") format("embedded-opentype"), url("../fonts/slate.woff2?-syv14m") format("woff2"), url("../fonts/slate.woff?-syv14m") format("woff"), url("../fonts/slate.ttf?-syv14m") format("truetype"), url("../fonts/slate.svg?-syv14m#slate") format("svg");
  font-weight: normal;
  font-style: normal; }

.tocify-wrapper > .search:before, .content aside.success:before, .content aside.notice:before, .content aside.warning:before {
  font-family: 'slate';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1; }

.content aside.warning:before {
  content: "\e600"; }

.content aside.notice:before {
  content: "\e602"; }

.content aside.success:before {
  content: "\e606"; }

.tocify-wrapper > .search:before {
  content: "\e607"; }

/*
Copyright 2008-2013 Concur Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License"); you may
not use this file except in compliance with the License. You may obtain
a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
*/
html, body {
  color: #333;
  padding: 0;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #eaf2f6;
  height: 100%;
  -webkit-text-size-adjust: none;
  /* Never autoresize text */ }

#toc > ul > li > a > span {
  float: right;
  background-color: #2484FF;
  border-radius: 40px;
  width: 20px; }

.tocify-wrapper {
  -webkit-transition: left 0.3s ease-in-out;
  transition: left 0.3s ease-in-out;
  overflow-y: auto;
  overflow-x: hidden;
  position: fixed;
  z-index: 30;
  top: 0;
  left: 0;
  bottom: 0;
  width: 230px;
  background-color: #393939;
  font-size: 13px;
  font-weight: bold; }
  .tocify-wrapper .lang-selector {
    display: none; }
    .tocify-wrapper .lang-selector a {
      padding-top: 0.5em;
      padding-bottom: 0.5em; }
  .tocify-wrapper > img {
    display: block;
    max-width: 100%; }
  .tocify-wrapper > .search {
    position: relative; }
    .tocify-wrapper > .search input {
      background: #393939;
      border-width: 0 0 1px 0;
      border-color: #666;
      padding: 6px 0 6px 20px;
      -webkit-box-sizing: border-box;
              box-sizing: border-box;
      margin: 10px 15px;
      width: 200px;
      outline: none;
      color: #fff;
      border-radius: 0;
      /* ios has a default border radius */ }
    .tocify-wrapper > .search:before {
      position: absolute;
      top: 17px;
      left: 15px;
      color: #fff; }
  .tocify-wrapper img + .tocify {
    margin-top: 20px; }
  .tocify-wrapper .search-results {
    margin-top: 0;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    height: 0;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-transition-property: height, margin;
    transition-property: height, margin;
    -webkit-transition-duration: 180ms;
            transition-duration: 180ms;
    -webkit-transition-timing-function: ease-in-out;
            transition-timing-function: ease-in-out;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0) 8px), linear-gradient(to top, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0) 8px), linear-gradient(to bottom, black, rgba(0, 0, 0, 0) 1.5px), linear-gradient(to top, #939393, rgba(147, 147, 147, 0) 1.5px), #262626; }
    .tocify-wrapper .search-results.visible {
      height: 30%;
      margin-bottom: 1em; }
    .tocify-wrapper .search-results li {
      margin: 1em 15px;
      line-height: 1; }
    .tocify-wrapper .search-results a {
      color: #fff;
      text-decoration: none; }
      .tocify-wrapper .search-results a:hover {
        text-decoration: underline; }
  .tocify-wrapper .tocify-item > a, .tocify-wrapper .toc-footer li {
    padding: 0 15px 0 15px;
    display: block;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis; }
  .tocify-wrapper ul, .tocify-wrapper li {
    list-style: none;
    margin: 0;
    padding: 0;
    line-height: 28px; }
  .tocify-wrapper li {
    color: #fff;
    -webkit-transition-property: background;
    transition-property: background;
    -webkit-transition-timing-function: linear;
            transition-timing-function: linear;
    -webkit-transition-duration: 230ms;
            transition-duration: 230ms; }
  .tocify-wrapper .tocify-focus {
    -webkit-box-shadow: 0px 1px 0px #000;
            box-shadow: 0px 1px 0px #000;
    background-color: #2467af;
    color: #fff; }
  .tocify-wrapper .tocify-subheader {
    display: none;
    background-color: #262626;
    font-weight: 500;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0) 8px), linear-gradient(to top, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0) 8px), linear-gradient(to bottom, black, rgba(0, 0, 0, 0) 1.5px), linear-gradient(to top, #939393, rgba(147, 147, 147, 0) 1.5px), #262626; }
    .tocify-wrapper .tocify-subheader .tocify-item > a {
      padding-left: 25px;
      font-size: 12px; }
    .tocify-wrapper .tocify-subheader > li:last-child {
      -webkit-box-shadow: none;
              box-shadow: none; }
  .tocify-wrapper .toc-footer {
    padding: 1em 0;
    margin-top: 1em;
    border-top: 1px dashed #666; }
    .tocify-wrapper .toc-footer li, .tocify-wrapper .toc-footer a {
      color: #fff;
      text-decoration: none; }
    .tocify-wrapper .toc-footer a:hover {
      text-decoration: underline; }
    .tocify-wrapper .toc-footer li {
      font-size: 0.8em;
      line-height: 1.7;
      text-decoration: none; }

#nav-button {
  padding: 0 1.5em 5em 0;
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  color: #000;
  text-decoration: none;
  font-weight: bold;
  opacity: 0.7;
  line-height: 16px;
  -webkit-transition: left 0.3s ease-in-out;
  transition: left 0.3s ease-in-out; }
  #nav-button span {
    display: block;
    padding: 6px 6px 6px;
    background-color: rgba(234, 242, 246, 0.7);
    -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
    -webkit-transform: rotate(-90deg) translate(-100%, 0);
            transform: rotate(-90deg) translate(-100%, 0);
    border-radius: 0 0 0 5px; }
  #nav-button img {
    height: 16px;
    vertical-align: bottom; }
  #nav-button:hover {
    opacity: 1; }
  #nav-button.open {
    left: 230px; }

.page-wrapper {
  margin-left: 230px;
  position: relative;
  z-index: 10;
  background-color: #eaf2f6;
  min-height: 100%;
  padding-bottom: 1px; }
  .page-wrapper .dark-box {
    width: 50%;
    background-color: #393939;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0; }
  .page-wrapper .lang-selector {
    position: fixed;
    z-index: 50;
    border-bottom: 5px solid #393939; }

.lang-selector {
  background-color: #222;
  width: 100%;
  font-weight: bold; }
  .lang-selector a {
    display: block;
    float: left;
    color: #fff;
    text-decoration: none;
    padding: 0 10px;
    line-height: 30px;
    outline: 0; }
    .lang-selector a:active, .lang-selector a:focus {
      background-color: #111;
      color: #fff; }
    .lang-selector a.active {
      background-color: #393939;
      color: #fff; }
  .lang-selector:after {
    content: '';
    clear: both;
    display: block; }

.content {
  position: relative;
  z-index: 30; }
  .content:after {
    content: '';
    display: block;
    clear: both; }
  .content > h1, .content > h2, .content > h3, .content > h4, .content > h5, .content > h6, .content > p, .content > table, .content > ul, .content > ol, .content > aside, .content > dl {
    margin-right: 50%;
    padding: 0 28px;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    display: block;
    text-shadow: 0px 1px 0px #fff; }
  .content > ul, .content > ol {
    padding-left: 43px; }
  .content > h1, .content > h2, .content > div {
    clear: both; }
  .content h1 {
    font-size: 30px;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
    border-bottom: 1px solid #ccc;
    margin-bottom: 21px;
    margin-top: 2em;
    border-top: 1px solid #ddd;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#f9f9f9));
    background-image: linear-gradient(to bottom, #fff, #f9f9f9); }
  .content h1:first-child, .content div:first-child + h1 {
    border-top-width: 0;
    margin-top: 0; }
  .content h2 {
    font-size: 20px;
    margin-top: 4em;
    margin-bottom: 0;
    border-top: 1px solid #ccc;
    padding-top: 1.2em;
    padding-bottom: 1.2em;
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.4)), to(rgba(255, 255, 255, 0)));
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0)); }
  .content h1 + h2, .content h1 + div + h2 {
    margin-top: -21px;
    border-top: none; }
  .content h3, .content h4, .content h5, .content h6 {
    font-size: 15px;
    margin-top: 2.5em;
    margin-bottom: 0.8em; }
  .content h4, .content h5, .content h6 {
    font-size: 10px; }
  .content hr {
    margin: 2em 0;
    border-top: 2px solid #393939;
    border-bottom: 2px solid #eaf2f6; }
  .content table {
    margin-bottom: 1em;
    overflow: auto; }
    .content table th, .content table td {
      text-align: left;
      vertical-align: top;
      line-height: 1.6; }
    .content table th {
      padding: 5px 10px;
      border-bottom: 1px solid #ccc;
      vertical-align: bottom; }
    .content table td {
      padding: 10px; }
    .content table tr:last-child {
      border-bottom: 1px solid #ccc; }
    .content table tr:nth-child(odd) > td {
      background-color: #f9fbfc; }
    .content table tr:nth-child(even) > td {
      background-color: #f3f7fa; }
  .content dt {
    font-weight: bold; }
  .content dd {
    margin-left: 15px; }
  .content p, .content li, .content dt, .content dd {
    line-height: 1.6;
    margin-top: 0; }
  .content img {
    max-width: 100%; }
  .content code {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 3px;
    border-radius: 3px; }
  .content pre > code {
    background-color: transparent;
    padding: 0; }
  .content aside {
    padding-top: 1em;
    padding-bottom: 1em;
    text-shadow: 0 1px 0 #c6dde9;
    margin-top: 1.5em;
    margin-bottom: 1.5em;
    background: #8fbcd4;
    line-height: 1.6; }
    .content aside.warning {
      background-color: #c97a7e;
      text-shadow: 0 1px 0 #dfb0b3; }
    .content aside.success {
      background-color: #6ac174;
      text-shadow: 0 1px 0 #a0d7a6; }
  .content aside:before {
    vertical-align: middle;
    padding-right: 0.5em;
    font-size: 14px; }
  .content .search-highlight {
    padding: 2px;
    margin: -2px;
    border-radius: 4px;
    border: 1px solid #F7E633;
    text-shadow: 1px 1px 0 #666;
    background: -webkit-gradient(linear, right bottom, left top, from(#F7E633), to(#F1D32F));
    background: linear-gradient(to top left, #F7E633 0%, #F1D32F 100%); }

.content pre, .content blockquote {
  background-color: #292929;
  color: #fff;
  padding: 2em 28px;
  margin: 0;
  width: 50%;
  float: right;
  clear: right;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.4); }
  .content pre > p, .content blockquote > p {
    margin: 0; }
  .content pre a, .content blockquote a {
    color: #fff;
    text-decoration: none;
    border-bottom: dashed 1px #ccc; }

.content blockquote > p {
  background-color: #1c1c1c;
  border-radius: 5px;
  padding: 13px;
  color: #ccc;
  border-top: 1px solid #000;
  border-bottom: 1px solid #404040; }

@media (max-width: 930px) {
  .tocify-wrapper {
    left: -230px; }
    .tocify-wrapper.open {
      left: 0; }
  .page-wrapper {
    margin-left: 0; }
  #nav-button {
    display: block; }
  .tocify-wrapper .tocify-item > a {
    padding-top: 0.3em;
    padding-bottom: 0.3em; } }

@media (max-width: 700px) {
  .dark-box {
    display: none; }
  .content > h1, .content > h2, .content > h3, .content > h4, .content > h5, .content > h6, .content > p, .content > table, .content > ul, .content > ol, .content > aside, .content > dl {
    margin-right: 0; }
  .tocify-wrapper .lang-selector {
    display: block; }
  .page-wrapper .lang-selector {
    display: none; }
  .content pre, .content blockquote {
    width: auto;
    float: none; }
  .content > pre + h1, .content > blockquote + h1, .content > pre + h2, .content > blockquote + h2, .content > pre + h3, .content > blockquote + h3, .content > pre + h4, .content > blockquote + h4, .content > pre + h5, .content > blockquote + h5, .content > pre + h6, .content > blockquote + h6, .content > pre + p, .content > blockquote + p, .content > pre + table, .content > blockquote + table, .content > pre + ul, .content > blockquote + ul, .content > pre + ol, .content > blockquote + ol, .content > pre + aside, .content > blockquote + aside, .content > pre + dl, .content > blockquote + dl {
    margin-top: 28px; } }

.highlight .c, .highlight .cm, .highlight .c1, .highlight .cs {
  color: #909090; }

.highlight, .highlight .w {
  background-color: #292929; }
