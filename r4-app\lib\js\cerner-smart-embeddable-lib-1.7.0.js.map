{"version": 3, "sources": ["webpack:///webpack/bootstrap 10bcd52f4c7361207090", "webpack:///./node_modules/core-js/library/modules/_core.js", "webpack:///./node_modules/core-js/library/modules/_global.js", "webpack:///./node_modules/core-js/library/modules/_wks.js", "webpack:///./node_modules/core-js/library/modules/_export.js", "webpack:///./node_modules/core-js/library/modules/_an-object.js", "webpack:///./node_modules/core-js/library/modules/_descriptors.js", "webpack:///./node_modules/core-js/library/modules/_object-dp.js", "webpack:///./node_modules/core-js/library/modules/_is-object.js", "webpack:///./node_modules/core-js/library/modules/_to-iobject.js", "webpack:///./node_modules/core-js/library/modules/_hide.js", "webpack:///./node_modules/core-js/library/modules/_has.js", "webpack:///./node_modules/core-js/library/modules/_iterators.js", "webpack:///./node_modules/core-js/library/modules/_ctx.js", "webpack:///./node_modules/core-js/library/modules/_fails.js", "webpack:///./node_modules/babel-runtime/helpers/classCallCheck.js", "webpack:///./node_modules/core-js/library/modules/_cof.js", "webpack:///./node_modules/core-js/library/modules/_library.js", "webpack:///./node_modules/core-js/library/modules/_property-desc.js", "webpack:///./node_modules/core-js/library/modules/_object-keys.js", "webpack:///./node_modules/core-js/library/modules/_to-object.js", "webpack:///./node_modules/core-js/library/modules/es6.string.iterator.js", "webpack:///./node_modules/core-js/library/modules/_object-pie.js", "webpack:///./node_modules/babel-runtime/helpers/createClass.js", "webpack:///./node_modules/core-js/library/modules/web.dom.iterable.js", "webpack:///./node_modules/core-js/library/modules/_a-function.js", "webpack:///./node_modules/core-js/library/modules/_uid.js", "webpack:///./node_modules/core-js/library/modules/_set-to-string-tag.js", "webpack:///./node_modules/xfc/lib/lib/logger.js", "webpack:///./node_modules/core-js/library/modules/_defined.js", "webpack:///./node_modules/core-js/library/modules/_dom-create.js", "webpack:///./node_modules/core-js/library/modules/_to-primitive.js", "webpack:///./node_modules/core-js/library/modules/_object-create.js", "webpack:///./node_modules/core-js/library/modules/_to-length.js", "webpack:///./node_modules/core-js/library/modules/_to-integer.js", "webpack:///./node_modules/core-js/library/modules/_shared-key.js", "webpack:///./node_modules/core-js/library/modules/_shared.js", "webpack:///./node_modules/core-js/library/modules/_enum-bug-keys.js", "webpack:///./node_modules/core-js/library/modules/_classof.js", "webpack:///./node_modules/core-js/library/modules/core.get-iterator-method.js", "webpack:///./node_modules/core-js/library/modules/_object-gops.js", "webpack:///./node_modules/babel-runtime/core-js/promise.js", "webpack:///./node_modules/core-js/library/modules/_new-promise-capability.js", "webpack:///./node_modules/core-js/library/modules/_wks-ext.js", "webpack:///./node_modules/core-js/library/modules/_wks-define.js", "webpack:///./node_modules/babel-runtime/helpers/slicedToArray.js", "webpack:///./node_modules/core-js/library/modules/_iobject.js", "webpack:///./node_modules/core-js/library/modules/_iter-define.js", "webpack:///./node_modules/core-js/library/modules/_ie8-dom-define.js", "webpack:///./node_modules/core-js/library/modules/_redefine.js", "webpack:///./node_modules/core-js/library/modules/_object-keys-internal.js", "webpack:///./node_modules/core-js/library/modules/_html.js", "webpack:///./node_modules/core-js/library/modules/_object-gpo.js", "webpack:///./node_modules/babel-runtime/core-js/object/entries.js", "webpack:///./node_modules/babel-runtime/helpers/extends.js", "webpack:///./node_modules/core-js/library/modules/_iter-call.js", "webpack:///./node_modules/core-js/library/modules/_is-array-iter.js", "webpack:///./node_modules/core-js/library/modules/_species-constructor.js", "webpack:///./node_modules/core-js/library/modules/_task.js", "webpack:///./node_modules/core-js/library/modules/_perform.js", "webpack:///./node_modules/core-js/library/modules/_promise-resolve.js", "webpack:///./node_modules/core-js/library/modules/_iter-detect.js", "webpack:///./node_modules/babel-runtime/core-js/object/get-prototype-of.js", "webpack:///./node_modules/core-js/library/modules/_object-sap.js", "webpack:///./node_modules/babel-runtime/helpers/possibleConstructorReturn.js", "webpack:///./node_modules/babel-runtime/helpers/typeof.js", "webpack:///./node_modules/core-js/library/modules/_meta.js", "webpack:///./node_modules/core-js/library/modules/_object-gopn.js", "webpack:///./node_modules/core-js/library/modules/_object-gopd.js", "webpack:///./node_modules/babel-runtime/helpers/inherits.js", "webpack:///./node_modules/events/events.js", "webpack:///./node_modules/jsonrpc-dispatch/lib/src/index.js", "webpack:///./node_modules/babel-runtime/core-js/object/freeze.js", "webpack:///./node_modules/uuid/lib/rng-browser.js", "webpack:///./node_modules/uuid/lib/bytesToUuid.js", "webpack:///./node_modules/xfc/lib/lib/uri.js", "webpack:///./node_modules/babel-runtime/helpers/toConsumableArray.js", "webpack:///./webpack.entry.js", "webpack:///./src/less/manifest.less", "webpack:///./src/js/index.js", "webpack:///./src/js/cerner-smart-embeddable-lib.js", "webpack:///./node_modules/xfc/lib/index.js", "webpack:///./node_modules/xfc/lib/consumer/index.js", "webpack:///./node_modules/xfc/lib/consumer/consumer.js", "webpack:///./node_modules/babel-runtime/core-js/is-iterable.js", "webpack:///./node_modules/core-js/library/fn/is-iterable.js", "webpack:///./node_modules/core-js/library/modules/es6.array.iterator.js", "webpack:///./node_modules/core-js/library/modules/_add-to-unscopables.js", "webpack:///./node_modules/core-js/library/modules/_iter-step.js", "webpack:///./node_modules/core-js/library/modules/_iter-create.js", "webpack:///./node_modules/core-js/library/modules/_object-dps.js", "webpack:///./node_modules/core-js/library/modules/_array-includes.js", "webpack:///./node_modules/core-js/library/modules/_to-absolute-index.js", "webpack:///./node_modules/core-js/library/modules/_string-at.js", "webpack:///./node_modules/core-js/library/modules/core.is-iterable.js", "webpack:///./node_modules/babel-runtime/core-js/get-iterator.js", "webpack:///./node_modules/core-js/library/fn/get-iterator.js", "webpack:///./node_modules/core-js/library/modules/core.get-iterator.js", "webpack:///./node_modules/core-js/library/fn/object/entries.js", "webpack:///./node_modules/core-js/library/modules/es7.object.entries.js", "webpack:///./node_modules/core-js/library/modules/_object-to-array.js", "webpack:///./node_modules/babel-runtime/core-js/object/define-property.js", "webpack:///./node_modules/core-js/library/fn/object/define-property.js", "webpack:///./node_modules/core-js/library/modules/es6.object.define-property.js", "webpack:///./node_modules/xfc/lib/consumer/frame.js", "webpack:///./node_modules/babel-runtime/core-js/object/assign.js", "webpack:///./node_modules/core-js/library/fn/object/assign.js", "webpack:///./node_modules/core-js/library/modules/es6.object.assign.js", "webpack:///./node_modules/core-js/library/modules/_object-assign.js", "webpack:///./node_modules/core-js/library/fn/promise.js", "webpack:///./node_modules/core-js/library/modules/es6.promise.js", "webpack:///./node_modules/core-js/library/modules/_an-instance.js", "webpack:///./node_modules/core-js/library/modules/_for-of.js", "webpack:///./node_modules/core-js/library/modules/_invoke.js", "webpack:///./node_modules/core-js/library/modules/_microtask.js", "webpack:///./node_modules/core-js/library/modules/_user-agent.js", "webpack:///./node_modules/core-js/library/modules/_redefine-all.js", "webpack:///./node_modules/core-js/library/modules/_set-species.js", "webpack:///./node_modules/core-js/library/modules/es7.promise.finally.js", "webpack:///./node_modules/core-js/library/modules/es7.promise.try.js", "webpack:///./node_modules/core-js/library/fn/object/get-prototype-of.js", "webpack:///./node_modules/core-js/library/modules/es6.object.get-prototype-of.js", "webpack:///./node_modules/babel-runtime/core-js/symbol/iterator.js", "webpack:///./node_modules/core-js/library/fn/symbol/iterator.js", "webpack:///./node_modules/babel-runtime/core-js/symbol.js", "webpack:///./node_modules/core-js/library/fn/symbol/index.js", "webpack:///./node_modules/core-js/library/modules/es6.symbol.js", "webpack:///./node_modules/core-js/library/modules/_enum-keys.js", "webpack:///./node_modules/core-js/library/modules/_is-array.js", "webpack:///./node_modules/core-js/library/modules/_object-gopn-ext.js", "webpack:///./node_modules/core-js/library/modules/es7.symbol.async-iterator.js", "webpack:///./node_modules/core-js/library/modules/es7.symbol.observable.js", "webpack:///./node_modules/babel-runtime/core-js/object/set-prototype-of.js", "webpack:///./node_modules/core-js/library/fn/object/set-prototype-of.js", "webpack:///./node_modules/core-js/library/modules/es6.object.set-prototype-of.js", "webpack:///./node_modules/core-js/library/modules/_set-proto.js", "webpack:///./node_modules/babel-runtime/core-js/object/create.js", "webpack:///./node_modules/core-js/library/fn/object/create.js", "webpack:///./node_modules/core-js/library/modules/es6.object.create.js", "webpack:///./node_modules/process/browser.js", "webpack:///./node_modules/core-js/library/fn/object/freeze.js", "webpack:///./node_modules/core-js/library/modules/es6.object.freeze.js", "webpack:///./node_modules/uuid/index.js", "webpack:///./node_modules/uuid/v1.js", "webpack:///./node_modules/uuid/v4.js", "webpack:///./node_modules/jsonrpc-dispatch/lib/src/errors.js", "webpack:///./node_modules/xfc/lib/provider/index.js", "webpack:///./node_modules/xfc/lib/provider/provider.js", "webpack:///./node_modules/xfc/lib/provider/application.js", "webpack:///./node_modules/babel-runtime/core-js/array/from.js", "webpack:///./node_modules/core-js/library/fn/array/from.js", "webpack:///./node_modules/core-js/library/modules/es6.array.from.js", "webpack:///./node_modules/core-js/library/modules/_create-property.js", "webpack:///./node_modules/xfc/lib/lib/string.js", "webpack:///./node_modules/xfc/lib/lib/dimension.js", "webpack:///./node_modules/mutation-observer/index.js", "webpack:///./src/js/com-overrider.js"], "names": ["Object", "defineProperty", "exports", "value", "_freeze", "require", "_freeze2", "_interopRequireDefault", "_promise", "_promise2", "_extends2", "_extends3", "_classCallCheck2", "_classCallCheck3", "_createClass2", "_createClass3", "_uuid", "_uuid2", "_errors", "_errors2", "obj", "__esModule", "default", "JSONRPCVersion", "JSONRPC", "dispatcher", "methods", "arguments", "length", "undefined", "version", "deferreds", "key", "send", "message", "data", "jsonrpc", "notification", "method", "params", "request", "_this", "resolve", "reject", "id", "v4", "handle", "handleRequest", "handleNotification", "handleResponse", "response", "deferred", "error", "result", "_this2", "code", "METHOD_NOT_FOUND", "apply", "then", "catch", "INTERNAL_ERROR", "CernerSmartEmbeddableLib", "init", "listenForCustomFrameHeight", "window", "calcFrameHeight", "setFrameHeight", "ComOverrider", "override", "Provider", "acls", "document", "getElementsByTagName", "scrollHeight", "h", "trigger", "height", "on", "invokeAPI", "apiName", "name", "Consumer", "_consumer", "_consumer2", "_provider", "_provider2", "toInteger", "max", "Math", "min", "module", "index", "Symbol", "v1", "uuid", "PARSE_ERROR", "INVALID_REQUEST", "INVALID_PARAMS", "MutationObserver", "WebKitMutationObserver", "MozMutationObserver", "WeakMap", "counter", "Date", "now", "random", "prototype", "set", "entry", "writable", "get", "hasValue", "has", "registrationsTable", "setImmediate", "msSetImmediate", "setImmediateQueue", "sentinel", "String", "addEventListener", "e", "queue", "for<PERSON>ach", "func", "push", "postMessage", "isScheduled", "scheduledObservers", "scheduleCallback", "observer", "dispatchCallbacks", "wrapIfNeeded", "node", "ShadowDOMPolyfill", "observers", "sort", "o1", "o2", "uid_", "anyNonEmpty", "takeRecords", "removeTransientObserversFor", "callback_", "nodes_", "registrations", "registration", "removeTransientObservers", "forEachAncestorAndObserverEnqueueRecord", "target", "callback", "parentNode", "j", "options", "subtree", "record", "enqueue", "uid<PERSON><PERSON><PERSON>", "JsMutationObserver", "records_", "observe", "childList", "attributes", "characterData", "attributeOldValue", "attributeFilter", "characterDataOldValue", "SyntaxError", "i", "removeListeners", "Registration", "addListeners", "disconnect", "splice", "copyOfRecords", "MutationRecord", "type", "addedNodes", "removedNodes", "previousSibling", "nextS<PERSON>ling", "attributeName", "attributeNamespace", "oldValue", "copyMutationRecord", "original", "slice", "currentRecord", "recordWithOldValue", "getRecord", "getRecordWithOldValue", "clearRecords", "recordRepresentsCurrentMutation", "selectRecord", "lastRecord", "newRecord", "transientObservedNodes", "records", "recordToReplaceLast", "addListeners_", "removeListeners_", "removeEventListener", "addTransientObserver", "handleEvent", "stopImmediatePropagation", "attrName", "namespace", "relatedNode", "namespaceURI", "MutationEvent", "attrChange", "ADDITION", "prevValue", "indexOf", "changedNode", "cernerSmartEmbeddableLib", "isEdge", "navigator", "userAgent", "self", "top", "APPLINK", "linkMode", "launchObject", "commandLineArgs"], "mappings": ";QAAA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAK;QACL;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;QAEA;QACA;;;;;;;AC7DA,6BAA6B;AAC7B,uCAAuC;;;;;;;ACDvC;AACA;AACA;AACA;AACA;AACA,yCAAyC;;;;;;;ACLzC,YAAY,mBAAO,CAAC,EAAW;AAC/B,UAAU,mBAAO,CAAC,EAAQ;AAC1B,aAAa,mBAAO,CAAC,CAAW;AAChC;;AAEA;AACA;AACA;AACA;;AAEA;;;;;;;ACVA,aAAa,mBAAO,CAAC,CAAW;AAChC,WAAW,mBAAO,CAAC,CAAS;AAC5B,UAAU,mBAAO,CAAC,EAAQ;AAC1B,WAAW,mBAAO,CAAC,CAAS;AAC5B,UAAU,mBAAO,CAAC,EAAQ;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE;AACjE;AACA,kFAAkF;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,cAAc;AACd,cAAc;AACd,cAAc;AACd,eAAe;AACf,eAAe;AACf,eAAe;AACf,gBAAgB;AAChB;;;;;;;AC7DA,eAAe,mBAAO,CAAC,CAAc;AACrC;AACA;AACA;AACA;;;;;;;ACJA;AACA,kBAAkB,mBAAO,CAAC,EAAU;AACpC,iCAAiC,QAAQ,mBAAmB,UAAU,EAAE,EAAE;AAC1E,CAAC;;;;;;;ACHD,eAAe,mBAAO,CAAC,CAAc;AACrC,qBAAqB,mBAAO,CAAC,EAAmB;AAChD,kBAAkB,mBAAO,CAAC,EAAiB;AAC3C;;AAEA,YAAY,mBAAO,CAAC,CAAgB;AACpC;AACA;AACA;AACA;AACA;AACA,GAAG,YAAY;AACf;AACA;AACA;AACA;;;;;;;ACfA;AACA;AACA;;;;;;;ACFA;AACA,cAAc,mBAAO,CAAC,EAAY;AAClC,cAAc,mBAAO,CAAC,EAAY;AAClC;AACA;AACA;;;;;;;ACLA,SAAS,mBAAO,CAAC,CAAc;AAC/B,iBAAiB,mBAAO,CAAC,EAAkB;AAC3C,iBAAiB,mBAAO,CAAC,CAAgB;AACzC;AACA,CAAC;AACD;AACA;AACA;;;;;;;ACPA,uBAAuB;AACvB;AACA;AACA;;;;;;;ACHA;;;;;;;ACAA;AACA,gBAAgB,mBAAO,CAAC,EAAe;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACnBA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;;;;;;ACNa;;AAEb;;AAEA;AACA;AACA;AACA;AACA,E;;;;;;ACRA,iBAAiB;;AAEjB;AACA;AACA;;;;;;;ACJA;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA,YAAY,mBAAO,CAAC,EAAyB;AAC7C,kBAAkB,mBAAO,CAAC,EAAkB;;AAE5C;AACA;AACA;;;;;;;ACNA;AACA,cAAc,mBAAO,CAAC,EAAY;AAClC;AACA;AACA;;;;;;;;ACJa;AACb,UAAU,mBAAO,CAAC,EAAc;;AAEhC;AACA,mBAAO,CAAC,EAAgB;AACxB,6BAA6B;AAC7B,cAAc;AACd;AACA,CAAC;AACD;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA,UAAU;AACV,CAAC;;;;;;;AChBD,cAAc;;;;;;;;ACAD;;AAEb;;AAEA,sBAAsB,mBAAO,CAAC,GAAmC;;AAEjE;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA,mBAAmB,kBAAkB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC,G;;;;;;AC1BD,mBAAO,CAAC,EAAsB;AAC9B,aAAa,mBAAO,CAAC,CAAW;AAChC,WAAW,mBAAO,CAAC,CAAS;AAC5B,gBAAgB,mBAAO,CAAC,EAAc;AACtC,oBAAoB,mBAAO,CAAC,CAAQ;;AAEpC;AACA;AACA;AACA;AACA;;AAEA,eAAe,yBAAyB;AACxC;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBA;AACA;AACA;AACA;;;;;;;ACHA;AACA;AACA;AACA;AACA;;;;;;;ACJA,UAAU,mBAAO,CAAC,CAAc;AAChC,UAAU,mBAAO,CAAC,EAAQ;AAC1B,UAAU,mBAAO,CAAC,CAAQ;;AAE1B;AACA,oEAAoE,iCAAiC;AACrG;;;;;;;;ACNA,+CAAa;;AAEb;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA,E;;;;;;;ACzBA;AACA;AACA;AACA;AACA;;;;;;;ACJA,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,CAAW;AAClC;AACA;AACA;AACA;AACA;;;;;;;ACNA;AACA,eAAe,mBAAO,CAAC,CAAc;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA,eAAe,mBAAO,CAAC,CAAc;AACrC,UAAU,mBAAO,CAAC,EAAe;AACjC,kBAAkB,mBAAO,CAAC,EAAkB;AAC5C,eAAe,mBAAO,CAAC,EAAe;AACtC,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA,eAAe,mBAAO,CAAC,EAAe;AACtC;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAO,CAAC,EAAS;AACnB,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;;;;;;ACxCA;AACA,gBAAgB,mBAAO,CAAC,EAAe;AACvC;AACA;AACA,2DAA2D;AAC3D;;;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACLA,aAAa,mBAAO,CAAC,EAAW;AAChC,UAAU,mBAAO,CAAC,EAAQ;AAC1B;AACA;AACA;;;;;;;ACJA,WAAW,mBAAO,CAAC,CAAS;AAC5B,aAAa,mBAAO,CAAC,CAAW;AAChC;AACA,kDAAkD;;AAElD;AACA,qEAAqE;AACrE,CAAC;AACD;AACA,QAAQ,mBAAO,CAAC,EAAY;AAC5B;AACA,CAAC;;;;;;;ACXD;AACA;AACA;AACA;;;;;;;ACHA;AACA,UAAU,mBAAO,CAAC,EAAQ;AAC1B,UAAU,mBAAO,CAAC,CAAQ;AAC1B;AACA,2BAA2B,kBAAkB,EAAE;;AAE/C;AACA;AACA;AACA;AACA,GAAG,YAAY;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACtBA,cAAc,mBAAO,CAAC,EAAY;AAClC,eAAe,mBAAO,CAAC,CAAQ;AAC/B,gBAAgB,mBAAO,CAAC,EAAc;AACtC,iBAAiB,mBAAO,CAAC,CAAS;AAClC;AACA;AACA;AACA;;;;;;;ACPA;;;;;;;ACAA,kBAAkB,YAAY,mBAAO,CAAC,GAA4B,sB;;;;;;;ACArD;AACb;AACA,gBAAgB,mBAAO,CAAC,EAAe;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;;;;;;ACjBA,YAAY,mBAAO,CAAC,CAAQ;;;;;;;ACA5B,aAAa,mBAAO,CAAC,CAAW;AAChC,WAAW,mBAAO,CAAC,CAAS;AAC5B,cAAc,mBAAO,CAAC,EAAY;AAClC,aAAa,mBAAO,CAAC,EAAY;AACjC,qBAAqB,mBAAO,CAAC,CAAc;AAC3C;AACA,0DAA0D,sBAAsB;AAChF,kFAAkF,wBAAwB;AAC1G;;;;;;;;ACRa;;AAEb;;AAEA,mBAAmB,mBAAO,CAAC,EAAwB;;AAEnD;;AAEA,oBAAoB,mBAAO,CAAC,EAAyB;;AAErD;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,wDAAwD,+BAA+B;AACvF;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA,CAAC,G;;;;;;AClDD;AACA,UAAU,mBAAO,CAAC,EAAQ;AAC1B;AACA;AACA;AACA;;;;;;;;ACLa;AACb,cAAc,mBAAO,CAAC,EAAY;AAClC,cAAc,mBAAO,CAAC,CAAW;AACjC,eAAe,mBAAO,CAAC,EAAa;AACpC,WAAW,mBAAO,CAAC,CAAS;AAC5B,gBAAgB,mBAAO,CAAC,EAAc;AACtC,kBAAkB,mBAAO,CAAC,EAAgB;AAC1C,qBAAqB,mBAAO,CAAC,EAAsB;AACnD,qBAAqB,mBAAO,CAAC,EAAe;AAC5C,eAAe,mBAAO,CAAC,CAAQ;AAC/B,8CAA8C;AAC9C;AACA;AACA;;AAEA,8BAA8B,aAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA,yCAAyC,oCAAoC;AAC7E,6CAA6C,oCAAoC;AACjF,KAAK,4BAA4B,oCAAoC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,mBAAmB;AACnC;AACA;AACA,kCAAkC,2BAA2B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;;;;;;ACpEA,kBAAkB,mBAAO,CAAC,CAAgB,MAAM,mBAAO,CAAC,EAAU;AAClE,+BAA+B,mBAAO,CAAC,EAAe,gBAAgB,mBAAmB,UAAU,EAAE,EAAE;AACvG,CAAC;;;;;;;ACFD,iBAAiB,mBAAO,CAAC,CAAS;;;;;;;ACAlC,UAAU,mBAAO,CAAC,EAAQ;AAC1B,gBAAgB,mBAAO,CAAC,CAAe;AACvC,mBAAmB,mBAAO,CAAC,EAAmB;AAC9C,eAAe,mBAAO,CAAC,EAAe;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AChBA,eAAe,mBAAO,CAAC,CAAW;AAClC;;;;;;;ACDA;AACA,UAAU,mBAAO,CAAC,EAAQ;AAC1B,eAAe,mBAAO,CAAC,EAAc;AACrC,eAAe,mBAAO,CAAC,EAAe;AACtC;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;ACZA,kBAAkB,YAAY,mBAAO,CAAC,EAAmC,sB;;;;;;;ACA5D;;AAEb;;AAEA,cAAc,mBAAO,CAAC,GAA0B;;AAEhD;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA,iBAAiB,sBAAsB;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,E;;;;;;;;;;;;ACtBA;AACA,eAAe,mBAAO,CAAC,CAAc;AACrC;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA,gBAAgB,mBAAO,CAAC,EAAc;AACtC,eAAe,mBAAO,CAAC,CAAQ;AAC/B;;AAEA;AACA;AACA;;;;;;;ACPA;AACA,eAAe,mBAAO,CAAC,CAAc;AACrC,gBAAgB,mBAAO,CAAC,EAAe;AACvC,cAAc,mBAAO,CAAC,CAAQ;AAC9B;AACA;AACA;AACA;AACA;;;;;;;ACRA,UAAU,mBAAO,CAAC,EAAQ;AAC1B,aAAa,mBAAO,CAAC,GAAW;AAChC,WAAW,mBAAO,CAAC,EAAS;AAC5B,UAAU,mBAAO,CAAC,EAAe;AACjC,aAAa,mBAAO,CAAC,CAAW;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,mBAAO,CAAC,EAAQ;AACtB;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACnFA;AACA;AACA,YAAY;AACZ,GAAG;AACH,YAAY;AACZ;AACA;;;;;;;ACNA,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,CAAc;AACrC,2BAA2B,mBAAO,CAAC,EAA2B;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA,eAAe,mBAAO,CAAC,CAAQ;AAC/B;;AAEA;AACA;AACA,iCAAiC,qBAAqB;AACtD;AACA,iCAAiC,SAAS,EAAE;AAC5C,CAAC,YAAY;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,SAAS,qBAAqB;AAC3D,iCAAiC,aAAa;AAC9C;AACA,GAAG,YAAY;AACf;AACA;;;;;;;ACrBA,kBAAkB,YAAY,mBAAO,CAAC,GAA4C,sB;;;;;;ACAlF;AACA,cAAc,mBAAO,CAAC,CAAW;AACjC,WAAW,mBAAO,CAAC,CAAS;AAC5B,YAAY,mBAAO,CAAC,EAAU;AAC9B;AACA,6BAA6B;AAC7B;AACA;AACA,qDAAqD,OAAO,EAAE;AAC9D;;;;;;;;ACTa;;AAEb;;AAEA,eAAe,mBAAO,CAAC,EAAmB;;AAE1C;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA;AACA;;AAEA;AACA,E;;;;;;;AChBa;;AAEb;;AAEA,gBAAgB,mBAAO,CAAC,GAA4B;;AAEpD;;AAEA,cAAc,mBAAO,CAAC,GAAmB;;AAEzC;;AAEA,iHAAiH,mBAAmB,EAAE,mBAAmB,4JAA4J;;AAErT,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA,CAAC;AACD;AACA,E;;;;;;ACpBA,WAAW,mBAAO,CAAC,EAAQ;AAC3B,eAAe,mBAAO,CAAC,CAAc;AACrC,UAAU,mBAAO,CAAC,EAAQ;AAC1B,cAAc,mBAAO,CAAC,CAAc;AACpC;AACA;AACA;AACA;AACA,cAAc,mBAAO,CAAC,EAAU;AAChC,iDAAiD;AACjD,CAAC;AACD;AACA,qBAAqB;AACrB;AACA,SAAS;AACT,GAAG,EAAE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACpDA;AACA,YAAY,mBAAO,CAAC,EAAyB;AAC7C,iBAAiB,mBAAO,CAAC,EAAkB;;AAE3C;AACA;AACA;;;;;;;ACNA,UAAU,mBAAO,CAAC,EAAe;AACjC,iBAAiB,mBAAO,CAAC,EAAkB;AAC3C,gBAAgB,mBAAO,CAAC,CAAe;AACvC,kBAAkB,mBAAO,CAAC,EAAiB;AAC3C,UAAU,mBAAO,CAAC,EAAQ;AAC1B,qBAAqB,mBAAO,CAAC,EAAmB;AAChD;;AAEA,YAAY,mBAAO,CAAC,CAAgB;AACpC;AACA;AACA;AACA;AACA,GAAG,YAAY;AACf;AACA;;;;;;;;ACfa;;AAEb;;AAEA,sBAAsB,mBAAO,CAAC,GAAoC;;AAElE;;AAEA,cAAc,mBAAO,CAAC,GAA0B;;AAEhD;;AAEA,eAAe,mBAAO,CAAC,EAAmB;;AAE1C;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,E;;;;;;;AChCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,sBAAsB;AACvC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA,cAAc;AACd;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA,mBAAmB,SAAS;AAC5B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA,iCAAiC,QAAQ;AACzC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,OAAO;AACP;AACA,sCAAsC,QAAQ;AAC9C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,OAAO;AACxB;AACA;AACA;;AAEA;AACA,QAAQ,yBAAyB;AACjC;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;;;;;;;;AC7ba;;AAEbA,OAAOC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAC3CC,SAAO;AADoC,CAA7C;;AAIA,IAAIC,UAAUC,mBAAOA,CAAC,EAAR,CAAd;;AAEA,IAAIC,WAAWC,uBAAuBH,OAAvB,CAAf;;AAEA,IAAII,WAAWH,mBAAOA,CAAC,EAAR,CAAf;;AAEA,IAAII,YAAYF,uBAAuBC,QAAvB,CAAhB;;AAEA,IAAIE,YAAYL,mBAAOA,CAAC,EAAR,CAAhB;;AAEA,IAAIM,YAAYJ,uBAAuBG,SAAvB,CAAhB;;AAEA,IAAIE,mBAAmBP,mBAAOA,CAAC,EAAR,CAAvB;;AAEA,IAAIQ,mBAAmBN,uBAAuBK,gBAAvB,CAAvB;;AAEA,IAAIE,gBAAgBT,mBAAOA,CAAC,EAAR,CAApB;;AAEA,IAAIU,gBAAgBR,uBAAuBO,aAAvB,CAApB;;AAEA,IAAIE,QAAQX,mBAAOA,CAAC,GAAR,CAAZ;;AAEA,IAAIY,SAASV,uBAAuBS,KAAvB,CAAb;;AAEA,IAAIE,UAAUb,mBAAOA,CAAC,GAAR,CAAd;;AAEA,IAAIc,WAAWZ,uBAAuBW,OAAvB,CAAf;;AAEA,SAASX,sBAAT,CAAgCa,GAAhC,EAAqC;AAAE,SAAOA,OAAOA,IAAIC,UAAX,GAAwBD,GAAxB,GAA8B,EAAEE,SAASF,GAAX,EAArC;AAAwD;;AAE/F,IAAIG,iBAAiB,KAArB;;AAEA;;;;;;;AAOA,IAAIC,UAAU,YAAY;AACxB;;;;;;AAMA,WAASA,OAAT,CAAiBC,UAAjB,EAA6B;AAC3B,QAAIC,UAAUC,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,UAAU,CAAV,MAAiBE,SAAzC,GAAqDF,UAAU,CAAV,CAArD,GAAoE,EAAlF;AACA,KAAC,GAAGd,iBAAiBS,OAArB,EAA8B,IAA9B,EAAoCE,OAApC;;AAEA,SAAKM,OAAL,GAAeP,cAAf;AACA,SAAKQ,SAAL,GAAiB,EAAjB;AACA,SAAKL,OAAL,GAAeA,OAAf;AACA,SAAKD,UAAL,GAAkBA,UAAlB;AACD;;AAED;;;;;AAMA,GAAC,GAAGV,cAAcO,OAAlB,EAA2BE,OAA3B,EAAoC,CAAC;AACnCQ,SAAK,MAD8B;AAEnC7B,WAAO,SAAS8B,IAAT,CAAcC,OAAd,EAAuB;AAC5B,UAAIC,OAAO,CAAC,GAAGxB,UAAUW,OAAd,EAAuB,EAAvB,EAA2BY,OAA3B,CAAX;AACAC,WAAKC,OAAL,GAAe,KAAKN,OAApB;;AAEA,WAAKL,UAAL,CAAgBU,IAAhB;AACD;;AAED;;;;;;;;;AATmC,GAAD,EAkBjC;AACDH,SAAK,cADJ;AAED7B,WAAO,SAASkC,YAAT,CAAsBC,MAAtB,EAA8B;AACnC,UAAIC,SAASZ,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,UAAU,CAAV,MAAiBE,SAAzC,GAAqDF,UAAU,CAAV,CAArD,GAAoE,EAAjF;;AAEA,WAAKM,IAAL,CAAU,EAAEK,QAAQA,MAAV,EAAkBC,QAAQA,MAA1B,EAAV;AACD;;AAED;;;;;;;;;;AARC,GAlBiC,EAoCjC;AACDP,SAAK,SADJ;AAED7B,WAAO,SAASqC,OAAT,CAAiBF,MAAjB,EAAyB;AAC9B,UAAIG,QAAQ,IAAZ;;AAEA,UAAIF,SAASZ,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,UAAU,CAAV,MAAiBE,SAAzC,GAAqDF,UAAU,CAAV,CAArD,GAAoE,EAAjF;;AAEA,aAAO,IAAIlB,UAAUa,OAAd,CAAsB,UAAUoB,OAAV,EAAmBC,MAAnB,EAA2B;AACtD,YAAIC,KAAK3B,OAAOK,OAAP,CAAeuB,EAAf,EAAT;;AAEA;AACA;AACA;AACA;AACAJ,cAAMV,SAAN,CAAgBa,EAAhB,IAAsB,EAAEF,SAASA,OAAX,EAAoBC,QAAQA,MAA5B,EAAtB;;AAEAF,cAAMR,IAAN,CAAW,EAAEW,IAAIA,EAAN,EAAUN,QAAQA,MAAlB,EAA0BC,QAAQA,MAAlC,EAAX;AACD,OAVM,CAAP;AAWD;;AAED;;;;;;;;;AApBC,GApCiC,EAiEjC;AACDP,SAAK,QADJ;AAED7B,WAAO,SAAS2C,MAAT,CAAgBZ,OAAhB,EAAyB;AAC9B;AACA,UAAIA,QAAQI,MAAZ,EAAoB;AAClB;AACA,YAAIJ,QAAQU,EAAZ,EAAgB;AACd,eAAKG,aAAL,CAAmBb,OAAnB;;AAEA;AACD,SAJD,MAIO;AACL,eAAKc,kBAAL,CAAwBd,OAAxB;AACD;;AAED;AACD,OAXD,MAWO,IAAIA,QAAQU,EAAZ,EAAgB;AACrB,aAAKK,cAAL,CAAoBf,OAApB;AACD;AACF;;AAED;;;;;;AApBC,GAjEiC,EA2FjC;AACDF,SAAK,gBADJ;AAED7B,WAAO,SAAS8C,cAAT,CAAwBC,QAAxB,EAAkC;AACvC,UAAIC,WAAW,KAAKpB,SAAL,CAAemB,SAASN,EAAxB,CAAf;AACA,UAAIO,aAAatB,SAAjB,EAA4B;AAC1B;AACD;;AAED,UAAIqB,SAASE,KAAb,EAAoB;AAClBD,iBAASR,MAAT,CAAgBO,SAASE,KAAzB;AACD,OAFD,MAEO;AACLD,iBAAST,OAAT,CAAiBQ,SAASG,MAA1B;AACD;;AAED,aAAO,KAAKtB,SAAL,CAAemB,SAASN,EAAxB,CAAP;AACD;;AAED;;;;;;AAjBC,GA3FiC,EAkHjC;AACDZ,SAAK,eADJ;AAED7B,WAAO,SAAS4C,aAAT,CAAuBP,OAAvB,EAAgC;AACrC,UAAIc,SAAS,IAAb;;AAEA,UAAIhB,SAAS,KAAKZ,OAAL,CAAac,QAAQF,MAArB,CAAb;AACA,UAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;AAChC,YAAIc,QAAQ;AACVlB,mBAAS,gBAAgBI,MAAhB,GAAyB,iBADxB;AAEViB,gBAAMpC,SAASG,OAAT,CAAiBkC;AAFb,SAAZ;AAIA,aAAKvB,IAAL,CAAU,EAAEW,IAAIJ,QAAQI,EAAd,EAAkBQ,OAAOA,KAAzB,EAAV;AACA;AACD;AACD;AACAd,aAAOmB,KAAP,CAAajB,OAAb,EAAsBA,QAAQD,MAA9B,EAAsCmB,IAAtC,CAA2C,UAAUL,MAAV,EAAkB;AAC3DC,eAAOrB,IAAP,CAAY,EAAEW,IAAIJ,QAAQI,EAAd,EAAkBS,QAAQA,MAA1B,EAAZ;AACA;AACD,OAHD,EAGGM,KAHH,CAGS,UAAUzB,OAAV,EAAmB;AAC1B,YAAIkB,QAAQ,EAAElB,SAASA,OAAX,EAAoBqB,MAAMpC,SAASG,OAAT,CAAiBsC,cAA3C,EAAZ;AACAN,eAAOrB,IAAP,CAAY,EAAEW,IAAIJ,QAAQI,EAAd,EAAkBQ,OAAOA,KAAzB,EAAZ;AACD,OAND;AAOD;;AAED;;;;;AAxBC,GAlHiC,EA+IjC;AACDpB,SAAK,oBADJ;AAED7B,WAAO,SAAS6C,kBAAT,CAA4BR,OAA5B,EAAqC;AAC1C,UAAIF,SAAS,KAAKZ,OAAL,CAAac,QAAQF,MAArB,CAAb;AACA,UAAIA,UAAU,OAAOA,MAAP,KAAkB,UAAhC,EAA4C;AAC1CA,eAAOmB,KAAP,CAAajB,OAAb,EAAsBA,QAAQD,MAA9B;AACD;AACF;AAPA,GA/IiC,CAApC;AAwJA,SAAOf,OAAP;AACD,CAhLa,EAAd;;AAkLAtB,QAAQoB,OAAR,GAAkB,CAAC,GAAGhB,SAASgB,OAAb,EAAsBE,OAAtB,CAAlB,C;;;;;;AC/NA,kBAAkB,YAAY,mBAAO,CAAC,GAAkC,sB;;;;;;ACAxE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,iCAAiC;;AAEjC;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB,QAAQ;AAC9B;AACA;AACA;;AAEA;AACA;AACA;;;;;;;ACjCA;AACA;AACA;AACA;AACA;AACA,eAAe,SAAS;AACxB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;ACzBa;;AAEb;AACA;AACA,CAAC;;AAED,uBAAuB,mBAAO,CAAC,EAAsC;;AAErE;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,sB;;;;;;;AC9Ba;;AAEb;;AAEA,YAAY,mBAAO,CAAC,GAAuB;;AAE3C;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA,6CAA6C,gBAAgB;AAC7D;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA,E;;;;;;;;;;;;;ACpBA;;AAEA;AACA,mBAAO,CAAC,EAA0B;;AAElC;AACA;AACA,mBAAO,CAAC,EAAU;;;;;;;ACPlB,yC;;;;;;;;;ACEA;;;;AACA;;;;;;AAHA;;AAKAqC,mCAAyBC,IAAzB;AACAD,mCAAyBE,0BAAzB;;AAEAC,OAAOH,wBAAP,GAAkCG,OAAOH,wBAAP,IAAmC,EAArE;AACAG,OAAOH,wBAAP,CAAgCI,eAAhC,GAAkDJ,mCAAyBI,eAA3E;AACAD,OAAOH,wBAAP,CAAgCK,cAAhC,GAAiDL,mCAAyBK,cAA1E;;AAEAC,uBAAaC,QAAb,CAAsBP,kCAAtB,E;;;;;;;;;;;;;ACVA;;AAEA;;;;AAIA,IAAMA,2BAA2B;;AAE/B;;;AAGAC,QAAM,gBAAM;AACVO,kBAASP,IAAT,CAAc;AACZQ,YAAM,CAAC,6BAAD,EACJ,oCADI,EACkC,gCADlC,EAEJ,6CAFI,EAE2C,0CAF3C,EAGJ,6CAHI,EAG2C,0CAH3C,EAIJ,oCAJI,EAIkC,gDAJlC;AADM,KAAd;AAOD,GAb8B;AAc/B;;;AAGAL,mBAAiB;AAAA,WAAMD,OAAOO,QAAP,CAAgBC,oBAAhB,CAAqC,MAArC,EAA6C,CAA7C,EAAgDC,YAAtD;AAAA,GAjBc;;AAmB/B;;;;AAIAP,kBAAgB,wBAACQ,CAAD,EAAO;AACrBL,kBAASM,OAAT,CAAiB,qBAAjB,EAAwC,EAAEC,QAAQF,CAAV,EAAxC;AACD,GAzB8B;AA0B/B;;;;AAIAX,8BAA4B,sCAAM;AAChCM,kBAASQ,EAAT,CAAY,qBAAZ,EAAmC,YAAM;AACvC,UAAMD,SAAYZ,OAAOH,wBAAP,CAAgCI,eAAhC,EAAZ,OAAN;AACAJ,+BAAyBK,cAAzB,CAAwCU,MAAxC;AACD,KAHD;AAID,GAnC8B;AAoC/B;;;;;AAKAE,aAAW,SAASA,SAAT,CAAmBC,OAAnB,EAA4BxC,MAA5B,EAAoC;AAC7C,QAAIwC,WAAWxC,MAAf,EAAuB;AACrB;AACA8B,oBAASM,OAAT,CAAiB,cAAjB,EAAiC,EAAEK,MAAMD,OAAR,EAAiBxC,cAAjB,EAAjC;AACD;AACF;AA9C8B,CAAjC,C,CARA;;kBAyDesB,wB;;;;;;;ACzDF;;AAEb7D,OAAOC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAC3CC,SAAO;AADoC,CAA7C;AAGAD,QAAQ+E,QAAR,GAAmB/E,QAAQmE,QAAR,GAAmBxC,SAAtC;;AAEA,IAAIqD,YAAY7E,mBAAOA,CAAC,EAAR,CAAhB;;AAEA,IAAI8E,aAAa5E,uBAAuB2E,SAAvB,CAAjB;;AAEA,IAAIE,YAAY/E,mBAAOA,CAAC,GAAR,CAAhB;;AAEA,IAAIgF,aAAa9E,uBAAuB6E,SAAvB,CAAjB;;AAEA,SAAS7E,sBAAT,CAAgCa,GAAhC,EAAqC;AAAE,SAAOA,OAAOA,IAAIC,UAAX,GAAwBD,GAAxB,GAA8B,EAAEE,SAASF,GAAX,EAArC;AAAwD;;AAE/FlB,QAAQmE,QAAR,GAAmBgB,WAAW/D,OAA9B;AACApB,QAAQ+E,QAAR,GAAmBE,WAAW7D,OAA9B,C;;;;;;;AClBa;;AAEbtB,OAAOC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAC3CC,SAAO;AADoC,CAA7C;;AAIA,IAAI+E,YAAY7E,mBAAOA,CAAC,EAAR,CAAhB;;AAEA,IAAI8E,aAAa5E,uBAAuB2E,SAAvB,CAAjB;;AAEA,SAAS3E,sBAAT,CAAgCa,GAAhC,EAAqC;AAAE,SAAOA,OAAOA,IAAIC,UAAX,GAAwBD,GAAxB,GAA8B,EAAEE,SAASF,GAAX,EAArC;AAAwD;;AAE/FlB,QAAQoB,OAAR,GAAkB,IAAI6D,WAAW7D,OAAf,EAAlB,C;;;;;;;ACZa;;AAEb;AACA;AACA,CAAC;;AAED,sBAAsB,mBAAO,CAAC,EAAqC;;AAEnE;;AAEA,eAAe,mBAAO,CAAC,EAAsC;;AAE7D;;AAEA,uBAAuB,mBAAO,CAAC,EAAsC;;AAErE;;AAEA,oBAAoB,mBAAO,CAAC,EAAmC;;AAE/D;;AAEA,aAAa,mBAAO,CAAC,GAAS;;AAE9B;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,gBAAgB,MAAM;AACtB;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,OAAO;AACtB,eAAe,OAAO;AACtB,gBAAgB,MAAM;AACtB;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;;AAEA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED,2B;;;;;;ACxFA,kBAAkB,YAAY,mBAAO,CAAC,EAAgC,sB;;;;;;ACAtE,mBAAO,CAAC,EAA6B;AACrC,mBAAO,CAAC,EAAgC;AACxC,iBAAiB,mBAAO,CAAC,EAA6B;;;;;;;;ACFzC;AACb,uBAAuB,mBAAO,CAAC,EAAuB;AACtD,WAAW,mBAAO,CAAC,EAAc;AACjC,gBAAgB,mBAAO,CAAC,EAAc;AACtC,gBAAgB,mBAAO,CAAC,CAAe;;AAEvC;AACA;AACA;AACA;AACA,iBAAiB,mBAAO,CAAC,EAAgB;AACzC,gCAAgC;AAChC,cAAc;AACd,iBAAiB;AACjB;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;;AAEA;AACA;AACA;;;;;;;ACjCA,8BAA8B;;;;;;;ACA9B;AACA,UAAU;AACV;;;;;;;;ACFa;AACb,aAAa,mBAAO,CAAC,EAAkB;AACvC,iBAAiB,mBAAO,CAAC,EAAkB;AAC3C,qBAAqB,mBAAO,CAAC,EAAsB;AACnD;;AAEA;AACA,mBAAO,CAAC,CAAS,qBAAqB,mBAAO,CAAC,CAAQ,4BAA4B,aAAa,EAAE;;AAEjG;AACA,qDAAqD,4BAA4B;AACjF;AACA;;;;;;;ACZA,SAAS,mBAAO,CAAC,CAAc;AAC/B,eAAe,mBAAO,CAAC,CAAc;AACrC,cAAc,mBAAO,CAAC,EAAgB;;AAEtC,iBAAiB,mBAAO,CAAC,CAAgB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACZA;AACA;AACA,gBAAgB,mBAAO,CAAC,CAAe;AACvC,eAAe,mBAAO,CAAC,EAAc;AACrC,sBAAsB,mBAAO,CAAC,EAAsB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,YAAY,eAAe;AAChC;AACA,KAAK;AACL;AACA;;;;;;;;;;ACtBA,IAAIgE,YAAYjF,mBAAOA,CAAC,EAAR,CAAhB;AACA,IAAIkF,MAAMC,KAAKD,GAAf;AACA,IAAIE,MAAMD,KAAKC,GAAf;AACAC,OAAOxF,OAAP,GAAiB,UAAUyF,KAAV,EAAiB/D,MAAjB,EAAyB;AACxC+D,UAAQL,UAAUK,KAAV,CAAR;AACA,SAAOA,QAAQ,CAAR,GAAYJ,IAAII,QAAQ/D,MAAZ,EAAoB,CAApB,CAAZ,GAAqC6D,IAAIE,KAAJ,EAAW/D,MAAX,CAA5C;AACD,CAHD,C;;;;;;ACHA,gBAAgB,mBAAO,CAAC,EAAe;AACvC,cAAc,mBAAO,CAAC,EAAY;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AChBA,cAAc,mBAAO,CAAC,EAAY;AAClC,eAAe,mBAAO,CAAC,CAAQ;AAC/B,gBAAgB,mBAAO,CAAC,EAAc;AACtC,iBAAiB,mBAAO,CAAC,CAAS;AAClC;AACA;AACA;AACA;AACA;AACA;;;;;;;ACTA,kBAAkB,YAAY,mBAAO,CAAC,EAAiC,sB;;;;;;ACAvE,mBAAO,CAAC,EAA6B;AACrC,mBAAO,CAAC,EAAgC;AACxC,iBAAiB,mBAAO,CAAC,EAA8B;;;;;;;ACFvD,eAAe,mBAAO,CAAC,CAAc;AACrC,UAAU,mBAAO,CAAC,EAA4B;AAC9C,iBAAiB,mBAAO,CAAC,CAAS;AAClC;AACA;AACA;AACA;;;;;;;ACNA,mBAAO,CAAC,GAAkC;AAC1C,iBAAiB,mBAAO,CAAC,CAAqB;;;;;;;ACD9C;AACA,cAAc,mBAAO,CAAC,CAAW;AACjC,eAAe,mBAAO,CAAC,GAAoB;;AAE3C;AACA;AACA;AACA;AACA,CAAC;;;;;;;ACRD,kBAAkB,mBAAO,CAAC,CAAgB;AAC1C,cAAc,mBAAO,CAAC,EAAgB;AACtC,gBAAgB,mBAAO,CAAC,CAAe;AACvC,aAAa,mBAAO,CAAC,EAAe;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACpBA,kBAAkB,YAAY,mBAAO,CAAC,GAA2C,sB;;;;;;ACAjF,mBAAO,CAAC,GAA0C;AAClD,cAAc,mBAAO,CAAC,CAAqB;AAC3C;AACA;AACA;;;;;;;ACJA,cAAc,mBAAO,CAAC,CAAW;AACjC;AACA,iCAAiC,mBAAO,CAAC,CAAgB,cAAc,iBAAiB,mBAAO,CAAC,CAAc,KAAK;;;;;;;;ACFtG;;AAEb;AACA;AACA,CAAC;;AAED,sBAAsB,mBAAO,CAAC,EAAqC;;AAEnE;;AAEA,eAAe,mBAAO,CAAC,EAAsC;;AAE7D;;AAEA,gBAAgB,mBAAO,CAAC,EAA+B;;AAEvD;;AAEA,eAAe,mBAAO,CAAC,EAA+B;;AAEtD;;AAEA,sBAAsB,mBAAO,CAAC,EAA+C;;AAE7E;;AAEA,uBAAuB,mBAAO,CAAC,EAAsC;;AAErE;;AAEA,oBAAoB,mBAAO,CAAC,EAAmC;;AAE/D;;AAEA,kCAAkC,mBAAO,CAAC,EAAiD;;AAE3F;;AAEA,iBAAiB,mBAAO,CAAC,EAAgC;;AAEzD;;AAEA,cAAc,mBAAO,CAAC,EAAQ;;AAE9B,cAAc,mBAAO,CAAC,EAAe;;AAErC;;AAEA,uBAAuB,mBAAO,CAAC,EAAkB;;AAEjD;;AAEA,WAAW,mBAAO,CAAC,EAAY;;AAE/B;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,YAAY,OAAO;AACnB,YAAY,OAAO;AACnB,YAAY,OAAO;AACnB;;;AAGA;AACA;AACA;AACA,uFAAuF;AACvF;AACA;AACA;AACA,6DAA6D;AAC7D;AACA,2DAA2D;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO;;AAEP;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,gBAAgB,OAAO;AACvB;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;;AAEA;AACA;AACA,cAAc,OAAO;AACrB;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,cAAc,OAAO;AACrB;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED,wB;;;;;;ACrUA,kBAAkB,YAAY,mBAAO,CAAC,GAAkC,sB;;;;;;ACAxE,mBAAO,CAAC,GAAiC;AACzC,iBAAiB,mBAAO,CAAC,CAAqB;;;;;;;ACD9C;AACA,cAAc,mBAAO,CAAC,CAAW;;AAEjC,0CAA0C,SAAS,mBAAO,CAAC,GAAkB,GAAG;;;;;;;;ACHnE;AACb;AACA,kBAAkB,mBAAO,CAAC,CAAgB;AAC1C,cAAc,mBAAO,CAAC,EAAgB;AACtC,WAAW,mBAAO,CAAC,EAAgB;AACnC,UAAU,mBAAO,CAAC,EAAe;AACjC,eAAe,mBAAO,CAAC,EAAc;AACrC,cAAc,mBAAO,CAAC,EAAY;AAClC;;AAEA;AACA,6BAA6B,mBAAO,CAAC,EAAU;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,UAAU,EAAE;AAChD,mBAAmB,sCAAsC;AACzD,CAAC,qCAAqC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;;;;;;;ACrCD,mBAAO,CAAC,EAAiC;AACzC,mBAAO,CAAC,EAAgC;AACxC,mBAAO,CAAC,EAA6B;AACrC,mBAAO,CAAC,GAAwB;AAChC,mBAAO,CAAC,GAAgC;AACxC,mBAAO,CAAC,GAA4B;AACpC,iBAAiB,mBAAO,CAAC,CAAkB;;;;;;;;ACN9B;AACb,cAAc,mBAAO,CAAC,EAAY;AAClC,aAAa,mBAAO,CAAC,CAAW;AAChC,UAAU,mBAAO,CAAC,EAAQ;AAC1B,cAAc,mBAAO,CAAC,EAAY;AAClC,cAAc,mBAAO,CAAC,CAAW;AACjC,eAAe,mBAAO,CAAC,CAAc;AACrC,gBAAgB,mBAAO,CAAC,EAAe;AACvC,iBAAiB,mBAAO,CAAC,GAAgB;AACzC,YAAY,mBAAO,CAAC,GAAW;AAC/B,yBAAyB,mBAAO,CAAC,EAAwB;AACzD,WAAW,mBAAO,CAAC,EAAS;AAC5B,gBAAgB,mBAAO,CAAC,GAAc;AACtC,iCAAiC,mBAAO,CAAC,EAA2B;AACpE,cAAc,mBAAO,CAAC,EAAY;AAClC,gBAAgB,mBAAO,CAAC,GAAe;AACvC,qBAAqB,mBAAO,CAAC,EAAoB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;;AAEA;AACA;AACA;AACA;AACA,+CAA+C,EAAE,mBAAO,CAAC,CAAQ;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,YAAY;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,WAAW;AACX,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,mBAAmB,kCAAkC;AACrD,SAAS;AACT;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,eAAe,uCAAuC;AACtD;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA,uBAAuB,0BAA0B;AACjD;AACA;AACA,SAAS;AACT;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH,kBAAkB,yBAAyB,KAAK;AAChD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,wBAAwB;AACxB,gBAAgB;AAChB,oBAAoB;AACpB,wBAAwB;AACxB,gBAAgB;AAChB,oBAAoB;AACpB;AACA,uBAAuB,mBAAO,CAAC,GAAiB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0DAA0D,oBAAoB;AAC9E,mBAAO,CAAC,EAAsB;AAC9B,mBAAO,CAAC,GAAgB;AACxB,UAAU,mBAAO,CAAC,CAAS;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gDAAgD,mBAAO,CAAC,EAAgB;AACxE;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,CAAC;;;;;;;AC7RD;AACA;AACA;AACA,GAAG;AACH;;;;;;;ACJA,UAAU,mBAAO,CAAC,EAAQ;AAC1B,WAAW,mBAAO,CAAC,EAAc;AACjC,kBAAkB,mBAAO,CAAC,EAAkB;AAC5C,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,EAAc;AACrC,gBAAgB,mBAAO,CAAC,EAA4B;AACpD;AACA;AACA;AACA,uCAAuC,iBAAiB,EAAE;AAC1D;AACA;AACA;AACA;AACA;AACA,mEAAmE,gBAAgB;AACnF;AACA;AACA,GAAG,4CAA4C,gCAAgC;AAC/E;AACA;AACA;AACA;AACA;AACA;;;;;;;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;ACfA,aAAa,mBAAO,CAAC,CAAW;AAChC,gBAAgB,mBAAO,CAAC,EAAS;AACjC;AACA;AACA;AACA,aAAa,mBAAO,CAAC,EAAQ;;AAE7B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,uCAAuC,sBAAsB,EAAE;AAC/D;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;;;;;;ACpEA,aAAa,mBAAO,CAAC,CAAW;AAChC;;AAEA;;;;;;;ACHA,WAAW,mBAAO,CAAC,CAAS;AAC5B;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;ACNa;AACb,aAAa,mBAAO,CAAC,CAAW;AAChC,WAAW,mBAAO,CAAC,CAAS;AAC5B,SAAS,mBAAO,CAAC,CAAc;AAC/B,kBAAkB,mBAAO,CAAC,CAAgB;AAC1C,cAAc,mBAAO,CAAC,CAAQ;;AAE9B;AACA;AACA;AACA;AACA,sBAAsB,aAAa;AACnC,GAAG;AACH;;;;;;;;ACbA;AACa;AACb,cAAc,mBAAO,CAAC,CAAW;AACjC,WAAW,mBAAO,CAAC,CAAS;AAC5B,aAAa,mBAAO,CAAC,CAAW;AAChC,yBAAyB,mBAAO,CAAC,EAAwB;AACzD,qBAAqB,mBAAO,CAAC,EAAoB;;AAEjD,2CAA2C;AAC3C;AACA;AACA;AACA;AACA,8DAA8D,UAAU,EAAE;AAC1E,KAAK;AACL;AACA,8DAA8D,SAAS,EAAE;AACzE,KAAK;AACL;AACA,CAAC,EAAE;;;;;;;;ACnBU;AACb;AACA,cAAc,mBAAO,CAAC,CAAW;AACjC,2BAA2B,mBAAO,CAAC,EAA2B;AAC9D,cAAc,mBAAO,CAAC,EAAY;;AAElC,+BAA+B;AAC/B;AACA;AACA;AACA;AACA,CAAC,EAAE;;;;;;;ACXH,mBAAO,CAAC,GAA2C;AACnD,iBAAiB,mBAAO,CAAC,CAAqB;;;;;;;ACD9C;AACA,eAAe,mBAAO,CAAC,EAAc;AACrC,sBAAsB,mBAAO,CAAC,EAAe;;AAE7C,mBAAO,CAAC,EAAe;AACvB;AACA;AACA;AACA,CAAC;;;;;;;ACRD,kBAAkB,YAAY,mBAAO,CAAC,GAAoC,sB;;;;;;ACA1E,mBAAO,CAAC,EAAmC;AAC3C,mBAAO,CAAC,EAAgC;AACxC,iBAAiB,mBAAO,CAAC,EAAwB;;;;;;;ACFjD,kBAAkB,YAAY,mBAAO,CAAC,GAA2B,sB;;;;;;;;;ACAjEvB,mBAAOA,CAAC,GAAR;AACAA,mBAAOA,CAAC,EAAR;AACAA,mBAAOA,CAAC,GAAR;AACAA,mBAAOA,CAAC,GAAR;AACAqF,OAAOxF,OAAP,GAAiBG,mBAAOA,CAAC,CAAR,EAA+BuF,MAAhD,C;;;;;;;ACJa;AACb;AACA,aAAa,mBAAO,CAAC,CAAW;AAChC,UAAU,mBAAO,CAAC,EAAQ;AAC1B,kBAAkB,mBAAO,CAAC,CAAgB;AAC1C,cAAc,mBAAO,CAAC,CAAW;AACjC,eAAe,mBAAO,CAAC,EAAa;AACpC,WAAW,mBAAO,CAAC,EAAS;AAC5B,aAAa,mBAAO,CAAC,EAAU;AAC/B,aAAa,mBAAO,CAAC,EAAW;AAChC,qBAAqB,mBAAO,CAAC,EAAsB;AACnD,UAAU,mBAAO,CAAC,EAAQ;AAC1B,UAAU,mBAAO,CAAC,CAAQ;AAC1B,aAAa,mBAAO,CAAC,EAAY;AACjC,gBAAgB,mBAAO,CAAC,EAAe;AACvC,eAAe,mBAAO,CAAC,GAAc;AACrC,cAAc,mBAAO,CAAC,GAAa;AACnC,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,EAAc;AACrC,gBAAgB,mBAAO,CAAC,CAAe;AACvC,kBAAkB,mBAAO,CAAC,EAAiB;AAC3C,iBAAiB,mBAAO,CAAC,EAAkB;AAC3C,cAAc,mBAAO,CAAC,EAAkB;AACxC,cAAc,mBAAO,CAAC,GAAoB;AAC1C,YAAY,mBAAO,CAAC,EAAgB;AACpC,YAAY,mBAAO,CAAC,EAAgB;AACpC,UAAU,mBAAO,CAAC,CAAc;AAChC,YAAY,mBAAO,CAAC,EAAgB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,sBAAsB;AACtB,sBAAsB,uBAAuB,WAAW,IAAI;AAC5D,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA,KAAK;AACL;AACA,sBAAsB,mCAAmC;AACzD,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE,gCAAgC;AAChG;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,EAAE,mBAAO,CAAC,EAAgB;AAC1B,EAAE,mBAAO,CAAC,EAAe;AACzB;;AAEA,sBAAsB,mBAAO,CAAC,EAAY;AAC1C;AACA;;AAEA;AACA;AACA;AACA;;AAEA,0DAA0D,kBAAkB;;AAE5E;AACA;AACA;AACA,oBAAoB,uBAAuB;;AAE3C,oDAAoD,6BAA6B;;AAEjF;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH,0BAA0B,eAAe,EAAE;AAC3C,0BAA0B,gBAAgB;AAC1C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA,8CAA8C,YAAY,EAAE;;AAE5D;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,OAAO,QAAQ,iCAAiC;AACpG,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,oCAAoC,mBAAO,CAAC,CAAS;AACrD;AACA;AACA;AACA;AACA;AACA;;;;;;;ACrPA;AACA,cAAc,mBAAO,CAAC,EAAgB;AACtC,WAAW,mBAAO,CAAC,EAAgB;AACnC,UAAU,mBAAO,CAAC,EAAe;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;ACdA;AACA,UAAU,mBAAO,CAAC,EAAQ;AAC1B;AACA;AACA;;;;;;;ACJA;AACA,gBAAgB,mBAAO,CAAC,CAAe;AACvC,WAAW,mBAAO,CAAC,EAAgB;AACnC,iBAAiB;;AAEjB;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;;;;;;AClBA,mBAAO,CAAC,EAAe;;;;;;;ACAvB,mBAAO,CAAC,EAAe;;;;;;;ACAvB,kBAAkB,YAAY,mBAAO,CAAC,GAA4C,sB;;;;;;ACAlF,mBAAO,CAAC,GAA2C;AACnD,iBAAiB,mBAAO,CAAC,CAAqB;;;;;;;ACD9C;AACA,cAAc,mBAAO,CAAC,CAAW;AACjC,8BAA8B,iBAAiB,mBAAO,CAAC,GAAc,OAAO;;;;;;;ACF5E;AACA;AACA,eAAe,mBAAO,CAAC,CAAc;AACrC,eAAe,mBAAO,CAAC,CAAc;AACrC;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA,cAAc,mBAAO,CAAC,EAAQ,iBAAiB,mBAAO,CAAC,EAAgB;AACvE;AACA;AACA,OAAO,YAAY,cAAc;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;AACR;AACA;;;;;;;ACxBA,kBAAkB,YAAY,mBAAO,CAAC,GAAkC,sB;;;;;;ACAxE,mBAAO,CAAC,GAAiC;AACzC,cAAc,mBAAO,CAAC,CAAqB;AAC3C;AACA;AACA;;;;;;;ACJA,cAAc,mBAAO,CAAC,CAAW;AACjC;AACA,8BAA8B,SAAS,mBAAO,CAAC,EAAkB,GAAG;;;;;;;ACFpE;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,qCAAqC;;AAErC;AACA;AACA;;AAEA,2BAA2B;AAC3B;AACA;AACA;AACA,4BAA4B,UAAU;;;;;;;ACvLtC,mBAAO,CAAC,GAAiC;AACzC,iBAAiB,mBAAO,CAAC,CAAqB;;;;;;;ACD9C;AACA,eAAe,mBAAO,CAAC,CAAc;AACrC,WAAW,mBAAO,CAAC,EAAS;;AAE5B,mBAAO,CAAC,EAAe;AACvB;AACA;AACA;AACA,CAAC;;;;;;;;;;ACRD,IAAIC,KAAKxF,mBAAOA,CAAC,GAAR,CAAT;AACA,IAAIwC,KAAKxC,mBAAOA,CAAC,GAAR,CAAT;;AAEA,IAAIyF,OAAOjD,EAAX;AACAiD,KAAKD,EAAL,GAAUA,EAAV;AACAC,KAAKjD,EAAL,GAAUA,EAAV;;AAEA6C,OAAOxF,OAAP,GAAiB4F,IAAjB,C;;;;;;ACPA,UAAU,mBAAO,CAAC,EAAW;AAC7B,kBAAkB,mBAAO,CAAC,EAAmB;;AAE7C;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,mCAAmC;AACnC;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,iBAAiB,OAAO;AACxB;AACA;;AAEA;AACA;;AAEA;;;;;;;AC5GA,UAAU,mBAAO,CAAC,EAAW;AAC7B,kBAAkB,mBAAO,CAAC,EAAmB;;AAE7C;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,oBAAoB,SAAS;AAC7B;AACA;AACA;;AAEA;AACA;;AAEA;;;;;;;;AC5Ba;;AAEb,IAAI1F,UAAUC,mBAAOA,CAAC,EAAR,CAAd;;AAEA,IAAIC,WAAWC,uBAAuBH,OAAvB,CAAf;;AAEA,SAASG,sBAAT,CAAgCa,GAAhC,EAAqC;AAAE,SAAOA,OAAOA,IAAIC,UAAX,GAAwBD,GAAxB,GAA8B,EAAEE,SAASF,GAAX,EAArC;AAAwD;;AAE/F;AACA,IAAI2E,cAAc,CAAC,GAAGzF,SAASgB,OAAb,EAAsB,EAAEY,SAAS,aAAX,EAA0BqB,MAAM,CAAC,KAAjC,EAAtB,CAAlB;AACA,IAAIyC,kBAAkB,CAAC,GAAG1F,SAASgB,OAAb,EAAsB,EAAEY,SAAS,iBAAX,EAA8BqB,MAAM,CAAC,KAArC,EAAtB,CAAtB;AACA,IAAIC,mBAAmB,CAAC,GAAGlD,SAASgB,OAAb,EAAsB,EAAEY,SAAS,kBAAX,EAA+BqB,MAAM,CAAC,KAAtC,EAAtB,CAAvB;AACA,IAAI0C,iBAAiB,CAAC,GAAG3F,SAASgB,OAAb,EAAsB,EAAEY,SAAS,gBAAX,EAA6BqB,MAAM,CAAC,KAApC,EAAtB,CAArB;AACA,IAAIK,iBAAiB,CAAC,GAAGtD,SAASgB,OAAb,EAAsB,EAAEY,SAAS,gBAAX,EAA6BqB,MAAM,CAAC,KAApC,EAAtB,CAArB;;AAEAmC,OAAOxF,OAAP,GAAiB,CAAC,GAAGI,SAASgB,OAAb,EAAsB;AACrCyE,eAAaA,WADwB;AAErCC,mBAAiBA,eAFoB;AAGrCxC,oBAAkBA,gBAHmB;AAIrCyC,kBAAgBA,cAJqB;AAKrCrC,kBAAgBA;AALqB,CAAtB,CAAjB,C;;;;;;;ACfa;;AAEb5D,OAAOC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAC3CC,SAAO;AADoC,CAA7C;;AAIA,IAAIiF,YAAY/E,mBAAOA,CAAC,GAAR,CAAhB;;AAEA,IAAIgF,aAAa9E,uBAAuB6E,SAAvB,CAAjB;;AAEA,SAAS7E,sBAAT,CAAgCa,GAAhC,EAAqC;AAAE,SAAOA,OAAOA,IAAIC,UAAX,GAAwBD,GAAxB,GAA8B,EAAEE,SAASF,GAAX,EAArC;AAAwD;;AAE/FlB,QAAQoB,OAAR,GAAkB,IAAI+D,WAAW/D,OAAf,EAAlB,C;;;;;;;ACZa;;AAEb;AACA;AACA,CAAC;;AAED,uBAAuB,mBAAO,CAAC,EAAsC;;AAErE;;AAEA,oBAAoB,mBAAO,CAAC,EAAmC;;AAE/D;;AAEA,mBAAmB,mBAAO,CAAC,GAAe;;AAE1C;;AAEA,cAAc,mBAAO,CAAC,EAAe;;AAErC;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED,2B;;;;;;;AC9Ea;;AAEb;AACA;AACA,CAAC;;AAED,0BAA0B,mBAAO,CAAC,EAAyC;;AAE3E;;AAEA,eAAe,mBAAO,CAAC,EAA+B;;AAEtD;;AAEA,sBAAsB,mBAAO,CAAC,EAA+C;;AAE7E;;AAEA,uBAAuB,mBAAO,CAAC,EAAsC;;AAErE;;AAEA,oBAAoB,mBAAO,CAAC,EAAmC;;AAE/D;;AAEA,kCAAkC,mBAAO,CAAC,EAAiD;;AAE3F;;AAEA,iBAAiB,mBAAO,CAAC,EAAgC;;AAEzD;;AAEA,uBAAuB,mBAAO,CAAC,EAAkB;;AAEjD;;AAEA,cAAc,mBAAO,CAAC,GAAe;;AAErC,cAAc,mBAAO,CAAC,EAAQ;;AAE9B,WAAW,mBAAO,CAAC,EAAY;;AAE/B;;AAEA,cAAc,mBAAO,CAAC,EAAe;;AAErC;;AAEA,iBAAiB,mBAAO,CAAC,GAAkB;;AAE3C,wBAAwB,mBAAO,CAAC,GAAmB;;AAEnD;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,WAAW;AACX,2CAA2C,wEAAwE;;AAEnH;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA,eAAe,OAAO;AACtB;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;;AAEX;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc,OAAO;AACrB;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA,gBAAgB,OAAO;AACvB;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,gBAAgB,OAAO;AACvB;;AAEA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA,SAAS;AACT;;AAEA;AACA,SAAS;AACT;AACA;;AAEA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA,cAAc,OAAO;AACrB;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc,OAAO;AACrB;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,WAAW;AACX;AACA;AACA;;AAEA;AACA;AACA,cAAc,OAAO;AACrB;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,gDAAgD,4BAA4B;;AAE5E;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,gBAAgB,OAAO;AACvB;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED,8B;;;;;;ACnYA,kBAAkB,YAAY,mBAAO,CAAC,GAA+B,sB;;;;;;ACArE,mBAAO,CAAC,EAAmC;AAC3C,mBAAO,CAAC,GAA8B;AACtC,iBAAiB,mBAAO,CAAC,CAAqB;;;;;;;;ACFjC;AACb,UAAU,mBAAO,CAAC,EAAQ;AAC1B,cAAc,mBAAO,CAAC,CAAW;AACjC,eAAe,mBAAO,CAAC,EAAc;AACrC,WAAW,mBAAO,CAAC,EAAc;AACjC,kBAAkB,mBAAO,CAAC,EAAkB;AAC5C,eAAe,mBAAO,CAAC,EAAc;AACrC,qBAAqB,mBAAO,CAAC,GAAoB;AACjD,gBAAgB,mBAAO,CAAC,EAA4B;;AAEpD,iCAAiC,mBAAO,CAAC,EAAgB,mBAAmB,kBAAkB,EAAE;AAChG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,gCAAgC;AACvF;AACA;AACA,KAAK;AACL;AACA,kCAAkC,gBAAgB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;ACpCY;AACb,sBAAsB,mBAAO,CAAC,CAAc;AAC5C,iBAAiB,mBAAO,CAAC,EAAkB;;AAE3C;AACA;AACA;AACA;;;;;;;;ACPa;;AAEb;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,4C;;;;;;;ACpBa;;AAEb;AACA;AACA,CAAC;;AAED,0BAA0B,mBAAO,CAAC,EAAyC;;AAE3E;;AAEA;AACA;AACA;AACA;;AAEA,cAAc,mBAAO,CAAC,EAAU;;AAEhC;;AAEA,sCAAsC,uCAAuC,gBAAgB;;AAE7F;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,C;;;;;;;;;AC9JA,IAAI4E,mBAAmBlC,OAAOkC,gBAAP,IAClBlC,OAAOmC,sBADW,IAElBnC,OAAOoC,mBAFZ;;AAIA;;;;;;AAMA,IAAIC,UAAUrC,OAAOqC,OAArB;;AAEA,IAAI,OAAOA,OAAP,KAAmB,WAAvB,EAAoC;AAClC,MAAIpG,iBAAiBD,OAAOC,cAA5B;AACA,MAAIqG,UAAUC,KAAKC,GAAL,KAAa,GAA3B;;AAEAH,YAAU,mBAAW;AACnB,SAAKrB,IAAL,GAAY,UAAUQ,KAAKiB,MAAL,KAAgB,GAAhB,KAAwB,CAAlC,KAAwCH,YAAY,IAApD,CAAZ;AACD,GAFD;;AAIAD,UAAQK,SAAR,GAAoB;AAClBC,SAAK,aAAS3E,GAAT,EAAc7B,KAAd,EAAqB;AACxB,UAAIyG,QAAQ5E,IAAI,KAAKgD,IAAT,CAAZ;AACA,UAAI4B,SAASA,MAAM,CAAN,MAAa5E,GAA1B,EACE4E,MAAM,CAAN,IAAWzG,KAAX,CADF,KAGEF,eAAe+B,GAAf,EAAoB,KAAKgD,IAAzB,EAA+B,EAAC7E,OAAO,CAAC6B,GAAD,EAAM7B,KAAN,CAAR,EAAsB0G,UAAU,IAAhC,EAA/B;AACF,aAAO,IAAP;AACD,KARiB;AASlBC,SAAK,aAAS9E,GAAT,EAAc;AACjB,UAAI4E,KAAJ;AACA,aAAO,CAACA,QAAQ5E,IAAI,KAAKgD,IAAT,CAAT,KAA4B4B,MAAM,CAAN,MAAa5E,GAAzC,GACH4E,MAAM,CAAN,CADG,GACQ/E,SADf;AAED,KAbiB;AAclB,cAAU,iBAASG,GAAT,EAAc;AACtB,UAAI4E,QAAQ5E,IAAI,KAAKgD,IAAT,CAAZ;AACA,UAAI,CAAC4B,KAAL,EAAY,OAAO,KAAP;AACZ,UAAIG,WAAWH,MAAM,CAAN,MAAa5E,GAA5B;AACA4E,YAAM,CAAN,IAAWA,MAAM,CAAN,IAAW/E,SAAtB;AACA,aAAOkF,QAAP;AACD,KApBiB;AAqBlBC,SAAK,aAAShF,GAAT,EAAc;AACjB,UAAI4E,QAAQ5E,IAAI,KAAKgD,IAAT,CAAZ;AACA,UAAI,CAAC4B,KAAL,EAAY,OAAO,KAAP;AACZ,aAAOA,MAAM,CAAN,MAAa5E,GAApB;AACD;AAzBiB,GAApB;AA2BD;;AAED,IAAIiF,qBAAqB,IAAIZ,OAAJ,EAAzB;;AAEA;AACA,IAAIa,eAAelD,OAAOmD,cAA1B;;AAEA;AACA,IAAI,CAACD,YAAL,EAAmB;AACjB,MAAIE,oBAAoB,EAAxB;AACA,MAAIC,WAAWC,OAAO9B,KAAKiB,MAAL,EAAP,CAAf;AACAzC,SAAOuD,gBAAP,CAAwB,SAAxB,EAAmC,UAASC,CAAT,EAAY;AAC7C,QAAIA,EAAErF,IAAF,KAAWkF,QAAf,EAAyB;AACvB,UAAII,QAAQL,iBAAZ;AACAA,0BAAoB,EAApB;AACAK,YAAMC,OAAN,CAAc,UAASC,IAAT,EAAe;AAC3BA;AACD,OAFD;AAGD;AACF,GARD;AASAT,iBAAe,sBAASS,IAAT,EAAe;AAC5BP,sBAAkBQ,IAAlB,CAAuBD,IAAvB;AACA3D,WAAO6D,WAAP,CAAmBR,QAAnB,EAA6B,GAA7B;AACD,GAHD;AAID;;AAED;AACA,IAAIS,cAAc,KAAlB;;AAEA;AACA,IAAIC,qBAAqB,EAAzB;;AAEA;;;;AAIA,SAASC,gBAAT,CAA0BC,QAA1B,EAAoC;AAClCF,qBAAmBH,IAAnB,CAAwBK,QAAxB;AACA,MAAI,CAACH,WAAL,EAAkB;AAChBA,kBAAc,IAAd;AACAZ,iBAAagB,iBAAb;AACD;AACF;;AAED,SAASC,YAAT,CAAsBC,IAAtB,EAA4B;AAC1B,SAAOpE,OAAOqE,iBAAP,IACHrE,OAAOqE,iBAAP,CAAyBF,YAAzB,CAAsCC,IAAtC,CADG,IAEHA,IAFJ;AAGD;;AAED,SAASF,iBAAT,GAA6B;AAC3B;;AAEAJ,gBAAc,KAAd,CAH2B,CAGN;;AAErB,MAAIQ,YAAYP,kBAAhB;AACAA,uBAAqB,EAArB;AACA;AACAO,YAAUC,IAAV,CAAe,UAASC,EAAT,EAAaC,EAAb,EAAiB;AAC9B,WAAOD,GAAGE,IAAH,GAAUD,GAAGC,IAApB;AACD,GAFD;;AAIA,MAAIC,cAAc,KAAlB;AACAL,YAAUZ,OAAV,CAAkB,UAASO,QAAT,EAAmB;;AAEnC;AACA,QAAIR,QAAQQ,SAASW,WAAT,EAAZ;AACA;AACAC,gCAA4BZ,QAA5B;;AAEA;AACA,QAAIR,MAAM7F,MAAV,EAAkB;AAChBqG,eAASa,SAAT,CAAmBrB,KAAnB,EAA0BQ,QAA1B;AACAU,oBAAc,IAAd;AACD;AACF,GAZD;;AAcA;AACA,MAAIA,WAAJ,EACET;AACH;;AAED,SAASW,2BAAT,CAAqCZ,QAArC,EAA+C;AAC7CA,WAASc,MAAT,CAAgBrB,OAAhB,CAAwB,UAASU,IAAT,EAAe;AACrC,QAAIY,gBAAgB/B,mBAAmBH,GAAnB,CAAuBsB,IAAvB,CAApB;AACA,QAAI,CAACY,aAAL,EACE;AACFA,kBAActB,OAAd,CAAsB,UAASuB,YAAT,EAAuB;AAC3C,UAAIA,aAAahB,QAAb,KAA0BA,QAA9B,EACEgB,aAAaC,wBAAb;AACH,KAHD;AAID,GARD;AASD;;AAED;;;;;;;;;;;;AAYA,SAASC,uCAAT,CAAiDC,MAAjD,EAAyDC,QAAzD,EAAmE;AACjE,OAAK,IAAIjB,OAAOgB,MAAhB,EAAwBhB,IAAxB,EAA8BA,OAAOA,KAAKkB,UAA1C,EAAsD;AACpD,QAAIN,gBAAgB/B,mBAAmBH,GAAnB,CAAuBsB,IAAvB,CAApB;;AAEA,QAAIY,aAAJ,EAAmB;AACjB,WAAK,IAAIO,IAAI,CAAb,EAAgBA,IAAIP,cAAcpH,MAAlC,EAA0C2H,GAA1C,EAA+C;AAC7C,YAAIN,eAAeD,cAAcO,CAAd,CAAnB;AACA,YAAIC,UAAUP,aAAaO,OAA3B;;AAEA;AACA,YAAIpB,SAASgB,MAAT,IAAmB,CAACI,QAAQC,OAAhC,EACE;;AAEF,YAAIC,SAASL,SAASG,OAAT,CAAb;AACA,YAAIE,MAAJ,EACET,aAAaU,OAAb,CAAqBD,MAArB;AACH;AACF;AACF;AACF;;AAED,IAAIE,aAAa,CAAjB;;AAEA;;;;;AAKA,SAASC,kBAAT,CAA4BR,QAA5B,EAAsC;AACpC,OAAKP,SAAL,GAAiBO,QAAjB;AACA,OAAKN,MAAL,GAAc,EAAd;AACA,OAAKe,QAAL,GAAgB,EAAhB;AACA,OAAKpB,IAAL,GAAY,EAAEkB,UAAd;AACD;;AAEDC,mBAAmBnD,SAAnB,GAA+B;AAC7BqD,WAAS,iBAASX,MAAT,EAAiBI,OAAjB,EAA0B;AACjCJ,aAASjB,aAAaiB,MAAb,CAAT;;AAEA;AACA,QAAI,CAACI,QAAQQ,SAAT,IAAsB,CAACR,QAAQS,UAA/B,IAA6C,CAACT,QAAQU,aAAtD;;AAEA;AACAV,YAAQW,iBAAR,IAA6B,CAACX,QAAQS,UAHtC;;AAKA;AACAT,YAAQY,eAAR,IAA2BZ,QAAQY,eAAR,CAAwBxI,MAAnD,IACI,CAAC4H,QAAQS,UAPb;;AASA;AACAT,YAAQa,qBAAR,IAAiC,CAACb,QAAQU,aAV9C,EAU6D;;AAE3D,YAAM,IAAII,WAAJ,EAAN;AACD;;AAED,QAAItB,gBAAgB/B,mBAAmBH,GAAnB,CAAuBsC,MAAvB,CAApB;AACA,QAAI,CAACJ,aAAL,EACE/B,mBAAmBN,GAAnB,CAAuByC,MAAvB,EAA+BJ,gBAAgB,EAA/C;;AAEF;AACA;AACA;AACA;AACA,QAAIC,YAAJ;AACA,SAAK,IAAIsB,IAAI,CAAb,EAAgBA,IAAIvB,cAAcpH,MAAlC,EAA0C2I,GAA1C,EAA+C;AAC7C,UAAIvB,cAAcuB,CAAd,EAAiBtC,QAAjB,KAA8B,IAAlC,EAAwC;AACtCgB,uBAAeD,cAAcuB,CAAd,CAAf;AACAtB,qBAAauB,eAAb;AACAvB,qBAAaO,OAAb,GAAuBA,OAAvB;AACA;AACD;AACF;;AAED;AACA;AACA;AACA;AACA;AACA,QAAI,CAACP,YAAL,EAAmB;AACjBA,qBAAe,IAAIwB,YAAJ,CAAiB,IAAjB,EAAuBrB,MAAvB,EAA+BI,OAA/B,CAAf;AACAR,oBAAcpB,IAAd,CAAmBqB,YAAnB;AACA,WAAKF,MAAL,CAAYnB,IAAZ,CAAiBwB,MAAjB;AACD;;AAEDH,iBAAayB,YAAb;AACD,GAlD4B;;AAoD7BC,cAAY,sBAAW;AACrB,SAAK5B,MAAL,CAAYrB,OAAZ,CAAoB,UAASU,IAAT,EAAe;AACjC,UAAIY,gBAAgB/B,mBAAmBH,GAAnB,CAAuBsB,IAAvB,CAApB;AACA,WAAK,IAAImC,IAAI,CAAb,EAAgBA,IAAIvB,cAAcpH,MAAlC,EAA0C2I,GAA1C,EAA+C;AAC7C,YAAItB,eAAeD,cAAcuB,CAAd,CAAnB;AACA,YAAItB,aAAahB,QAAb,KAA0B,IAA9B,EAAoC;AAClCgB,uBAAauB,eAAb;AACAxB,wBAAc4B,MAAd,CAAqBL,CAArB,EAAwB,CAAxB;AACA;AACA;AACA;AACD;AACF;AACF,KAZD,EAYG,IAZH;AAaA,SAAKT,QAAL,GAAgB,EAAhB;AACD,GAnE4B;;AAqE7BlB,eAAa,uBAAW;AACtB,QAAIiC,gBAAgB,KAAKf,QAAzB;AACA,SAAKA,QAAL,GAAgB,EAAhB;AACA,WAAOe,aAAP;AACD;AAzE4B,CAA/B;;AA4EA;;;;;AAKA,SAASC,cAAT,CAAwBC,IAAxB,EAA8B3B,MAA9B,EAAsC;AACpC,OAAK2B,IAAL,GAAYA,IAAZ;AACA,OAAK3B,MAAL,GAAcA,MAAd;AACA,OAAK4B,UAAL,GAAkB,EAAlB;AACA,OAAKC,YAAL,GAAoB,EAApB;AACA,OAAKC,eAAL,GAAuB,IAAvB;AACA,OAAKC,WAAL,GAAmB,IAAnB;AACA,OAAKC,aAAL,GAAqB,IAArB;AACA,OAAKC,kBAAL,GAA0B,IAA1B;AACA,OAAKC,QAAL,GAAgB,IAAhB;AACD;;AAED,SAASC,kBAAT,CAA4BC,QAA5B,EAAsC;AACpC,MAAI9B,SAAS,IAAIoB,cAAJ,CAAmBU,SAAST,IAA5B,EAAkCS,SAASpC,MAA3C,CAAb;AACAM,SAAOsB,UAAP,GAAoBQ,SAASR,UAAT,CAAoBS,KAApB,EAApB;AACA/B,SAAOuB,YAAP,GAAsBO,SAASP,YAAT,CAAsBQ,KAAtB,EAAtB;AACA/B,SAAOwB,eAAP,GAAyBM,SAASN,eAAlC;AACAxB,SAAOyB,WAAP,GAAqBK,SAASL,WAA9B;AACAzB,SAAO0B,aAAP,GAAuBI,SAASJ,aAAhC;AACA1B,SAAO2B,kBAAP,GAA4BG,SAASH,kBAArC;AACA3B,SAAO4B,QAAP,GAAkBE,SAASF,QAA3B;AACA,SAAO5B,MAAP;AACD;;AAED;AACA,IAAIgC,aAAJ,EAAmBC,kBAAnB;;AAEA;;;;;;AAMA,SAASC,SAAT,CAAmBb,IAAnB,EAAyB3B,MAAzB,EAAiC;AAC/B,SAAOsC,gBAAgB,IAAIZ,cAAJ,CAAmBC,IAAnB,EAAyB3B,MAAzB,CAAvB;AACD;;AAED;;;;;AAKA,SAASyC,qBAAT,CAA+BP,QAA/B,EAAyC;AACvC,MAAIK,kBAAJ,EACE,OAAOA,kBAAP;AACFA,uBAAqBJ,mBAAmBG,aAAnB,CAArB;AACAC,qBAAmBL,QAAnB,GAA8BA,QAA9B;AACA,SAAOK,kBAAP;AACD;;AAED,SAASG,YAAT,GAAwB;AACtBJ,kBAAgBC,qBAAqB9J,SAArC;AACD;;AAED;;;;;AAKA,SAASkK,+BAAT,CAAyCrC,MAAzC,EAAiD;AAC/C,SAAOA,WAAWiC,kBAAX,IAAiCjC,WAAWgC,aAAnD;AACD;;AAED;;;;;;;;AAQA,SAASM,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6C;AAC3C,MAAID,eAAeC,SAAnB,EACE,OAAOD,UAAP;;AAEF;AACA;AACA,MAAIN,sBAAsBI,gCAAgCE,UAAhC,CAA1B,EACE,OAAON,kBAAP;;AAEF,SAAO,IAAP;AACD;;AAED;;;;;;;AAOA,SAASlB,YAAT,CAAsBxC,QAAtB,EAAgCmB,MAAhC,EAAwCI,OAAxC,EAAiD;AAC/C,OAAKvB,QAAL,GAAgBA,QAAhB;AACA,OAAKmB,MAAL,GAAcA,MAAd;AACA,OAAKI,OAAL,GAAeA,OAAf;AACA,OAAK2C,sBAAL,GAA8B,EAA9B;AACD;;AAED1B,aAAa/D,SAAb,GAAyB;AACvBiD,WAAS,iBAASD,MAAT,EAAiB;AACxB,QAAI0C,UAAU,KAAKnE,QAAL,CAAc6B,QAA5B;AACA,QAAIlI,SAASwK,QAAQxK,MAArB;;AAEA;AACA;AACA;AACA;AACA,QAAIwK,QAAQxK,MAAR,GAAiB,CAArB,EAAwB;AACtB,UAAIqK,aAAaG,QAAQxK,SAAS,CAAjB,CAAjB;AACA,UAAIyK,sBAAsBL,aAAaC,UAAb,EAAyBvC,MAAzB,CAA1B;AACA,UAAI2C,mBAAJ,EAAyB;AACvBD,gBAAQxK,SAAS,CAAjB,IAAsByK,mBAAtB;AACA;AACD;AACF,KAPD,MAOO;AACLrE,uBAAiB,KAAKC,QAAtB;AACD;;AAEDmE,YAAQxK,MAAR,IAAkB8H,MAAlB;AACD,GArBsB;;AAuBvBgB,gBAAc,wBAAW;AACvB,SAAK4B,aAAL,CAAmB,KAAKlD,MAAxB;AACD,GAzBsB;;AA2BvBkD,iBAAe,uBAASlE,IAAT,EAAe;AAC5B,QAAIoB,UAAU,KAAKA,OAAnB;AACA,QAAIA,QAAQS,UAAZ,EACE7B,KAAKb,gBAAL,CAAsB,iBAAtB,EAAyC,IAAzC,EAA+C,IAA/C;;AAEF,QAAIiC,QAAQU,aAAZ,EACE9B,KAAKb,gBAAL,CAAsB,0BAAtB,EAAkD,IAAlD,EAAwD,IAAxD;;AAEF,QAAIiC,QAAQQ,SAAZ,EACE5B,KAAKb,gBAAL,CAAsB,iBAAtB,EAAyC,IAAzC,EAA+C,IAA/C;;AAEF,QAAIiC,QAAQQ,SAAR,IAAqBR,QAAQC,OAAjC,EACErB,KAAKb,gBAAL,CAAsB,gBAAtB,EAAwC,IAAxC,EAA8C,IAA9C;AACH,GAxCsB;;AA0CvBiD,mBAAiB,2BAAW;AAC1B,SAAK+B,gBAAL,CAAsB,KAAKnD,MAA3B;AACD,GA5CsB;;AA8CvBmD,oBAAkB,0BAASnE,IAAT,EAAe;AAC/B,QAAIoB,UAAU,KAAKA,OAAnB;AACA,QAAIA,QAAQS,UAAZ,EACE7B,KAAKoE,mBAAL,CAAyB,iBAAzB,EAA4C,IAA5C,EAAkD,IAAlD;;AAEF,QAAIhD,QAAQU,aAAZ,EACE9B,KAAKoE,mBAAL,CAAyB,0BAAzB,EAAqD,IAArD,EAA2D,IAA3D;;AAEF,QAAIhD,QAAQQ,SAAZ,EACE5B,KAAKoE,mBAAL,CAAyB,iBAAzB,EAA4C,IAA5C,EAAkD,IAAlD;;AAEF,QAAIhD,QAAQQ,SAAR,IAAqBR,QAAQC,OAAjC,EACErB,KAAKoE,mBAAL,CAAyB,gBAAzB,EAA2C,IAA3C,EAAiD,IAAjD;AACH,GA3DsB;;AA6DvB;;;;;AAKAC,wBAAsB,8BAASrE,IAAT,EAAe;AACnC;AACA;AACA,QAAIA,SAAS,KAAKgB,MAAlB,EACE;;AAEF,SAAKkD,aAAL,CAAmBlE,IAAnB;AACA,SAAK+D,sBAAL,CAA4BvE,IAA5B,CAAiCQ,IAAjC;AACA,QAAIY,gBAAgB/B,mBAAmBH,GAAnB,CAAuBsB,IAAvB,CAApB;AACA,QAAI,CAACY,aAAL,EACE/B,mBAAmBN,GAAnB,CAAuByB,IAAvB,EAA6BY,gBAAgB,EAA7C;;AAEF;AACA;AACAA,kBAAcpB,IAAd,CAAmB,IAAnB;AACD,GAjFsB;;AAmFvBsB,4BAA0B,oCAAW;AACnC,QAAIiD,yBAAyB,KAAKA,sBAAlC;AACA,SAAKA,sBAAL,GAA8B,EAA9B;;AAEAA,2BAAuBzE,OAAvB,CAA+B,UAASU,IAAT,EAAe;AAC5C;AACA,WAAKmE,gBAAL,CAAsBnE,IAAtB;;AAEA,UAAIY,gBAAgB/B,mBAAmBH,GAAnB,CAAuBsB,IAAvB,CAApB;AACA,WAAK,IAAImC,IAAI,CAAb,EAAgBA,IAAIvB,cAAcpH,MAAlC,EAA0C2I,GAA1C,EAA+C;AAC7C,YAAIvB,cAAcuB,CAAd,MAAqB,IAAzB,EAA+B;AAC7BvB,wBAAc4B,MAAd,CAAqBL,CAArB,EAAwB,CAAxB;AACA;AACA;AACA;AACD;AACF;AACF,KAbD,EAaG,IAbH;AAcD,GArGsB;;AAuGvBmC,eAAa,qBAASlF,CAAT,EAAY;AACvB;AACA;AACA;AACAA,MAAEmF,wBAAF;;AAEA,YAAQnF,EAAEuD,IAAV;AACE,WAAK,iBAAL;AACE;;AAEA,YAAI/F,OAAOwC,EAAEoF,QAAb;AACA,YAAIC,YAAYrF,EAAEsF,WAAF,CAAcC,YAA9B;AACA,YAAI3D,SAAS5B,EAAE4B,MAAf;;AAEA;AACA,YAAIM,SAAS,IAAIkC,SAAJ,CAAc,YAAd,EAA4BxC,MAA5B,CAAb;AACAM,eAAO0B,aAAP,GAAuBpG,IAAvB;AACA0E,eAAO2B,kBAAP,GAA4BwB,SAA5B;;AAEA;AACA,YAAIvB,WAAW,IAAf;AACA,YAAI,EAAE,OAAO0B,aAAP,KAAyB,WAAzB,IAAwCxF,EAAEyF,UAAF,KAAiBD,cAAcE,QAAzE,CAAJ,EACE5B,WAAW9D,EAAE2F,SAAb;;AAEFhE,gDAAwCC,MAAxC,EAAgD,UAASI,OAAT,EAAkB;AAChE;AACA,cAAI,CAACA,QAAQS,UAAb,EACE;;AAEF;AACA,cAAIT,QAAQY,eAAR,IAA2BZ,QAAQY,eAAR,CAAwBxI,MAAnD,IACA4H,QAAQY,eAAR,CAAwBgD,OAAxB,CAAgCpI,IAAhC,MAA0C,CAAC,CAD3C,IAEAwE,QAAQY,eAAR,CAAwBgD,OAAxB,CAAgCP,SAAhC,MAA+C,CAAC,CAFpD,EAEuD;AACrD;AACD;AACD;AACA,cAAIrD,QAAQW,iBAAZ,EACE,OAAO0B,sBAAsBP,QAAtB,CAAP;;AAEF;AACA,iBAAO5B,MAAP;AACD,SAjBD;;AAmBA;;AAEF,WAAK,0BAAL;AACE;AACA,YAAIN,SAAS5B,EAAE4B,MAAf;;AAEA;AACA,YAAIM,SAASkC,UAAU,eAAV,EAA2BxC,MAA3B,CAAb;;AAEA;AACA,YAAIkC,WAAW9D,EAAE2F,SAAjB;;AAGAhE,gDAAwCC,MAAxC,EAAgD,UAASI,OAAT,EAAkB;AAChE;AACA,cAAI,CAACA,QAAQU,aAAb,EACE;;AAEF;AACA,cAAIV,QAAQa,qBAAZ,EACE,OAAOwB,sBAAsBP,QAAtB,CAAP;;AAEF;AACA,iBAAO5B,MAAP;AACD,SAXD;;AAaA;;AAEF,WAAK,gBAAL;AACE,aAAK+C,oBAAL,CAA0BjF,EAAE4B,MAA5B;AACA;AACF,WAAK,iBAAL;AACE;AACA,YAAIA,SAAS5B,EAAEsF,WAAf;AACA,YAAIO,cAAc7F,EAAE4B,MAApB;AACA,YAAI4B,UAAJ,EAAgBC,YAAhB;AACA,YAAIzD,EAAEuD,IAAF,KAAW,iBAAf,EAAkC;AAChCC,uBAAa,CAACqC,WAAD,CAAb;AACApC,yBAAe,EAAf;AACD,SAHD,MAGO;;AAELD,uBAAa,EAAb;AACAC,yBAAe,CAACoC,WAAD,CAAf;AACD;AACD,YAAInC,kBAAkBmC,YAAYnC,eAAlC;AACA,YAAIC,cAAckC,YAAYlC,WAA9B;;AAEA;AACA,YAAIzB,SAASkC,UAAU,WAAV,EAAuBxC,MAAvB,CAAb;AACAM,eAAOsB,UAAP,GAAoBA,UAApB;AACAtB,eAAOuB,YAAP,GAAsBA,YAAtB;AACAvB,eAAOwB,eAAP,GAAyBA,eAAzB;AACAxB,eAAOyB,WAAP,GAAqBA,WAArB;;AAEAhC,gDAAwCC,MAAxC,EAAgD,UAASI,OAAT,EAAkB;AAChE;AACA,cAAI,CAACA,QAAQQ,SAAb,EACE;;AAEF;AACA,iBAAON,MAAP;AACD,SAPD;;AA3FJ;;AAsGAoC;AACD;AApNsB,CAAzB;;AAuNA,IAAI,CAAC5F,gBAAL,EAAuB;AACrBA,qBAAmB2D,kBAAnB;AACD;;AAEDnE,OAAOxF,OAAP,GAAiBgG,gBAAjB,C;;;;;;;;;;;;ACzkBA;;AAEA;;;AAGA,IAAM/B,eAAe;AACnB;;;;;AAKAC,YAAU,kBAACkJ,wBAAD,EAA8B;AACtC,QAAMC,SAASvJ,OAAOwJ,SAAP,CAAiBC,SAAjB,CAA2BL,OAA3B,CAAmC,KAAnC,MAA8C,CAAC,CAA9D;AACA,QAAIpJ,OAAO0J,IAAP,KAAgB1J,OAAO2J,GAAvB,IAA8BJ,MAAlC,EAA0C;AACxC;AACAvJ,aAAO4J,OAAP,GAAiB,UAACC,QAAD,EAAWC,YAAX,EAAyBC,eAAzB;AAAA,eACfT,yBAAyBxI,SAAzB,CAAmC,SAAnC,EAA8C;AAC5C+I,4BAD4C;AAE5CC,oCAF4C;AAG5CC;AAH4C,SAA9C,CADe;AAAA,OAAjB;AAMD;AACF;AAjBkB,CAArB;kBAmBe5J,Y", "file": "./js/cerner-smart-embeddable-lib-1.7.0.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 77);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 10bcd52f4c7361207090", "var core = module.exports = { version: '2.6.11' };\nif (typeof __e == 'number') __e = core; // eslint-disable-line no-undef\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_core.js\n// module id = 0\n// module chunks = 0", "// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nvar global = module.exports = typeof window != 'undefined' && window.Math == Math\n  ? window : typeof self != 'undefined' && self.Math == Math ? self\n  // eslint-disable-next-line no-new-func\n  : Function('return this')();\nif (typeof __g == 'number') __g = global; // eslint-disable-line no-undef\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_global.js\n// module id = 1\n// module chunks = 0", "var store = require('./_shared')('wks');\nvar uid = require('./_uid');\nvar Symbol = require('./_global').Symbol;\nvar USE_SYMBOL = typeof Symbol == 'function';\n\nvar $exports = module.exports = function (name) {\n  return store[name] || (store[name] =\n    USE_SYMBOL && Symbol[name] || (USE_SYMBOL ? Symbol : uid)('Symbol.' + name));\n};\n\n$exports.store = store;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_wks.js\n// module id = 2\n// module chunks = 0", "var global = require('./_global');\nvar core = require('./_core');\nvar ctx = require('./_ctx');\nvar hide = require('./_hide');\nvar has = require('./_has');\nvar PROTOTYPE = 'prototype';\n\nvar $export = function (type, name, source) {\n  var IS_FORCED = type & $export.F;\n  var IS_GLOBAL = type & $export.G;\n  var IS_STATIC = type & $export.S;\n  var IS_PROTO = type & $export.P;\n  var IS_BIND = type & $export.B;\n  var IS_WRAP = type & $export.W;\n  var exports = IS_GLOBAL ? core : core[name] || (core[name] = {});\n  var expProto = exports[PROTOTYPE];\n  var target = IS_GLOBAL ? global : IS_STATIC ? global[name] : (global[name] || {})[PROTOTYPE];\n  var key, own, out;\n  if (IS_GLOBAL) source = name;\n  for (key in source) {\n    // contains in native\n    own = !IS_FORCED && target && target[key] !== undefined;\n    if (own && has(exports, key)) continue;\n    // export native or passed\n    out = own ? target[key] : source[key];\n    // prevent global pollution for namespaces\n    exports[key] = IS_GLOBAL && typeof target[key] != 'function' ? source[key]\n    // bind timers to global for call from export context\n    : IS_BIND && own ? ctx(out, global)\n    // wrap global constructors for prevent change them in library\n    : IS_WRAP && target[key] == out ? (function (C) {\n      var F = function (a, b, c) {\n        if (this instanceof C) {\n          switch (arguments.length) {\n            case 0: return new C();\n            case 1: return new C(a);\n            case 2: return new C(a, b);\n          } return new C(a, b, c);\n        } return C.apply(this, arguments);\n      };\n      F[PROTOTYPE] = C[PROTOTYPE];\n      return F;\n    // make static versions for prototype methods\n    })(out) : IS_PROTO && typeof out == 'function' ? ctx(Function.call, out) : out;\n    // export proto methods to core.%CONSTRUCTOR%.methods.%NAME%\n    if (IS_PROTO) {\n      (exports.virtual || (exports.virtual = {}))[key] = out;\n      // export proto methods to core.%CONSTRUCTOR%.prototype.%NAME%\n      if (type & $export.R && expProto && !expProto[key]) hide(expProto, key, out);\n    }\n  }\n};\n// type bitmap\n$export.F = 1;   // forced\n$export.G = 2;   // global\n$export.S = 4;   // static\n$export.P = 8;   // proto\n$export.B = 16;  // bind\n$export.W = 32;  // wrap\n$export.U = 64;  // safe\n$export.R = 128; // real proto method for `library`\nmodule.exports = $export;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_export.js\n// module id = 3\n// module chunks = 0", "var isObject = require('./_is-object');\nmodule.exports = function (it) {\n  if (!isObject(it)) throw TypeError(it + ' is not an object!');\n  return it;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_an-object.js\n// module id = 4\n// module chunks = 0", "// Thank's IE8 for his funny defineProperty\nmodule.exports = !require('./_fails')(function () {\n  return Object.defineProperty({}, 'a', { get: function () { return 7; } }).a != 7;\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_descriptors.js\n// module id = 5\n// module chunks = 0", "var anObject = require('./_an-object');\nvar IE8_DOM_DEFINE = require('./_ie8-dom-define');\nvar toPrimitive = require('./_to-primitive');\nvar dP = Object.defineProperty;\n\nexports.f = require('./_descriptors') ? Object.defineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPrimitive(P, true);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return dP(O, P, Attributes);\n  } catch (e) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported!');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-dp.js\n// module id = 6\n// module chunks = 0", "module.exports = function (it) {\n  return typeof it === 'object' ? it !== null : typeof it === 'function';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_is-object.js\n// module id = 7\n// module chunks = 0", "// to indexed object, toObject with fallback for non-array-like ES3 strings\nvar IObject = require('./_iobject');\nvar defined = require('./_defined');\nmodule.exports = function (it) {\n  return IObject(defined(it));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-iobject.js\n// module id = 8\n// module chunks = 0", "var dP = require('./_object-dp');\nvar createDesc = require('./_property-desc');\nmodule.exports = require('./_descriptors') ? function (object, key, value) {\n  return dP.f(object, key, createDesc(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_hide.js\n// module id = 9\n// module chunks = 0", "var hasOwnProperty = {}.hasOwnProperty;\nmodule.exports = function (it, key) {\n  return hasOwnProperty.call(it, key);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_has.js\n// module id = 10\n// module chunks = 0", "module.exports = {};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iterators.js\n// module id = 11\n// module chunks = 0", "// optional / simple context binding\nvar aFunction = require('./_a-function');\nmodule.exports = function (fn, that, length) {\n  aFunction(fn);\n  if (that === undefined) return fn;\n  switch (length) {\n    case 1: return function (a) {\n      return fn.call(that, a);\n    };\n    case 2: return function (a, b) {\n      return fn.call(that, a, b);\n    };\n    case 3: return function (a, b, c) {\n      return fn.call(that, a, b, c);\n    };\n  }\n  return function (/* ...args */) {\n    return fn.apply(that, arguments);\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_ctx.js\n// module id = 12\n// module chunks = 0", "module.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (e) {\n    return true;\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_fails.js\n// module id = 13\n// module chunks = 0", "\"use strict\";\n\nexports.__esModule = true;\n\nexports.default = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/helpers/classCallCheck.js\n// module id = 14\n// module chunks = 0", "var toString = {}.toString;\n\nmodule.exports = function (it) {\n  return toString.call(it).slice(8, -1);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_cof.js\n// module id = 15\n// module chunks = 0", "module.exports = true;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_library.js\n// module id = 16\n// module chunks = 0", "module.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_property-desc.js\n// module id = 17\n// module chunks = 0", "// ********4 / 15.2.3.14 Object.keys(O)\nvar $keys = require('./_object-keys-internal');\nvar enumBugKeys = require('./_enum-bug-keys');\n\nmodule.exports = Object.keys || function keys(O) {\n  return $keys(O, enumBugKeys);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-keys.js\n// module id = 18\n// module chunks = 0", "// 7.1.13 ToObject(argument)\nvar defined = require('./_defined');\nmodule.exports = function (it) {\n  return Object(defined(it));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-object.js\n// module id = 19\n// module chunks = 0", "'use strict';\nvar $at = require('./_string-at')(true);\n\n// 21.1.3.27 String.prototype[@@iterator]()\nrequire('./_iter-define')(String, 'String', function (iterated) {\n  this._t = String(iterated); // target\n  this._i = 0;                // next index\n// 21.1.5.2.1 %StringIteratorPrototype%.next()\n}, function () {\n  var O = this._t;\n  var index = this._i;\n  var point;\n  if (index >= O.length) return { value: undefined, done: true };\n  point = $at(O, index);\n  this._i += point.length;\n  return { value: point, done: false };\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.string.iterator.js\n// module id = 20\n// module chunks = 0", "exports.f = {}.propertyIsEnumerable;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-pie.js\n// module id = 21\n// module chunks = 0", "\"use strict\";\n\nexports.__esModule = true;\n\nvar _defineProperty = require(\"../core-js/object/define-property\");\n\nvar _defineProperty2 = _interopRequireDefault(_defineProperty);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      (0, _defineProperty2.default)(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/helpers/createClass.js\n// module id = 22\n// module chunks = 0", "require('./es6.array.iterator');\nvar global = require('./_global');\nvar hide = require('./_hide');\nvar Iterators = require('./_iterators');\nvar TO_STRING_TAG = require('./_wks')('toStringTag');\n\nvar DOMIterables = ('CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,' +\n  'DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,' +\n  'MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,' +\n  'SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,' +\n  'TextTrackList,TouchList').split(',');\n\nfor (var i = 0; i < DOMIterables.length; i++) {\n  var NAME = DOMIterables[i];\n  var Collection = global[NAME];\n  var proto = Collection && Collection.prototype;\n  if (proto && !proto[TO_STRING_TAG]) hide(proto, TO_STRING_TAG, NAME);\n  Iterators[NAME] = Iterators.Array;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/web.dom.iterable.js\n// module id = 23\n// module chunks = 0", "module.exports = function (it) {\n  if (typeof it != 'function') throw TypeError(it + ' is not a function!');\n  return it;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_a-function.js\n// module id = 24\n// module chunks = 0", "var id = 0;\nvar px = Math.random();\nmodule.exports = function (key) {\n  return 'Symbol('.concat(key === undefined ? '' : key, ')_', (++id + px).toString(36));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_uid.js\n// module id = 25\n// module chunks = 0", "var def = require('./_object-dp').f;\nvar has = require('./_has');\nvar TAG = require('./_wks')('toStringTag');\n\nmodule.exports = function (it, tag, stat) {\n  if (it && !has(it = stat ? it : it.prototype, TAG)) def(it, TAG, { configurable: true, value: tag });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_set-to-string-tag.js\n// module id = 26\n// module chunks = 0", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n/* eslint no-console: 0 */\nexports.default = {\n  log: function log() {\n    if (process.env.NODE_ENV !== 'production') {\n      var _console;\n\n      (_console = console).log.apply(_console, arguments);\n    }\n  },\n\n  warn: function warn() {\n    var _console2;\n\n    return (_console2 = console).warn.apply(_console2, arguments);\n  },\n  error: function error() {\n    var _console3;\n\n    return (_console3 = console).error.apply(_console3, arguments);\n  }\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/xfc/lib/lib/logger.js\n// module id = 27\n// module chunks = 0", "// 7.2.1 RequireObjectCoercible(argument)\nmodule.exports = function (it) {\n  if (it == undefined) throw TypeError(\"Can't call method on  \" + it);\n  return it;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_defined.js\n// module id = 28\n// module chunks = 0", "var isObject = require('./_is-object');\nvar document = require('./_global').document;\n// typeof document.createElement is 'object' in old IE\nvar is = isObject(document) && isObject(document.createElement);\nmodule.exports = function (it) {\n  return is ? document.createElement(it) : {};\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_dom-create.js\n// module id = 29\n// module chunks = 0", "// 7.1.1 ToPrimitive(input [, PreferredType])\nvar isObject = require('./_is-object');\n// instead of the ES6 spec version, we didn't implement @@toPrimitive case\n// and the second argument - flag - preferred type is a string\nmodule.exports = function (it, S) {\n  if (!isObject(it)) return it;\n  var fn, val;\n  if (S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it))) return val;\n  if (typeof (fn = it.valueOf) == 'function' && !isObject(val = fn.call(it))) return val;\n  if (!S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it))) return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-primitive.js\n// module id = 30\n// module chunks = 0", "// ******** / 15.2.3.5 Object.create(O [, Properties])\nvar anObject = require('./_an-object');\nvar dPs = require('./_object-dps');\nvar enumBugKeys = require('./_enum-bug-keys');\nvar IE_PROTO = require('./_shared-key')('IE_PROTO');\nvar Empty = function () { /* empty */ };\nvar PROTOTYPE = 'prototype';\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar createDict = function () {\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = require('./_dom-create')('iframe');\n  var i = enumBugKeys.length;\n  var lt = '<';\n  var gt = '>';\n  var iframeDocument;\n  iframe.style.display = 'none';\n  require('./_html').appendChild(iframe);\n  iframe.src = 'javascript:'; // eslint-disable-line no-script-url\n  // createDict = iframe.contentWindow.Object;\n  // html.removeChild(iframe);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(lt + 'script' + gt + 'document.F=Object' + lt + '/script' + gt);\n  iframeDocument.close();\n  createDict = iframeDocument.F;\n  while (i--) delete createDict[PROTOTYPE][enumBugKeys[i]];\n  return createDict();\n};\n\nmodule.exports = Object.create || function create(O, Properties) {\n  var result;\n  if (O !== null) {\n    Empty[PROTOTYPE] = anObject(O);\n    result = new Empty();\n    Empty[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = createDict();\n  return Properties === undefined ? result : dPs(result, Properties);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-create.js\n// module id = 31\n// module chunks = 0", "// 7.1.15 ToLength\nvar toInteger = require('./_to-integer');\nvar min = Math.min;\nmodule.exports = function (it) {\n  return it > 0 ? min(toInteger(it), 0x1fffffffffffff) : 0; // pow(2, 53) - 1 == 9007199254740991\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-length.js\n// module id = 32\n// module chunks = 0", "// 7.1.4 ToInteger\nvar ceil = Math.ceil;\nvar floor = Math.floor;\nmodule.exports = function (it) {\n  return isNaN(it = +it) ? 0 : (it > 0 ? floor : ceil)(it);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_to-integer.js\n// module id = 33\n// module chunks = 0", "var shared = require('./_shared')('keys');\nvar uid = require('./_uid');\nmodule.exports = function (key) {\n  return shared[key] || (shared[key] = uid(key));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_shared-key.js\n// module id = 34\n// module chunks = 0", "var core = require('./_core');\nvar global = require('./_global');\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || (global[SHARED] = {});\n\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: core.version,\n  mode: require('./_library') ? 'pure' : 'global',\n  copyright: '© 2019 <PERSON> (zloirock.ru)'\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_shared.js\n// module id = 35\n// module chunks = 0", "// IE 8- don't enum bug keys\nmodule.exports = (\n  'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'\n).split(',');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_enum-bug-keys.js\n// module id = 36\n// module chunks = 0", "// getting tag from ******** Object.prototype.toString()\nvar cof = require('./_cof');\nvar TAG = require('./_wks')('toStringTag');\n// ES3 wrong here\nvar ARG = cof(function () { return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (e) { /* empty */ }\n};\n\nmodule.exports = function (it) {\n  var O, T, B;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (T = tryGet(O = Object(it), TAG)) == 'string' ? T\n    // builtinTag case\n    : ARG ? cof(O)\n    // ES3 arguments fallback\n    : (B = cof(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : B;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_classof.js\n// module id = 37\n// module chunks = 0", "var classof = require('./_classof');\nvar ITERATOR = require('./_wks')('iterator');\nvar Iterators = require('./_iterators');\nmodule.exports = require('./_core').getIteratorMethod = function (it) {\n  if (it != undefined) return it[ITERATOR]\n    || it['@@iterator']\n    || Iterators[classof(it)];\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/core.get-iterator-method.js\n// module id = 38\n// module chunks = 0", "exports.f = Object.getOwnPropertySymbols;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-gops.js\n// module id = 39\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/promise\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/promise.js\n// module id = 40\n// module chunks = 0", "'use strict';\n// 25.4.1.5 NewPromiseCapability(C)\nvar aFunction = require('./_a-function');\n\nfunction PromiseCapability(C) {\n  var resolve, reject;\n  this.promise = new C(function ($$resolve, $$reject) {\n    if (resolve !== undefined || reject !== undefined) throw TypeError('Bad Promise constructor');\n    resolve = $$resolve;\n    reject = $$reject;\n  });\n  this.resolve = aFunction(resolve);\n  this.reject = aFunction(reject);\n}\n\nmodule.exports.f = function (C) {\n  return new PromiseCapability(C);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_new-promise-capability.js\n// module id = 41\n// module chunks = 0", "exports.f = require('./_wks');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_wks-ext.js\n// module id = 42\n// module chunks = 0", "var global = require('./_global');\nvar core = require('./_core');\nvar LIBRARY = require('./_library');\nvar wksExt = require('./_wks-ext');\nvar defineProperty = require('./_object-dp').f;\nmodule.exports = function (name) {\n  var $Symbol = core.Symbol || (core.Symbol = LIBRARY ? {} : global.Symbol || {});\n  if (name.charAt(0) != '_' && !(name in $Symbol)) defineProperty($Symbol, name, { value: wksExt.f(name) });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_wks-define.js\n// module id = 43\n// module chunks = 0", "\"use strict\";\n\nexports.__esModule = true;\n\nvar _isIterable2 = require(\"../core-js/is-iterable\");\n\nvar _isIterable3 = _interopRequireDefault(_isIterable2);\n\nvar _getIterator2 = require(\"../core-js/get-iterator\");\n\nvar _getIterator3 = _interopRequireDefault(_getIterator2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = function () {\n  function sliceIterator(arr, i) {\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _e = undefined;\n\n    try {\n      for (var _i = (0, _getIterator3.default)(arr), _s; !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"]) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n\n    return _arr;\n  }\n\n  return function (arr, i) {\n    if (Array.isArray(arr)) {\n      return arr;\n    } else if ((0, _isIterable3.default)(Object(arr))) {\n      return sliceIterator(arr, i);\n    } else {\n      throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n    }\n  };\n}();\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/helpers/slicedToArray.js\n// module id = 44\n// module chunks = 0", "// fallback for non-array-like ES3 and non-enumerable old V8 strings\nvar cof = require('./_cof');\n// eslint-disable-next-line no-prototype-builtins\nmodule.exports = Object('z').propertyIsEnumerable(0) ? Object : function (it) {\n  return cof(it) == 'String' ? it.split('') : Object(it);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iobject.js\n// module id = 45\n// module chunks = 0", "'use strict';\nvar LIBRARY = require('./_library');\nvar $export = require('./_export');\nvar redefine = require('./_redefine');\nvar hide = require('./_hide');\nvar Iterators = require('./_iterators');\nvar $iterCreate = require('./_iter-create');\nvar setToStringTag = require('./_set-to-string-tag');\nvar getPrototypeOf = require('./_object-gpo');\nvar ITERATOR = require('./_wks')('iterator');\nvar BUGGY = !([].keys && 'next' in [].keys()); // <PERSON>fari has buggy iterators w/o `next`\nvar FF_ITERATOR = '@@iterator';\nvar KEYS = 'keys';\nvar VALUES = 'values';\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (Base, NAME, Constructor, next, DEFAULT, IS_SET, FORCED) {\n  $iterCreate(Constructor, NAME, next);\n  var getMethod = function (kind) {\n    if (!BUGGY && kind in proto) return proto[kind];\n    switch (kind) {\n      case KEYS: return function keys() { return new Constructor(this, kind); };\n      case VALUES: return function values() { return new Constructor(this, kind); };\n    } return function entries() { return new Constructor(this, kind); };\n  };\n  var TAG = NAME + ' Iterator';\n  var DEF_VALUES = DEFAULT == VALUES;\n  var VALUES_BUG = false;\n  var proto = Base.prototype;\n  var $native = proto[ITERATOR] || proto[FF_ITERATOR] || DEFAULT && proto[DEFAULT];\n  var $default = $native || getMethod(DEFAULT);\n  var $entries = DEFAULT ? !DEF_VALUES ? $default : getMethod('entries') : undefined;\n  var $anyNative = NAME == 'Array' ? proto.entries || $native : $native;\n  var methods, key, IteratorPrototype;\n  // Fix native\n  if ($anyNative) {\n    IteratorPrototype = getPrototypeOf($anyNative.call(new Base()));\n    if (IteratorPrototype !== Object.prototype && IteratorPrototype.next) {\n      // Set @@toStringTag to native iterators\n      setToStringTag(IteratorPrototype, TAG, true);\n      // fix for some old engines\n      if (!LIBRARY && typeof IteratorPrototype[ITERATOR] != 'function') hide(IteratorPrototype, ITERATOR, returnThis);\n    }\n  }\n  // fix Array#{values, @@iterator}.name in V8 / FF\n  if (DEF_VALUES && $native && $native.name !== VALUES) {\n    VALUES_BUG = true;\n    $default = function values() { return $native.call(this); };\n  }\n  // Define iterator\n  if ((!LIBRARY || FORCED) && (BUGGY || VALUES_BUG || !proto[ITERATOR])) {\n    hide(proto, ITERATOR, $default);\n  }\n  // Plug for library\n  Iterators[NAME] = $default;\n  Iterators[TAG] = returnThis;\n  if (DEFAULT) {\n    methods = {\n      values: DEF_VALUES ? $default : getMethod(VALUES),\n      keys: IS_SET ? $default : getMethod(KEYS),\n      entries: $entries\n    };\n    if (FORCED) for (key in methods) {\n      if (!(key in proto)) redefine(proto, key, methods[key]);\n    } else $export($export.P + $export.F * (BUGGY || VALUES_BUG), NAME, methods);\n  }\n  return methods;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iter-define.js\n// module id = 46\n// module chunks = 0", "module.exports = !require('./_descriptors') && !require('./_fails')(function () {\n  return Object.defineProperty(require('./_dom-create')('div'), 'a', { get: function () { return 7; } }).a != 7;\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_ie8-dom-define.js\n// module id = 47\n// module chunks = 0", "module.exports = require('./_hide');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_redefine.js\n// module id = 48\n// module chunks = 0", "var has = require('./_has');\nvar toIObject = require('./_to-iobject');\nvar arrayIndexOf = require('./_array-includes')(false);\nvar IE_PROTO = require('./_shared-key')('IE_PROTO');\n\nmodule.exports = function (object, names) {\n  var O = toIObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) if (key != IE_PROTO) has(O, key) && result.push(key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (has(O, key = names[i++])) {\n    ~arrayIndexOf(result, key) || result.push(key);\n  }\n  return result;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-keys-internal.js\n// module id = 49\n// module chunks = 0", "var document = require('./_global').document;\nmodule.exports = document && document.documentElement;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_html.js\n// module id = 50\n// module chunks = 0", "// 19.1.2.9 / 15.2.3.2 Object.getPrototypeOf(O)\nvar has = require('./_has');\nvar toObject = require('./_to-object');\nvar IE_PROTO = require('./_shared-key')('IE_PROTO');\nvar ObjectProto = Object.prototype;\n\nmodule.exports = Object.getPrototypeOf || function (O) {\n  O = toObject(O);\n  if (has(O, IE_PROTO)) return O[IE_PROTO];\n  if (typeof O.constructor == 'function' && O instanceof O.constructor) {\n    return O.constructor.prototype;\n  } return O instanceof Object ? ObjectProto : null;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-gpo.js\n// module id = 51\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/object/entries\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/object/entries.js\n// module id = 52\n// module chunks = 0", "\"use strict\";\n\nexports.__esModule = true;\n\nvar _assign = require(\"../core-js/object/assign\");\n\nvar _assign2 = _interopRequireDefault(_assign);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = _assign2.default || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/helpers/extends.js\n// module id = 53\n// module chunks = 0", "// call something on iterator step with safe closing on error\nvar anObject = require('./_an-object');\nmodule.exports = function (iterator, fn, value, entries) {\n  try {\n    return entries ? fn(anObject(value)[0], value[1]) : fn(value);\n  // 7.4.6 IteratorClose(iterator, completion)\n  } catch (e) {\n    var ret = iterator['return'];\n    if (ret !== undefined) anObject(ret.call(iterator));\n    throw e;\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iter-call.js\n// module id = 55\n// module chunks = 0", "// check on default Array iterator\nvar Iterators = require('./_iterators');\nvar ITERATOR = require('./_wks')('iterator');\nvar ArrayProto = Array.prototype;\n\nmodule.exports = function (it) {\n  return it !== undefined && (Iterators.Array === it || ArrayProto[ITERATOR] === it);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_is-array-iter.js\n// module id = 56\n// module chunks = 0", "// 7.3.20 SpeciesConstructor(O, defaultConstructor)\nvar anObject = require('./_an-object');\nvar aFunction = require('./_a-function');\nvar SPECIES = require('./_wks')('species');\nmodule.exports = function (O, D) {\n  var C = anObject(O).constructor;\n  var S;\n  return C === undefined || (S = anObject(C)[SPECIES]) == undefined ? D : aFunction(S);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_species-constructor.js\n// module id = 57\n// module chunks = 0", "var ctx = require('./_ctx');\nvar invoke = require('./_invoke');\nvar html = require('./_html');\nvar cel = require('./_dom-create');\nvar global = require('./_global');\nvar process = global.process;\nvar setTask = global.setImmediate;\nvar clearTask = global.clearImmediate;\nvar MessageChannel = global.MessageChannel;\nvar Dispatch = global.Dispatch;\nvar counter = 0;\nvar queue = {};\nvar ONREADYSTATECHANGE = 'onreadystatechange';\nvar defer, channel, port;\nvar run = function () {\n  var id = +this;\n  // eslint-disable-next-line no-prototype-builtins\n  if (queue.hasOwnProperty(id)) {\n    var fn = queue[id];\n    delete queue[id];\n    fn();\n  }\n};\nvar listener = function (event) {\n  run.call(event.data);\n};\n// Node.js 0.9+ & IE10+ has setImmediate, otherwise:\nif (!setTask || !clearTask) {\n  setTask = function setImmediate(fn) {\n    var args = [];\n    var i = 1;\n    while (arguments.length > i) args.push(arguments[i++]);\n    queue[++counter] = function () {\n      // eslint-disable-next-line no-new-func\n      invoke(typeof fn == 'function' ? fn : Function(fn), args);\n    };\n    defer(counter);\n    return counter;\n  };\n  clearTask = function clearImmediate(id) {\n    delete queue[id];\n  };\n  // Node.js 0.8-\n  if (require('./_cof')(process) == 'process') {\n    defer = function (id) {\n      process.nextTick(ctx(run, id, 1));\n    };\n  // Sphere (JS game engine) Dispatch API\n  } else if (Dispatch && Dispatch.now) {\n    defer = function (id) {\n      Dispatch.now(ctx(run, id, 1));\n    };\n  // Browsers with MessageChannel, includes WebWorkers\n  } else if (MessageChannel) {\n    channel = new MessageChannel();\n    port = channel.port2;\n    channel.port1.onmessage = listener;\n    defer = ctx(port.postMessage, port, 1);\n  // Browsers with postMessage, skip WebWorkers\n  // IE8 has postMessage, but it's sync & typeof its postMessage is 'object'\n  } else if (global.addEventListener && typeof postMessage == 'function' && !global.importScripts) {\n    defer = function (id) {\n      global.postMessage(id + '', '*');\n    };\n    global.addEventListener('message', listener, false);\n  // IE8-\n  } else if (ONREADYSTATECHANGE in cel('script')) {\n    defer = function (id) {\n      html.appendChild(cel('script'))[ONREADYSTATECHANGE] = function () {\n        html.removeChild(this);\n        run.call(id);\n      };\n    };\n  // Rest old browsers\n  } else {\n    defer = function (id) {\n      setTimeout(ctx(run, id, 1), 0);\n    };\n  }\n}\nmodule.exports = {\n  set: setTask,\n  clear: clearTask\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_task.js\n// module id = 58\n// module chunks = 0", "module.exports = function (exec) {\n  try {\n    return { e: false, v: exec() };\n  } catch (e) {\n    return { e: true, v: e };\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_perform.js\n// module id = 59\n// module chunks = 0", "var anObject = require('./_an-object');\nvar isObject = require('./_is-object');\nvar newPromiseCapability = require('./_new-promise-capability');\n\nmodule.exports = function (C, x) {\n  anObject(C);\n  if (isObject(x) && x.constructor === C) return x;\n  var promiseCapability = newPromiseCapability.f(C);\n  var resolve = promiseCapability.resolve;\n  resolve(x);\n  return promiseCapability.promise;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_promise-resolve.js\n// module id = 60\n// module chunks = 0", "var ITERATOR = require('./_wks')('iterator');\nvar SAFE_CLOSING = false;\n\ntry {\n  var riter = [7][ITERATOR]();\n  riter['return'] = function () { SAFE_CLOSING = true; };\n  // eslint-disable-next-line no-throw-literal\n  Array.from(riter, function () { throw 2; });\n} catch (e) { /* empty */ }\n\nmodule.exports = function (exec, skipClosing) {\n  if (!skipClosing && !SAFE_CLOSING) return false;\n  var safe = false;\n  try {\n    var arr = [7];\n    var iter = arr[ITERATOR]();\n    iter.next = function () { return { done: safe = true }; };\n    arr[ITERATOR] = function () { return iter; };\n    exec(arr);\n  } catch (e) { /* empty */ }\n  return safe;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iter-detect.js\n// module id = 61\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/object/get-prototype-of\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/object/get-prototype-of.js\n// module id = 62\n// module chunks = 0", "// most Object methods by ES6 should accept primitives\nvar $export = require('./_export');\nvar core = require('./_core');\nvar fails = require('./_fails');\nmodule.exports = function (KEY, exec) {\n  var fn = (core.Object || {})[KEY] || Object[KEY];\n  var exp = {};\n  exp[KEY] = exec(fn);\n  $export($export.S + $export.F * fails(function () { fn(1); }), 'Object', exp);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-sap.js\n// module id = 63\n// module chunks = 0", "\"use strict\";\n\nexports.__esModule = true;\n\nvar _typeof2 = require(\"../helpers/typeof\");\n\nvar _typeof3 = _interopRequireDefault(_typeof2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = function (self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return call && ((typeof call === \"undefined\" ? \"undefined\" : (0, _typeof3.default)(call)) === \"object\" || typeof call === \"function\") ? call : self;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/helpers/possibleConstructorReturn.js\n// module id = 64\n// module chunks = 0", "\"use strict\";\n\nexports.__esModule = true;\n\nvar _iterator = require(\"../core-js/symbol/iterator\");\n\nvar _iterator2 = _interopRequireDefault(_iterator);\n\nvar _symbol = require(\"../core-js/symbol\");\n\nvar _symbol2 = _interopRequireDefault(_symbol);\n\nvar _typeof = typeof _symbol2.default === \"function\" && typeof _iterator2.default === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof _symbol2.default === \"function\" && obj.constructor === _symbol2.default && obj !== _symbol2.default.prototype ? \"symbol\" : typeof obj; };\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = typeof _symbol2.default === \"function\" && _typeof(_iterator2.default) === \"symbol\" ? function (obj) {\n  return typeof obj === \"undefined\" ? \"undefined\" : _typeof(obj);\n} : function (obj) {\n  return obj && typeof _symbol2.default === \"function\" && obj.constructor === _symbol2.default && obj !== _symbol2.default.prototype ? \"symbol\" : typeof obj === \"undefined\" ? \"undefined\" : _typeof(obj);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/helpers/typeof.js\n// module id = 65\n// module chunks = 0", "var META = require('./_uid')('meta');\nvar isObject = require('./_is-object');\nvar has = require('./_has');\nvar setDesc = require('./_object-dp').f;\nvar id = 0;\nvar isExtensible = Object.isExtensible || function () {\n  return true;\n};\nvar FREEZE = !require('./_fails')(function () {\n  return isExtensible(Object.preventExtensions({}));\n});\nvar setMeta = function (it) {\n  setDesc(it, META, { value: {\n    i: 'O' + ++id, // object ID\n    w: {}          // weak collections IDs\n  } });\n};\nvar fastKey = function (it, create) {\n  // return primitive with prefix\n  if (!isObject(it)) return typeof it == 'symbol' ? it : (typeof it == 'string' ? 'S' : 'P') + it;\n  if (!has(it, META)) {\n    // can't set metadata to uncaught frozen object\n    if (!isExtensible(it)) return 'F';\n    // not necessary to add metadata\n    if (!create) return 'E';\n    // add missing metadata\n    setMeta(it);\n  // return object ID\n  } return it[META].i;\n};\nvar getWeak = function (it, create) {\n  if (!has(it, META)) {\n    // can't set metadata to uncaught frozen object\n    if (!isExtensible(it)) return true;\n    // not necessary to add metadata\n    if (!create) return false;\n    // add missing metadata\n    setMeta(it);\n  // return hash weak collections IDs\n  } return it[META].w;\n};\n// add metadata on freeze-family methods calling\nvar onFreeze = function (it) {\n  if (FREEZE && meta.NEED && isExtensible(it) && !has(it, META)) setMeta(it);\n  return it;\n};\nvar meta = module.exports = {\n  KEY: META,\n  NEED: false,\n  fastKey: fastKey,\n  getWeak: getWeak,\n  onFreeze: onFreeze\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_meta.js\n// module id = 66\n// module chunks = 0", "// ******** / 15.2.3.4 Object.getOwnPropertyNames(O)\nvar $keys = require('./_object-keys-internal');\nvar hiddenKeys = require('./_enum-bug-keys').concat('length', 'prototype');\n\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return $keys(O, hiddenKeys);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-gopn.js\n// module id = 67\n// module chunks = 0", "var pIE = require('./_object-pie');\nvar createDesc = require('./_property-desc');\nvar toIObject = require('./_to-iobject');\nvar toPrimitive = require('./_to-primitive');\nvar has = require('./_has');\nvar IE8_DOM_DEFINE = require('./_ie8-dom-define');\nvar gOPD = Object.getOwnPropertyDescriptor;\n\nexports.f = require('./_descriptors') ? gOPD : function getOwnPropertyDescriptor(O, P) {\n  O = toIObject(O);\n  P = toPrimitive(P, true);\n  if (IE8_DOM_DEFINE) try {\n    return gOPD(O, P);\n  } catch (e) { /* empty */ }\n  if (has(O, P)) return createDesc(!pIE.f.call(O, P), O[P]);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-gopd.js\n// module id = 68\n// module chunks = 0", "\"use strict\";\n\nexports.__esModule = true;\n\nvar _setPrototypeOf = require(\"../core-js/object/set-prototype-of\");\n\nvar _setPrototypeOf2 = _interopRequireDefault(_setPrototypeOf);\n\nvar _create = require(\"../core-js/object/create\");\n\nvar _create2 = _interopRequireDefault(_create);\n\nvar _typeof2 = require(\"../helpers/typeof\");\n\nvar _typeof3 = _interopRequireDefault(_typeof2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = function (subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + (typeof superClass === \"undefined\" ? \"undefined\" : (0, _typeof3.default)(superClass)));\n  }\n\n  subClass.prototype = (0, _create2.default)(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf2.default ? (0, _setPrototypeOf2.default)(subClass, superClass) : subClass.__proto__ = superClass;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/helpers/inherits.js\n// module id = 69\n// module chunks = 0", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/events/events.js\n// module id = 70\n// module chunks = 0", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _freeze = require('babel-runtime/core-js/object/freeze');\n\nvar _freeze2 = _interopRequireDefault(_freeze);\n\nvar _promise = require('babel-runtime/core-js/promise');\n\nvar _promise2 = _interopRequireDefault(_promise);\n\nvar _extends2 = require('babel-runtime/helpers/extends');\n\nvar _extends3 = _interopRequireDefault(_extends2);\n\nvar _classCallCheck2 = require('babel-runtime/helpers/classCallCheck');\n\nvar _classCallCheck3 = _interopRequireDefault(_classCallCheck2);\n\nvar _createClass2 = require('babel-runtime/helpers/createClass');\n\nvar _createClass3 = _interopRequireDefault(_createClass2);\n\nvar _uuid = require('uuid');\n\nvar _uuid2 = _interopRequireDefault(_uuid);\n\nvar _errors = require('./errors');\n\nvar _errors2 = _interopRequireDefault(_errors);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar JSONRPCVersion = '2.0';\n\n/**\n * JSONRPC class based on the JSONRPC specification[0]. This object handles creating JSONRPC\n * requests and notification objects and managing resolving responses.\n *\n * @see {@link  http://json-rpc.org/wiki/specification}\n */\n\nvar JSONRPC = function () {\n  /**\n   * Initializes a JSONRPC instance.\n   * @param {function} dispatcher - A function which takes a single argument for\n   *                                the JSONRPC object to send to the RPC server.\n   * @param {object} methods - The JSONRPC methods to handle.\n   */\n  function JSONRPC(dispatcher) {\n    var methods = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    (0, _classCallCheck3.default)(this, JSONRPC);\n\n    this.version = JSONRPCVersion;\n    this.deferreds = {};\n    this.methods = methods;\n    this.dispatcher = dispatcher;\n  }\n\n  /**\n   * Sends the given JSONPRC message using the dispatcher provided.\n   * @param {object} message - A JSONPRC 2.0 message object.\n   */\n\n\n  (0, _createClass3.default)(JSONRPC, [{\n    key: 'send',\n    value: function send(message) {\n      var data = (0, _extends3.default)({}, message);\n      data.jsonrpc = this.version;\n\n      this.dispatcher(data);\n    }\n\n    /**\n     * Create a notification object for the given method and params.\n     * @see {@link  http://www.jsonrpc.org/specification#notification|JSONRPC Notifications}\n     *\n     * @param {string} method - The RPC method to execute\n     * @param {object[]} params - The parameters to execute with the method.\n     * @returns {object} JSONRPC notification object\n     */\n\n  }, {\n    key: 'notification',\n    value: function notification(method) {\n      var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n\n      this.send({ method: method, params: params });\n    }\n\n    /**\n     * Create a request object for the given method and params and passes the result to\n     * next(err, result).\n     * @see {@link  http://www.jsonrpc.org/specification#request_object|JSONRPC Requests}\n     *\n     * @param {string} method - The RPC method to execute\n     * @param {object[]} params - The parameters to execute with the method.\n     * @returns {Promise} which is resolved with the response value.\n     */\n\n  }, {\n    key: 'request',\n    value: function request(method) {\n      var _this = this;\n\n      var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n\n      return new _promise2.default(function (resolve, reject) {\n        var id = _uuid2.default.v4();\n\n        // Save the resolve/reject callbacks as a deferred. We do this because\n        // the response may not occur within the scope of the dispatch method. Example\n        // Cross-Domain messaging is sent over postMessage but received via the\n        // message event.\n        _this.deferreds[id] = { resolve: resolve, reject: reject };\n\n        _this.send({ id: id, method: method, params: params });\n      });\n    }\n\n    /**\n    * Handles a JSONRPC message for the following scenarios:\n    * request - Executes the method defined and passes the result to dispatch\n    * response - Resolves the promise associated with the id on the response\n    * notification - Executes the method defined\n    *\n    * @param {object} message - The JSONRPC message to handle.\n    */\n\n  }, {\n    key: 'handle',\n    value: function handle(message) {\n      // Requests and notifications have methods defined.\n      if (message.method) {\n        // Requests have ids\n        if (message.id) {\n          this.handleRequest(message);\n\n          // Notifications have no ids\n        } else {\n          this.handleNotification(message);\n        }\n\n        // Responses have no methods, but have an id\n      } else if (message.id) {\n        this.handleResponse(message);\n      }\n    }\n\n    /**\n    * Handle a JSONRPC response object and resolve the promise associated to the\n    * originating request.\n    * @param {object} response - The JSONRPC response object to handle.\n    */\n\n  }, {\n    key: 'handleResponse',\n    value: function handleResponse(response) {\n      var deferred = this.deferreds[response.id];\n      if (deferred === undefined) {\n        return;\n      }\n\n      if (response.error) {\n        deferred.reject(response.error);\n      } else {\n        deferred.resolve(response.result);\n      }\n\n      delete this.deferreds[response.id];\n    }\n\n    /**\n    * Handle a JSONRPC request object and execute the method it specifies sending\n    * the result to the dispatcher.\n    * @param {object} request - The JSONRPC request object to handle.\n    */\n\n  }, {\n    key: 'handleRequest',\n    value: function handleRequest(request) {\n      var _this2 = this;\n\n      var method = this.methods[request.method];\n      if (typeof method !== 'function') {\n        var error = {\n          message: 'The method ' + method + ' was not found.',\n          code: _errors2.default.METHOD_NOT_FOUND\n        };\n        this.send({ id: request.id, error: error });\n        return;\n      }\n      // Success\n      method.apply(request, request.params).then(function (result) {\n        _this2.send({ id: request.id, result: result });\n        // Error\n      }).catch(function (message) {\n        var error = { message: message, code: _errors2.default.INTERNAL_ERROR };\n        _this2.send({ id: request.id, error: error });\n      });\n    }\n\n    /**\n    * Handle a JSONRPC notification request and execute the method it specifies.\n    * @param {object} request - The JSONRPC notification object to handle.\n    */\n\n  }, {\n    key: 'handleNotification',\n    value: function handleNotification(request) {\n      var method = this.methods[request.method];\n      if (method && typeof method === 'function') {\n        method.apply(request, request.params);\n      }\n    }\n  }]);\n  return JSONRPC;\n}();\n\nexports.default = (0, _freeze2.default)(JSONRPC);\n\n\n// WEBPACK FOOTER //\n// ./node_modules/jsonrpc-dispatch/lib/src/index.js", "module.exports = { \"default\": require(\"core-js/library/fn/object/freeze\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/object/freeze.js\n// module id = 72\n// module chunks = 0", "// Unique ID creation requires a high quality random # generator.  In the\n// browser this is a little complicated due to unknown quality of Math.random()\n// and inconsistent support for the `crypto` API.  We do the best we can via\n// feature-detection\n\n// getRandomValues needs to be invoked in a context where \"this\" is a Crypto\n// implementation. Also, find the complete implementation of crypto on IE11.\nvar getRandomValues = (typeof(crypto) != 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto)) ||\n                      (typeof(msCrypto) != 'undefined' && typeof window.msCrypto.getRandomValues == 'function' && msCrypto.getRandomValues.bind(msCrypto));\n\nif (getRandomValues) {\n  // WHATWG crypto RNG - http://wiki.whatwg.org/wiki/Crypto\n  var rnds8 = new Uint8Array(16); // eslint-disable-line no-undef\n\n  module.exports = function whatwgRNG() {\n    getRandomValues(rnds8);\n    return rnds8;\n  };\n} else {\n  // Math.random()-based (RNG)\n  //\n  // If all else fails, use Math.random().  It's fast, but is of unspecified\n  // quality.\n  var rnds = new Array(16);\n\n  module.exports = function mathRNG() {\n    for (var i = 0, r; i < 16; i++) {\n      if ((i & 0x03) === 0) r = Math.random() * 0x100000000;\n      rnds[i] = r >>> ((i & 0x03) << 3) & 0xff;\n    }\n\n    return rnds;\n  };\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/uuid/lib/rng-browser.js\n// module id = 73\n// module chunks = 0", "/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\nvar byteToHex = [];\nfor (var i = 0; i < 256; ++i) {\n  byteToHex[i] = (i + 0x100).toString(16).substr(1);\n}\n\nfunction bytesToUuid(buf, offset) {\n  var i = offset || 0;\n  var bth = byteToHex;\n  // join used to fix memory issue caused by concatenation: https://bugs.chromium.org/p/v8/issues/detail?id=3175#c4\n  return ([\n    bth[buf[i++]], bth[buf[i++]],\n    bth[buf[i++]], bth[buf[i++]], '-',\n    bth[buf[i++]], bth[buf[i++]], '-',\n    bth[buf[i++]], bth[buf[i++]], '-',\n    bth[buf[i++]], bth[buf[i++]], '-',\n    bth[buf[i++]], bth[buf[i++]],\n    bth[buf[i++]], bth[buf[i++]],\n    bth[buf[i++]], bth[buf[i++]]\n  ]).join('');\n}\n\nmodule.exports = bytesToUuid;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/uuid/lib/bytesToUuid.js\n// module id = 74\n// module chunks = 0", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _classCallCheck2 = require('babel-runtime/helpers/classCallCheck');\n\nvar _classCallCheck3 = _interopRequireDefault(_classCallCheck2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/** URI class for parsing URIs */\nvar URI = function URI(uri) {\n  (0, _classCallCheck3.default)(this, URI);\n\n  var a = document.createElement('a');\n  a.href = uri;\n\n  this.protocol = a.protocol;\n  this.pathname = a.pathname;\n\n  // In postMessage the default port for each protocol is stripped from\n  // the event origin. Exclude it so the origins match up.\n  var portMatch = this.protocol === 'http:' ? /(:80)$/ : /(:443)$/;\n  this.host = a.host.replace(portMatch, '');\n\n  this.origin = this.protocol + '//' + this.host;\n};\n\nexports.default = URI;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/xfc/lib/lib/uri.js\n// module id = 75\n// module chunks = 0", "\"use strict\";\n\nexports.__esModule = true;\n\nvar _from = require(\"../core-js/array/from\");\n\nvar _from2 = _interopRequireDefault(_from);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = function (arr) {\n  if (Array.isArray(arr)) {\n    for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {\n      arr2[i] = arr[i];\n    }\n\n    return arr2;\n  } else {\n    return (0, _from2.default)(arr);\n  }\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/helpers/toConsumableArray.js\n// module id = 76\n// module chunks = 0", "// This is the entry point for webpack to distribute assets\n\n// Require all less files and output them as css for non webpack consumers\nrequire('./src/less/manifest.less');\n\n// Require all JS and output as a single js bundle.\n// Note, we are requiring the es6 js.\nrequire('./src/js');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./webpack.entry.js\n// module id = 78\n// module chunks = 0", "// removed by extract-text-webpack-plugin\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/less/manifest.less\n// module id = 79\n// module chunks = 0", "/* globals window */\n\nimport CernerSmartEmbeddableLib from './cerner-smart-embeddable-lib';\nimport ComOverrider from './com-overrider';\n\nCernerSmartEmbeddableLib.init();\nCernerSmartEmbeddableLib.listenForCustomFrameHeight();\n\nwindow.CernerSmartEmbeddableLib = window.CernerSmartEmbeddableLib || {};\nwindow.CernerSmartEmbeddableLib.calcFrameHeight = CernerSmartEmbeddableLib.calcFrameHeight;\nwindow.CernerSmartEmbeddableLib.setFrameHeight = CernerSmartEmbeddableLib.setFrameHeight;\n\nComOverrider.override(CernerSmartEmbeddableLib);\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/index.js", "/* global window */\n\nimport { Provider } from 'xfc';\n\n/**\n* Wrapper object to initialize the provider's content\n* to allow content to embed inside an iframe.\n*/\nconst CernerSmartEmbeddableLib = {\n\n  /**\n  * Initializes the provider wrapper object with ACLs.\n  */\n  init: () => {\n    Provider.init({\n      acls: ['https://embedded.cerner.com',\n        'https://embedded.sandboxcerner.com', 'https://embedded.devcerner.com',\n        'https://embedded.applications.ca.cerner.com', 'https://embedded.ca.cernerpowerchart.net',\n        'https://embedded.applications.au.cerner.com', 'https://embedded.au.cernerpowerchart.net',\n        'https://embedded.emea-2.cerner.com', 'https://embedded.applications.uae-1.cerner.com'],\n    });\n  },\n  /**\n  * Get the frame height.  The default height is HTML's scrollHeight.\n  */\n  calcFrameHeight: () => window.document.getElementsByTagName('html')[0].scrollHeight,\n\n  /**\n  * Pass the height info to the consumer by triggering iframeCustomResizer\n  * message with the height detail.\n  */\n  setFrameHeight: (h) => {\n    Provider.trigger('iframeCustomResizer', { height: h });\n  },\n  /**\n  * Listen for iframeCustomResizer message.\n  * Calculate the frame height in px and set the height.\n  */\n  listenForCustomFrameHeight: () => {\n    Provider.on('iframeCustomResizer', () => {\n      const height = `${window.CernerSmartEmbeddableLib.calcFrameHeight()}px`;\n      CernerSmartEmbeddableLib.setFrameHeight(height);\n    });\n  },\n  /**\n   * API invocation with specified name and corresponding params\n   * @param {string} apiName   - API name required to invoke, not null  or undefined\n   * @param {object} params - Any number of parameters that passed to API, not null or undefined\n   */\n  invokeAPI: function invokeAPI(apiName, params) {\n    if (apiName && params) {\n      // Trigger COM Api specific event 'invokeCOMApi'\n      Provider.trigger('invokeCOMApi', { name: apiName, params });\n    }\n  },\n};\n\nexport default CernerSmartEmbeddableLib;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/cerner-smart-embeddable-lib.js", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Consumer = exports.Provider = undefined;\n\nvar _consumer = require('./consumer');\n\nvar _consumer2 = _interopRequireDefault(_consumer);\n\nvar _provider = require('./provider');\n\nvar _provider2 = _interopRequireDefault(_provider);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.Provider = _provider2.default;\nexports.Consumer = _consumer2.default;\n\n\n// WEBPACK FOOTER //\n// ./node_modules/xfc/lib/index.js", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _consumer = require('./consumer');\n\nvar _consumer2 = _interopRequireDefault(_consumer);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = new _consumer2.default();\n\n\n// WEBPACK FOOTER //\n// ./node_modules/xfc/lib/consumer/index.js", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _slicedToArray2 = require('babel-runtime/helpers/slicedToArray');\n\nvar _slicedToArray3 = _interopRequireDefault(_slicedToArray2);\n\nvar _entries = require('babel-runtime/core-js/object/entries');\n\nvar _entries2 = _interopRequireDefault(_entries);\n\nvar _classCallCheck2 = require('babel-runtime/helpers/classCallCheck');\n\nvar _classCallCheck3 = _interopRequireDefault(_classCallCheck2);\n\nvar _createClass2 = require('babel-runtime/helpers/createClass');\n\nvar _createClass3 = _interopRequireDefault(_createClass2);\n\nvar _frame = require('./frame');\n\nvar _frame2 = _interopRequireDefault(_frame);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar Consumer = function () {\n  function Consumer() {\n    (0, _classCallCheck3.default)(this, Consumer);\n  }\n\n  (0, _createClass3.default)(Consumer, [{\n    key: 'init',\n\n    /**\n     * Initialize a consumer.\n     * @param  {Array}  globalHandlers - an object containing event handlers that apply to all frames.\n     * @example\n     * // Each key/value pair in globalHandlers should be a pair of event name and event handler.\n     * const handlers = {'eventA': function() {}, 'eventB': function() {}};\n     * Consumer.init(handlers);\n     */\n    value: function init() {\n      var globalHandlers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n      this.globalHandlers = globalHandlers;\n    }\n\n    /**\n     * Mount the given source as an application into the given container.\n     * @param {object} container - The DOM element to append the mounted frame to.\n     * @param {string} source - The source URL to load the app from.\n     * @param {string} options - An optional parameter that contains optional configs\n     * @return {Frame} Returns the application that was mounted.\n     */\n\n  }, {\n    key: 'mount',\n    value: function mount(container, source) {\n      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n      var frame = new _frame2.default();\n      frame.init(container, source, options);\n\n      // Apply global handlers to the frame\n      (0, _entries2.default)(this.globalHandlers).forEach(function (_ref) {\n        var _ref2 = (0, _slicedToArray3.default)(_ref, 2),\n            event = _ref2[0],\n            handler = _ref2[1];\n\n        var handlersArray = [].concat(handler);\n        handlersArray.forEach(function (eventHandler) {\n          // Add the given event handler to the frame.\n          if (typeof eventHandler === 'function') {\n            frame.on(event, eventHandler);\n          }\n        });\n      });\n      frame.mount();\n\n      return frame;\n    }\n  }]);\n  return Consumer;\n}();\n\nexports.default = Consumer;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/xfc/lib/consumer/consumer.js\n// module id = 84\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/is-iterable\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/is-iterable.js\n// module id = 85\n// module chunks = 0", "require('../modules/web.dom.iterable');\nrequire('../modules/es6.string.iterator');\nmodule.exports = require('../modules/core.is-iterable');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/is-iterable.js\n// module id = 86\n// module chunks = 0", "'use strict';\nvar addToUnscopables = require('./_add-to-unscopables');\nvar step = require('./_iter-step');\nvar Iterators = require('./_iterators');\nvar toIObject = require('./_to-iobject');\n\n// 22.1.3.4 Array.prototype.entries()\n// 22.1.3.13 Array.prototype.keys()\n// 22.1.3.29 Array.prototype.values()\n// 22.1.3.30 Array.prototype[@@iterator]()\nmodule.exports = require('./_iter-define')(Array, 'Array', function (iterated, kind) {\n  this._t = toIObject(iterated); // target\n  this._i = 0;                   // next index\n  this._k = kind;                // kind\n// 22.1.5.2.1 %ArrayIteratorPrototype%.next()\n}, function () {\n  var O = this._t;\n  var kind = this._k;\n  var index = this._i++;\n  if (!O || index >= O.length) {\n    this._t = undefined;\n    return step(1);\n  }\n  if (kind == 'keys') return step(0, index);\n  if (kind == 'values') return step(0, O[index]);\n  return step(0, [index, O[index]]);\n}, 'values');\n\n// argumentsList[@@iterator] is %ArrayProto_values% (9.4.4.6, 9.4.4.7)\nIterators.Arguments = Iterators.Array;\n\naddToUnscopables('keys');\naddToUnscopables('values');\naddToUnscopables('entries');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.array.iterator.js\n// module id = 87\n// module chunks = 0", "module.exports = function () { /* empty */ };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_add-to-unscopables.js\n// module id = 88\n// module chunks = 0", "module.exports = function (done, value) {\n  return { value: value, done: !!done };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iter-step.js\n// module id = 89\n// module chunks = 0", "'use strict';\nvar create = require('./_object-create');\nvar descriptor = require('./_property-desc');\nvar setToStringTag = require('./_set-to-string-tag');\nvar IteratorPrototype = {};\n\n// ********.1 %IteratorPrototype%[@@iterator]()\nrequire('./_hide')(IteratorPrototype, require('./_wks')('iterator'), function () { return this; });\n\nmodule.exports = function (Constructor, NAME, next) {\n  Constructor.prototype = create(IteratorPrototype, { next: descriptor(1, next) });\n  setToStringTag(Constructor, NAME + ' Iterator');\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_iter-create.js\n// module id = 90\n// module chunks = 0", "var dP = require('./_object-dp');\nvar anObject = require('./_an-object');\nvar getKeys = require('./_object-keys');\n\nmodule.exports = require('./_descriptors') ? Object.defineProperties : function defineProperties(O, Properties) {\n  anObject(O);\n  var keys = getKeys(Properties);\n  var length = keys.length;\n  var i = 0;\n  var P;\n  while (length > i) dP.f(O, P = keys[i++], Properties[P]);\n  return O;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-dps.js\n// module id = 91\n// module chunks = 0", "// false -> Array#indexOf\n// true  -> Array#includes\nvar toIObject = require('./_to-iobject');\nvar toLength = require('./_to-length');\nvar toAbsoluteIndex = require('./_to-absolute-index');\nmodule.exports = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIObject($this);\n    var length = toLength(O.length);\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare\n    if (IS_INCLUDES && el != el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare\n      if (value != value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) if (IS_INCLUDES || index in O) {\n      if (O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_array-includes.js\n// module id = 92\n// module chunks = 0", "var toInteger = require('./_to-integer');\nvar max = Math.max;\nvar min = Math.min;\nmodule.exports = function (index, length) {\n  index = toInteger(index);\n  return index < 0 ? max(index + length, 0) : min(index, length);\n};\n\n\n\n// WEBPACK FOOTER //\n// ./node_modules/core-js/library/modules/_to-absolute-index.js", "var toInteger = require('./_to-integer');\nvar defined = require('./_defined');\n// true  -> String#at\n// false -> String#codePointAt\nmodule.exports = function (TO_STRING) {\n  return function (that, pos) {\n    var s = String(defined(that));\n    var i = toInteger(pos);\n    var l = s.length;\n    var a, b;\n    if (i < 0 || i >= l) return TO_STRING ? '' : undefined;\n    a = s.charCodeAt(i);\n    return a < 0xd800 || a > 0xdbff || i + 1 === l || (b = s.charCodeAt(i + 1)) < 0xdc00 || b > 0xdfff\n      ? TO_STRING ? s.charAt(i) : a\n      : TO_STRING ? s.slice(i, i + 2) : (a - 0xd800 << 10) + (b - 0xdc00) + 0x10000;\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_string-at.js\n// module id = 94\n// module chunks = 0", "var classof = require('./_classof');\nvar ITERATOR = require('./_wks')('iterator');\nvar Iterators = require('./_iterators');\nmodule.exports = require('./_core').isIterable = function (it) {\n  var O = Object(it);\n  return O[ITERATOR] !== undefined\n    || '@@iterator' in O\n    // eslint-disable-next-line no-prototype-builtins\n    || Iterators.hasOwnProperty(classof(O));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/core.is-iterable.js\n// module id = 95\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/get-iterator\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/get-iterator.js\n// module id = 96\n// module chunks = 0", "require('../modules/web.dom.iterable');\nrequire('../modules/es6.string.iterator');\nmodule.exports = require('../modules/core.get-iterator');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/get-iterator.js\n// module id = 97\n// module chunks = 0", "var anObject = require('./_an-object');\nvar get = require('./core.get-iterator-method');\nmodule.exports = require('./_core').getIterator = function (it) {\n  var iterFn = get(it);\n  if (typeof iterFn != 'function') throw TypeError(it + ' is not iterable!');\n  return anObject(iterFn.call(it));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/core.get-iterator.js\n// module id = 98\n// module chunks = 0", "require('../../modules/es7.object.entries');\nmodule.exports = require('../../modules/_core').Object.entries;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/object/entries.js\n// module id = 99\n// module chunks = 0", "// https://github.com/tc39/proposal-object-values-entries\nvar $export = require('./_export');\nvar $entries = require('./_object-to-array')(true);\n\n$export($export.S, 'Object', {\n  entries: function entries(it) {\n    return $entries(it);\n  }\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es7.object.entries.js\n// module id = 100\n// module chunks = 0", "var DESCRIPTORS = require('./_descriptors');\nvar getKeys = require('./_object-keys');\nvar toIObject = require('./_to-iobject');\nvar isEnum = require('./_object-pie').f;\nmodule.exports = function (isEntries) {\n  return function (it) {\n    var O = toIObject(it);\n    var keys = getKeys(O);\n    var length = keys.length;\n    var i = 0;\n    var result = [];\n    var key;\n    while (length > i) {\n      key = keys[i++];\n      if (!DESCRIPTORS || isEnum.call(O, key)) {\n        result.push(isEntries ? [key, O[key]] : O[key]);\n      }\n    }\n    return result;\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-to-array.js\n// module id = 101\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/object/define-property\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/object/define-property.js\n// module id = 102\n// module chunks = 0", "require('../../modules/es6.object.define-property');\nvar $Object = require('../../modules/_core').Object;\nmodule.exports = function defineProperty(it, key, desc) {\n  return $Object.defineProperty(it, key, desc);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/object/define-property.js\n// module id = 103\n// module chunks = 0", "var $export = require('./_export');\n// ******** / ******** Object.defineProperty(O, P, Attributes)\n$export($export.S + $export.F * !require('./_descriptors'), 'Object', { defineProperty: require('./_object-dp').f });\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.object.define-property.js\n// module id = 104\n// module chunks = 0", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _slicedToArray2 = require('babel-runtime/helpers/slicedToArray');\n\nvar _slicedToArray3 = _interopRequireDefault(_slicedToArray2);\n\nvar _entries = require('babel-runtime/core-js/object/entries');\n\nvar _entries2 = _interopRequireDefault(_entries);\n\nvar _extends2 = require('babel-runtime/helpers/extends');\n\nvar _extends3 = _interopRequireDefault(_extends2);\n\nvar _promise = require('babel-runtime/core-js/promise');\n\nvar _promise2 = _interopRequireDefault(_promise);\n\nvar _getPrototypeOf = require('babel-runtime/core-js/object/get-prototype-of');\n\nvar _getPrototypeOf2 = _interopRequireDefault(_getPrototypeOf);\n\nvar _classCallCheck2 = require('babel-runtime/helpers/classCallCheck');\n\nvar _classCallCheck3 = _interopRequireDefault(_classCallCheck2);\n\nvar _createClass2 = require('babel-runtime/helpers/createClass');\n\nvar _createClass3 = _interopRequireDefault(_createClass2);\n\nvar _possibleConstructorReturn2 = require('babel-runtime/helpers/possibleConstructorReturn');\n\nvar _possibleConstructorReturn3 = _interopRequireDefault(_possibleConstructorReturn2);\n\nvar _inherits2 = require('babel-runtime/helpers/inherits');\n\nvar _inherits3 = _interopRequireDefault(_inherits2);\n\nvar _events = require('events');\n\nvar _logger = require('../lib/logger');\n\nvar _logger2 = _interopRequireDefault(_logger);\n\nvar _jsonrpcDispatch = require('jsonrpc-dispatch');\n\nvar _jsonrpcDispatch2 = _interopRequireDefault(_jsonrpcDispatch);\n\nvar _uri = require('../lib/uri');\n\nvar _uri2 = _interopRequireDefault(_uri);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * Application container class which represents an application frame hosting\n * an app on a 3rd party domain.\n */\nvar Frame = function (_EventEmitter) {\n  (0, _inherits3.default)(Frame, _EventEmitter);\n\n  function Frame(props) {\n    (0, _classCallCheck3.default)(this, Frame);\n\n    // Binds 'this' for methods called internally\n    var _this = (0, _possibleConstructorReturn3.default)(this, (Frame.__proto__ || (0, _getPrototypeOf2.default)(Frame)).call(this, props));\n\n    _this.handleProviderMessage = _this.handleProviderMessage.bind(_this);\n    _this.initIframeResizer = _this.initIframeResizer.bind(_this);\n    _this.send = _this.send.bind(_this);\n    _this.cleanup = _this.cleanup.bind(_this);\n    _this.load = _this.load.bind(_this);\n    return _this;\n  }\n\n  /**\n  * @param {object} container - The DOM node to append the application frame to.\n  * @param {string} source - The url source of the application\n  * @param {object} options - An optional parameter that contains a set of optional configs\n  */\n\n\n  (0, _createClass3.default)(Frame, [{\n    key: 'init',\n    value: function init(container, source) {\n      var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n          _ref$secret = _ref.secret,\n          secret = _ref$secret === undefined ? null : _ref$secret,\n          _ref$resizeConfig = _ref.resizeConfig,\n          resizeConfig = _ref$resizeConfig === undefined ? {} : _ref$resizeConfig,\n          _ref$iframeAttrs = _ref.iframeAttrs,\n          iframeAttrs = _ref$iframeAttrs === undefined ? {} : _ref$iframeAttrs;\n\n      this.source = source;\n      this.container = container;\n      this.iframe = null;\n      this.iframeAttrs = iframeAttrs;\n      this.wrapper = null;\n      this.origin = new _uri2.default(this.source).origin;\n      this.secret = secret;\n      this.resizeConfig = resizeConfig;\n\n      var self = this;\n      this.JSONRPC = new _jsonrpcDispatch2.default(self.send, {\n        launch: function launch() {\n          self.wrapper.setAttribute('data-status', 'launched');\n          self.emit('xfc.launched');\n          return _promise2.default.resolve();\n        },\n        authorized: function authorized() {\n          var detail = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n          self.wrapper.setAttribute('data-status', 'authorized');\n          self.emit('xfc.authorized', detail);\n          self.initIframeResizer();\n          return _promise2.default.resolve();\n        },\n        unload: function unload() {\n          var detail = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n          self.wrapper.setAttribute('data-status', 'unloaded');\n          self.emit('xfc.unload', detail);\n          return _promise2.default.resolve();\n        },\n        resize: function resize() {\n          var height = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n          var width = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n\n          if (typeof resizeConfig.customCalculationMethod === 'function') {\n            resizeConfig.customCalculationMethod.call(self.iframe);\n            return _promise2.default.resolve();\n          }\n\n          if (height) {\n            self.iframe.style.height = height;\n          }\n\n          if (width) {\n            self.iframe.style.width = width;\n          }\n          return _promise2.default.resolve();\n        },\n        event: function event(_event, detail) {\n          self.emit(_event, detail);\n          return _promise2.default.resolve();\n        },\n        authorizeConsumer: function authorizeConsumer() {\n          return _promise2.default.resolve('hello');\n        },\n        challengeConsumer: function challengeConsumer() {\n          return _promise2.default.resolve(self.secret);\n        },\n        loadPage: function loadPage(url) {\n          self.load(url);\n          return _promise2.default.resolve();\n        }\n      });\n    }\n  }, {\n    key: 'initIframeResizer',\n    value: function initIframeResizer() {\n      var config = this.resizeConfig;\n\n      // If user chooses to use fixedHeight or fixedWidth,\n      // set height/width to the specified value and keep unchanged.\n      if (config.fixedHeight || config.fixedWidth) {\n        if (config.fixedHeight) {\n          this.iframe.style.height = config.fixedHeight;\n        }\n        if (config.fixedWidth) {\n          this.iframe.style.width = config.fixedWidth;\n        }\n      } else {\n        // If user chooses to update iframe dynamically,\n        // replace customCalculationMethod by a boolean indicator\n        // in config because method is not transferrable.\n        if (typeof config.customCalculationMethod === 'function') {\n          config = (0, _extends3.default)({}, config);\n          config.customCal = true;\n          delete config.customCalculationMethod;\n        }\n        this.JSONRPC.notification('resize', [config]);\n      }\n    }\n\n    /**\n    * Mount this application onto its container and initiate resize sync.\n    */\n\n  }, {\n    key: 'mount',\n    value: function mount() {\n      if (this.iframe) return;\n\n      // Set up listener for all incoming communication\n      window.addEventListener('message', this.handleProviderMessage);\n\n      this.wrapper = document.createElement('div');\n      this.wrapper.className = 'xfc';\n      this.wrapper.setAttribute('data-status', 'mounted');\n      this.container.appendChild(this.wrapper);\n\n      var iframe = document.createElement('iframe');\n      iframe.src = this.source;\n      if (!this.resizeConfig.scrolling) {\n        iframe.style.overflow = 'hidden';\n        iframe.scrolling = 'no';\n      }\n\n      // Put custom attributes on iframe if specified\n      (0, _entries2.default)(this.iframeAttrs).forEach(function (_ref2) {\n        var _ref3 = (0, _slicedToArray3.default)(_ref2, 2),\n            key = _ref3[0],\n            value = _ref3[1];\n\n        iframe.setAttribute(key, value);\n      });\n\n      this.iframe = iframe;\n      this.wrapper.appendChild(iframe);\n\n      this.emit('xfc.mounted');\n    }\n\n    /**\n     * Unmount this application from its container\n     */\n\n  }, {\n    key: 'unmount',\n    value: function unmount() {\n      if (this.wrapper.parentNode === this.container) {\n        this.container.removeChild(this.wrapper);\n        this.emit('xfc.unmounted');\n        this.cleanup();\n      }\n    }\n\n    /**\n     * Cleans up unused message listeners and references of detached nodes\n     */\n\n  }, {\n    key: 'cleanup',\n    value: function cleanup() {\n      // Remove listener for all incoming communication\n      window.removeEventListener('message', this.handleProviderMessage);\n\n      // Sets references of detached nodes to null to avoid potential memory leak\n      this.iframe = null;\n      this.wrapper = null;\n    }\n\n    /**\n     * Loads a new page within existing frame.\n     * @param  {string} url - the URL of new page to load.\n     */\n\n  }, {\n    key: 'load',\n    value: function load(url) {\n      this.origin = new _uri2.default(url).origin;\n      this.source = url;\n      this.wrapper.setAttribute('data-status', 'mounted');\n      this.iframe.src = url; // Triggers the loading of new page\n    }\n\n    /**\n    * Handles an incoming message event by processing the JSONRPC request\n    * @param {object} event - The emitted message event.\n    */\n\n  }, {\n    key: 'handleProviderMessage',\n    value: function handleProviderMessage(event) {\n      // 1. This isn't a JSONRPC message or iframe is null, exit.\n      if (!event.data.jsonrpc || !this.iframe) return;\n\n      // 2. Identify the app the message came from.\n      if (this.iframe.contentWindow !== event.source) return;\n\n      // For Chrome, the origin property is in the event.originalEvent object.\n      // Update origin so it can be used to post back to this frame.\n      this.origin = event.origin || event.originalEvent.origin;\n\n      _logger2.default.log('<< consumer', event.origin, event.data);\n\n      // 3. Send a response, if any, back to the app.\n      this.JSONRPC.handle(event.data);\n    }\n\n    /**\n    * Post the given message to the application frame.\n    * @param {object} message - The message to post.\n    * See: https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage\n    */\n\n  }, {\n    key: 'send',\n    value: function send(message) {\n      if (message) {\n        _logger2.default.log('>> consumer', this.origin, message);\n        this.iframe.contentWindow.postMessage(message, this.origin);\n      }\n    }\n\n    /**\n    * Triggers an event within the embedded application.\n    * @param {string} event - The event name to trigger.\n    * @param {object} detail - The data context to send with the event.\n    */\n\n  }, {\n    key: 'trigger',\n    value: function trigger(event, detail) {\n      this.JSONRPC.notification('event', [event, detail]);\n    }\n  }]);\n  return Frame;\n}(_events.EventEmitter);\n\nexports.default = Frame;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/xfc/lib/consumer/frame.js\n// module id = 105\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/object/assign\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/object/assign.js\n// module id = 106\n// module chunks = 0", "require('../../modules/es6.object.assign');\nmodule.exports = require('../../modules/_core').Object.assign;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/object/assign.js\n// module id = 107\n// module chunks = 0", "// ******** Object.assign(target, source)\nvar $export = require('./_export');\n\n$export($export.S + $export.F, 'Object', { assign: require('./_object-assign') });\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.object.assign.js\n// module id = 108\n// module chunks = 0", "'use strict';\n// ******** Object.assign(target, source, ...)\nvar DESCRIPTORS = require('./_descriptors');\nvar getKeys = require('./_object-keys');\nvar gOPS = require('./_object-gops');\nvar pIE = require('./_object-pie');\nvar toObject = require('./_to-object');\nvar IObject = require('./_iobject');\nvar $assign = Object.assign;\n\n// should work with symbols and should have deterministic property order (V8 bug)\nmodule.exports = !$assign || require('./_fails')(function () {\n  var A = {};\n  var B = {};\n  // eslint-disable-next-line no-undef\n  var S = Symbol();\n  var K = 'abcdefghijklmnopqrst';\n  A[S] = 7;\n  K.split('').forEach(function (k) { B[k] = k; });\n  return $assign({}, A)[S] != 7 || Object.keys($assign({}, B)).join('') != K;\n}) ? function assign(target, source) { // eslint-disable-line no-unused-vars\n  var T = toObject(target);\n  var aLen = arguments.length;\n  var index = 1;\n  var getSymbols = gOPS.f;\n  var isEnum = pIE.f;\n  while (aLen > index) {\n    var S = IObject(arguments[index++]);\n    var keys = getSymbols ? getKeys(S).concat(getSymbols(S)) : getKeys(S);\n    var length = keys.length;\n    var j = 0;\n    var key;\n    while (length > j) {\n      key = keys[j++];\n      if (!DESCRIPTORS || isEnum.call(S, key)) T[key] = S[key];\n    }\n  } return T;\n} : $assign;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-assign.js\n// module id = 109\n// module chunks = 0", "require('../modules/es6.object.to-string');\nrequire('../modules/es6.string.iterator');\nrequire('../modules/web.dom.iterable');\nrequire('../modules/es6.promise');\nrequire('../modules/es7.promise.finally');\nrequire('../modules/es7.promise.try');\nmodule.exports = require('../modules/_core').Promise;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/promise.js\n// module id = 110\n// module chunks = 0", "'use strict';\nvar LIBRARY = require('./_library');\nvar global = require('./_global');\nvar ctx = require('./_ctx');\nvar classof = require('./_classof');\nvar $export = require('./_export');\nvar isObject = require('./_is-object');\nvar aFunction = require('./_a-function');\nvar anInstance = require('./_an-instance');\nvar forOf = require('./_for-of');\nvar speciesConstructor = require('./_species-constructor');\nvar task = require('./_task').set;\nvar microtask = require('./_microtask')();\nvar newPromiseCapabilityModule = require('./_new-promise-capability');\nvar perform = require('./_perform');\nvar userAgent = require('./_user-agent');\nvar promiseResolve = require('./_promise-resolve');\nvar PROMISE = 'Promise';\nvar TypeError = global.TypeError;\nvar process = global.process;\nvar versions = process && process.versions;\nvar v8 = versions && versions.v8 || '';\nvar $Promise = global[PROMISE];\nvar isNode = classof(process) == 'process';\nvar empty = function () { /* empty */ };\nvar Internal, newGenericPromiseCapability, OwnPromiseCapability, Wrapper;\nvar newPromiseCapability = newGenericPromiseCapability = newPromiseCapabilityModule.f;\n\nvar USE_NATIVE = !!function () {\n  try {\n    // correct subclassing with @@species support\n    var promise = $Promise.resolve(1);\n    var FakePromise = (promise.constructor = {})[require('./_wks')('species')] = function (exec) {\n      exec(empty, empty);\n    };\n    // unhandled rejections tracking support, NodeJS Promise without it fails @@species test\n    return (isNode || typeof PromiseRejectionEvent == 'function')\n      && promise.then(empty) instanceof FakePromise\n      // v8 6.6 (Node 10 and Chrome 66) have a bug with resolving custom thenables\n      // https://bugs.chromium.org/p/chromium/issues/detail?id=830565\n      // we can't detect it synchronously, so just check versions\n      && v8.indexOf('6.6') !== 0\n      && userAgent.indexOf('Chrome/66') === -1;\n  } catch (e) { /* empty */ }\n}();\n\n// helpers\nvar isThenable = function (it) {\n  var then;\n  return isObject(it) && typeof (then = it.then) == 'function' ? then : false;\n};\nvar notify = function (promise, isReject) {\n  if (promise._n) return;\n  promise._n = true;\n  var chain = promise._c;\n  microtask(function () {\n    var value = promise._v;\n    var ok = promise._s == 1;\n    var i = 0;\n    var run = function (reaction) {\n      var handler = ok ? reaction.ok : reaction.fail;\n      var resolve = reaction.resolve;\n      var reject = reaction.reject;\n      var domain = reaction.domain;\n      var result, then, exited;\n      try {\n        if (handler) {\n          if (!ok) {\n            if (promise._h == 2) onHandleUnhandled(promise);\n            promise._h = 1;\n          }\n          if (handler === true) result = value;\n          else {\n            if (domain) domain.enter();\n            result = handler(value); // may throw\n            if (domain) {\n              domain.exit();\n              exited = true;\n            }\n          }\n          if (result === reaction.promise) {\n            reject(TypeError('Promise-chain cycle'));\n          } else if (then = isThenable(result)) {\n            then.call(result, resolve, reject);\n          } else resolve(result);\n        } else reject(value);\n      } catch (e) {\n        if (domain && !exited) domain.exit();\n        reject(e);\n      }\n    };\n    while (chain.length > i) run(chain[i++]); // variable length - can't use forEach\n    promise._c = [];\n    promise._n = false;\n    if (isReject && !promise._h) onUnhandled(promise);\n  });\n};\nvar onUnhandled = function (promise) {\n  task.call(global, function () {\n    var value = promise._v;\n    var unhandled = isUnhandled(promise);\n    var result, handler, console;\n    if (unhandled) {\n      result = perform(function () {\n        if (isNode) {\n          process.emit('unhandledRejection', value, promise);\n        } else if (handler = global.onunhandledrejection) {\n          handler({ promise: promise, reason: value });\n        } else if ((console = global.console) && console.error) {\n          console.error('Unhandled promise rejection', value);\n        }\n      });\n      // Browsers should not trigger `rejectionHandled` event if it was handled here, NodeJS - should\n      promise._h = isNode || isUnhandled(promise) ? 2 : 1;\n    } promise._a = undefined;\n    if (unhandled && result.e) throw result.v;\n  });\n};\nvar isUnhandled = function (promise) {\n  return promise._h !== 1 && (promise._a || promise._c).length === 0;\n};\nvar onHandleUnhandled = function (promise) {\n  task.call(global, function () {\n    var handler;\n    if (isNode) {\n      process.emit('rejectionHandled', promise);\n    } else if (handler = global.onrejectionhandled) {\n      handler({ promise: promise, reason: promise._v });\n    }\n  });\n};\nvar $reject = function (value) {\n  var promise = this;\n  if (promise._d) return;\n  promise._d = true;\n  promise = promise._w || promise; // unwrap\n  promise._v = value;\n  promise._s = 2;\n  if (!promise._a) promise._a = promise._c.slice();\n  notify(promise, true);\n};\nvar $resolve = function (value) {\n  var promise = this;\n  var then;\n  if (promise._d) return;\n  promise._d = true;\n  promise = promise._w || promise; // unwrap\n  try {\n    if (promise === value) throw TypeError(\"Promise can't be resolved itself\");\n    if (then = isThenable(value)) {\n      microtask(function () {\n        var wrapper = { _w: promise, _d: false }; // wrap\n        try {\n          then.call(value, ctx($resolve, wrapper, 1), ctx($reject, wrapper, 1));\n        } catch (e) {\n          $reject.call(wrapper, e);\n        }\n      });\n    } else {\n      promise._v = value;\n      promise._s = 1;\n      notify(promise, false);\n    }\n  } catch (e) {\n    $reject.call({ _w: promise, _d: false }, e); // wrap\n  }\n};\n\n// constructor polyfill\nif (!USE_NATIVE) {\n  // 25.4.3.1 Promise(executor)\n  $Promise = function Promise(executor) {\n    anInstance(this, $Promise, PROMISE, '_h');\n    aFunction(executor);\n    Internal.call(this);\n    try {\n      executor(ctx($resolve, this, 1), ctx($reject, this, 1));\n    } catch (err) {\n      $reject.call(this, err);\n    }\n  };\n  // eslint-disable-next-line no-unused-vars\n  Internal = function Promise(executor) {\n    this._c = [];             // <- awaiting reactions\n    this._a = undefined;      // <- checked in isUnhandled reactions\n    this._s = 0;              // <- state\n    this._d = false;          // <- done\n    this._v = undefined;      // <- value\n    this._h = 0;              // <- rejection state, 0 - default, 1 - handled, 2 - unhandled\n    this._n = false;          // <- notify\n  };\n  Internal.prototype = require('./_redefine-all')($Promise.prototype, {\n    // 25.4.5.3 Promise.prototype.then(onFulfilled, onRejected)\n    then: function then(onFulfilled, onRejected) {\n      var reaction = newPromiseCapability(speciesConstructor(this, $Promise));\n      reaction.ok = typeof onFulfilled == 'function' ? onFulfilled : true;\n      reaction.fail = typeof onRejected == 'function' && onRejected;\n      reaction.domain = isNode ? process.domain : undefined;\n      this._c.push(reaction);\n      if (this._a) this._a.push(reaction);\n      if (this._s) notify(this, false);\n      return reaction.promise;\n    },\n    // 25.4.5.1 Promise.prototype.catch(onRejected)\n    'catch': function (onRejected) {\n      return this.then(undefined, onRejected);\n    }\n  });\n  OwnPromiseCapability = function () {\n    var promise = new Internal();\n    this.promise = promise;\n    this.resolve = ctx($resolve, promise, 1);\n    this.reject = ctx($reject, promise, 1);\n  };\n  newPromiseCapabilityModule.f = newPromiseCapability = function (C) {\n    return C === $Promise || C === Wrapper\n      ? new OwnPromiseCapability(C)\n      : newGenericPromiseCapability(C);\n  };\n}\n\n$export($export.G + $export.W + $export.F * !USE_NATIVE, { Promise: $Promise });\nrequire('./_set-to-string-tag')($Promise, PROMISE);\nrequire('./_set-species')(PROMISE);\nWrapper = require('./_core')[PROMISE];\n\n// statics\n$export($export.S + $export.F * !USE_NATIVE, PROMISE, {\n  // 25.4.4.5 Promise.reject(r)\n  reject: function reject(r) {\n    var capability = newPromiseCapability(this);\n    var $$reject = capability.reject;\n    $$reject(r);\n    return capability.promise;\n  }\n});\n$export($export.S + $export.F * (LIBRARY || !USE_NATIVE), PROMISE, {\n  // 25.4.4.6 Promise.resolve(x)\n  resolve: function resolve(x) {\n    return promiseResolve(LIBRARY && this === Wrapper ? $Promise : this, x);\n  }\n});\n$export($export.S + $export.F * !(USE_NATIVE && require('./_iter-detect')(function (iter) {\n  $Promise.all(iter)['catch'](empty);\n})), PROMISE, {\n  // 25.4.4.1 Promise.all(iterable)\n  all: function all(iterable) {\n    var C = this;\n    var capability = newPromiseCapability(C);\n    var resolve = capability.resolve;\n    var reject = capability.reject;\n    var result = perform(function () {\n      var values = [];\n      var index = 0;\n      var remaining = 1;\n      forOf(iterable, false, function (promise) {\n        var $index = index++;\n        var alreadyCalled = false;\n        values.push(undefined);\n        remaining++;\n        C.resolve(promise).then(function (value) {\n          if (alreadyCalled) return;\n          alreadyCalled = true;\n          values[$index] = value;\n          --remaining || resolve(values);\n        }, reject);\n      });\n      --remaining || resolve(values);\n    });\n    if (result.e) reject(result.v);\n    return capability.promise;\n  },\n  // 25.4.4.4 Promise.race(iterable)\n  race: function race(iterable) {\n    var C = this;\n    var capability = newPromiseCapability(C);\n    var reject = capability.reject;\n    var result = perform(function () {\n      forOf(iterable, false, function (promise) {\n        C.resolve(promise).then(capability.resolve, reject);\n      });\n    });\n    if (result.e) reject(result.v);\n    return capability.promise;\n  }\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.promise.js\n// module id = 111\n// module chunks = 0", "module.exports = function (it, Constructor, name, forbiddenField) {\n  if (!(it instanceof Constructor) || (forbiddenField !== undefined && forbiddenField in it)) {\n    throw TypeError(name + ': incorrect invocation!');\n  } return it;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_an-instance.js\n// module id = 112\n// module chunks = 0", "var ctx = require('./_ctx');\nvar call = require('./_iter-call');\nvar isArrayIter = require('./_is-array-iter');\nvar anObject = require('./_an-object');\nvar toLength = require('./_to-length');\nvar getIterFn = require('./core.get-iterator-method');\nvar BREAK = {};\nvar RETURN = {};\nvar exports = module.exports = function (iterable, entries, fn, that, ITERATOR) {\n  var iterFn = ITERATOR ? function () { return iterable; } : getIterFn(iterable);\n  var f = ctx(fn, that, entries ? 2 : 1);\n  var index = 0;\n  var length, step, iterator, result;\n  if (typeof iterFn != 'function') throw TypeError(iterable + ' is not iterable!');\n  // fast case for arrays with default iterator\n  if (isArrayIter(iterFn)) for (length = toLength(iterable.length); length > index; index++) {\n    result = entries ? f(anObject(step = iterable[index])[0], step[1]) : f(iterable[index]);\n    if (result === BREAK || result === RETURN) return result;\n  } else for (iterator = iterFn.call(iterable); !(step = iterator.next()).done;) {\n    result = call(iterator, f, step.value, entries);\n    if (result === BREAK || result === RETURN) return result;\n  }\n};\nexports.BREAK = BREAK;\nexports.RETURN = RETURN;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_for-of.js\n// module id = 113\n// module chunks = 0", "// fast apply, http://jsperf.lnkit.com/fast-apply/5\nmodule.exports = function (fn, args, that) {\n  var un = that === undefined;\n  switch (args.length) {\n    case 0: return un ? fn()\n                      : fn.call(that);\n    case 1: return un ? fn(args[0])\n                      : fn.call(that, args[0]);\n    case 2: return un ? fn(args[0], args[1])\n                      : fn.call(that, args[0], args[1]);\n    case 3: return un ? fn(args[0], args[1], args[2])\n                      : fn.call(that, args[0], args[1], args[2]);\n    case 4: return un ? fn(args[0], args[1], args[2], args[3])\n                      : fn.call(that, args[0], args[1], args[2], args[3]);\n  } return fn.apply(that, args);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_invoke.js\n// module id = 114\n// module chunks = 0", "var global = require('./_global');\nvar macrotask = require('./_task').set;\nvar Observer = global.MutationObserver || global.WebKitMutationObserver;\nvar process = global.process;\nvar Promise = global.Promise;\nvar isNode = require('./_cof')(process) == 'process';\n\nmodule.exports = function () {\n  var head, last, notify;\n\n  var flush = function () {\n    var parent, fn;\n    if (isNode && (parent = process.domain)) parent.exit();\n    while (head) {\n      fn = head.fn;\n      head = head.next;\n      try {\n        fn();\n      } catch (e) {\n        if (head) notify();\n        else last = undefined;\n        throw e;\n      }\n    } last = undefined;\n    if (parent) parent.enter();\n  };\n\n  // Node.js\n  if (isNode) {\n    notify = function () {\n      process.nextTick(flush);\n    };\n  // browsers with MutationObserver, except iOS Safari - https://github.com/zloirock/core-js/issues/339\n  } else if (Observer && !(global.navigator && global.navigator.standalone)) {\n    var toggle = true;\n    var node = document.createTextNode('');\n    new Observer(flush).observe(node, { characterData: true }); // eslint-disable-line no-new\n    notify = function () {\n      node.data = toggle = !toggle;\n    };\n  // environments with maybe non-completely correct, but existent Promise\n  } else if (Promise && Promise.resolve) {\n    // Promise.resolve without an argument throws an error in LG WebOS 2\n    var promise = Promise.resolve(undefined);\n    notify = function () {\n      promise.then(flush);\n    };\n  // for other environments - macrotask based on:\n  // - setImmediate\n  // - MessageChannel\n  // - window.postMessag\n  // - onreadystatechange\n  // - setTimeout\n  } else {\n    notify = function () {\n      // strange IE + webpack dev server bug - use .call(global)\n      macrotask.call(global, flush);\n    };\n  }\n\n  return function (fn) {\n    var task = { fn: fn, next: undefined };\n    if (last) last.next = task;\n    if (!head) {\n      head = task;\n      notify();\n    } last = task;\n  };\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_microtask.js\n// module id = 115\n// module chunks = 0", "var global = require('./_global');\nvar navigator = global.navigator;\n\nmodule.exports = navigator && navigator.userAgent || '';\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_user-agent.js\n// module id = 116\n// module chunks = 0", "var hide = require('./_hide');\nmodule.exports = function (target, src, safe) {\n  for (var key in src) {\n    if (safe && target[key]) target[key] = src[key];\n    else hide(target, key, src[key]);\n  } return target;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_redefine-all.js\n// module id = 117\n// module chunks = 0", "'use strict';\nvar global = require('./_global');\nvar core = require('./_core');\nvar dP = require('./_object-dp');\nvar DESCRIPTORS = require('./_descriptors');\nvar SPECIES = require('./_wks')('species');\n\nmodule.exports = function (KEY) {\n  var C = typeof core[KEY] == 'function' ? core[KEY] : global[KEY];\n  if (DESCRIPTORS && C && !C[SPECIES]) dP.f(C, SPECIES, {\n    configurable: true,\n    get: function () { return this; }\n  });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_set-species.js\n// module id = 118\n// module chunks = 0", "// https://github.com/tc39/proposal-promise-finally\n'use strict';\nvar $export = require('./_export');\nvar core = require('./_core');\nvar global = require('./_global');\nvar speciesConstructor = require('./_species-constructor');\nvar promiseResolve = require('./_promise-resolve');\n\n$export($export.P + $export.R, 'Promise', { 'finally': function (onFinally) {\n  var C = speciesConstructor(this, core.Promise || global.Promise);\n  var isFunction = typeof onFinally == 'function';\n  return this.then(\n    isFunction ? function (x) {\n      return promiseResolve(C, onFinally()).then(function () { return x; });\n    } : onFinally,\n    isFunction ? function (e) {\n      return promiseResolve(C, onFinally()).then(function () { throw e; });\n    } : onFinally\n  );\n} });\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es7.promise.finally.js\n// module id = 119\n// module chunks = 0", "'use strict';\n// https://github.com/tc39/proposal-promise-try\nvar $export = require('./_export');\nvar newPromiseCapability = require('./_new-promise-capability');\nvar perform = require('./_perform');\n\n$export($export.S, 'Promise', { 'try': function (callbackfn) {\n  var promiseCapability = newPromiseCapability.f(this);\n  var result = perform(callbackfn);\n  (result.e ? promiseCapability.reject : promiseCapability.resolve)(result.v);\n  return promiseCapability.promise;\n} });\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es7.promise.try.js\n// module id = 120\n// module chunks = 0", "require('../../modules/es6.object.get-prototype-of');\nmodule.exports = require('../../modules/_core').Object.getPrototypeOf;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/object/get-prototype-of.js\n// module id = 121\n// module chunks = 0", "// 19.1.2.9 Object.getPrototypeOf(O)\nvar toObject = require('./_to-object');\nvar $getPrototypeOf = require('./_object-gpo');\n\nrequire('./_object-sap')('getPrototypeOf', function () {\n  return function getPrototypeOf(it) {\n    return $getPrototypeOf(toObject(it));\n  };\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.object.get-prototype-of.js\n// module id = 122\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/symbol/iterator\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/symbol/iterator.js\n// module id = 123\n// module chunks = 0", "require('../../modules/es6.string.iterator');\nrequire('../../modules/web.dom.iterable');\nmodule.exports = require('../../modules/_wks-ext').f('iterator');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/symbol/iterator.js\n// module id = 124\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/symbol\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/symbol.js\n// module id = 125\n// module chunks = 0", "require('../../modules/es6.symbol');\nrequire('../../modules/es6.object.to-string');\nrequire('../../modules/es7.symbol.async-iterator');\nrequire('../../modules/es7.symbol.observable');\nmodule.exports = require('../../modules/_core').Symbol;\n\n\n\n// WEBPACK FOOTER //\n// ./node_modules/core-js/library/fn/symbol/index.js", "'use strict';\n// ECMAScript 6 symbols shim\nvar global = require('./_global');\nvar has = require('./_has');\nvar DESCRIPTORS = require('./_descriptors');\nvar $export = require('./_export');\nvar redefine = require('./_redefine');\nvar META = require('./_meta').KEY;\nvar $fails = require('./_fails');\nvar shared = require('./_shared');\nvar setToStringTag = require('./_set-to-string-tag');\nvar uid = require('./_uid');\nvar wks = require('./_wks');\nvar wksExt = require('./_wks-ext');\nvar wksDefine = require('./_wks-define');\nvar enumKeys = require('./_enum-keys');\nvar isArray = require('./_is-array');\nvar anObject = require('./_an-object');\nvar isObject = require('./_is-object');\nvar toObject = require('./_to-object');\nvar toIObject = require('./_to-iobject');\nvar toPrimitive = require('./_to-primitive');\nvar createDesc = require('./_property-desc');\nvar _create = require('./_object-create');\nvar gOPNExt = require('./_object-gopn-ext');\nvar $GOPD = require('./_object-gopd');\nvar $GOPS = require('./_object-gops');\nvar $DP = require('./_object-dp');\nvar $keys = require('./_object-keys');\nvar gOPD = $GOPD.f;\nvar dP = $DP.f;\nvar gOPN = gOPNExt.f;\nvar $Symbol = global.Symbol;\nvar $JSON = global.JSON;\nvar _stringify = $JSON && $JSON.stringify;\nvar PROTOTYPE = 'prototype';\nvar HIDDEN = wks('_hidden');\nvar TO_PRIMITIVE = wks('toPrimitive');\nvar isEnum = {}.propertyIsEnumerable;\nvar SymbolRegistry = shared('symbol-registry');\nvar AllSymbols = shared('symbols');\nvar OPSymbols = shared('op-symbols');\nvar ObjectProto = Object[PROTOTYPE];\nvar USE_NATIVE = typeof $Symbol == 'function' && !!$GOPS.f;\nvar QObject = global.QObject;\n// Don't use setters in Qt Script, https://github.com/zloirock/core-js/issues/173\nvar setter = !QObject || !QObject[PROTOTYPE] || !QObject[PROTOTYPE].findChild;\n\n// fallback for old Android, https://code.google.com/p/v8/issues/detail?id=687\nvar setSymbolDesc = DESCRIPTORS && $fails(function () {\n  return _create(dP({}, 'a', {\n    get: function () { return dP(this, 'a', { value: 7 }).a; }\n  })).a != 7;\n}) ? function (it, key, D) {\n  var protoDesc = gOPD(ObjectProto, key);\n  if (protoDesc) delete ObjectProto[key];\n  dP(it, key, D);\n  if (protoDesc && it !== ObjectProto) dP(ObjectProto, key, protoDesc);\n} : dP;\n\nvar wrap = function (tag) {\n  var sym = AllSymbols[tag] = _create($Symbol[PROTOTYPE]);\n  sym._k = tag;\n  return sym;\n};\n\nvar isSymbol = USE_NATIVE && typeof $Symbol.iterator == 'symbol' ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  return it instanceof $Symbol;\n};\n\nvar $defineProperty = function defineProperty(it, key, D) {\n  if (it === ObjectProto) $defineProperty(OPSymbols, key, D);\n  anObject(it);\n  key = toPrimitive(key, true);\n  anObject(D);\n  if (has(AllSymbols, key)) {\n    if (!D.enumerable) {\n      if (!has(it, HIDDEN)) dP(it, HIDDEN, createDesc(1, {}));\n      it[HIDDEN][key] = true;\n    } else {\n      if (has(it, HIDDEN) && it[HIDDEN][key]) it[HIDDEN][key] = false;\n      D = _create(D, { enumerable: createDesc(0, false) });\n    } return setSymbolDesc(it, key, D);\n  } return dP(it, key, D);\n};\nvar $defineProperties = function defineProperties(it, P) {\n  anObject(it);\n  var keys = enumKeys(P = toIObject(P));\n  var i = 0;\n  var l = keys.length;\n  var key;\n  while (l > i) $defineProperty(it, key = keys[i++], P[key]);\n  return it;\n};\nvar $create = function create(it, P) {\n  return P === undefined ? _create(it) : $defineProperties(_create(it), P);\n};\nvar $propertyIsEnumerable = function propertyIsEnumerable(key) {\n  var E = isEnum.call(this, key = toPrimitive(key, true));\n  if (this === ObjectProto && has(AllSymbols, key) && !has(OPSymbols, key)) return false;\n  return E || !has(this, key) || !has(AllSymbols, key) || has(this, HIDDEN) && this[HIDDEN][key] ? E : true;\n};\nvar $getOwnPropertyDescriptor = function getOwnPropertyDescriptor(it, key) {\n  it = toIObject(it);\n  key = toPrimitive(key, true);\n  if (it === ObjectProto && has(AllSymbols, key) && !has(OPSymbols, key)) return;\n  var D = gOPD(it, key);\n  if (D && has(AllSymbols, key) && !(has(it, HIDDEN) && it[HIDDEN][key])) D.enumerable = true;\n  return D;\n};\nvar $getOwnPropertyNames = function getOwnPropertyNames(it) {\n  var names = gOPN(toIObject(it));\n  var result = [];\n  var i = 0;\n  var key;\n  while (names.length > i) {\n    if (!has(AllSymbols, key = names[i++]) && key != HIDDEN && key != META) result.push(key);\n  } return result;\n};\nvar $getOwnPropertySymbols = function getOwnPropertySymbols(it) {\n  var IS_OP = it === ObjectProto;\n  var names = gOPN(IS_OP ? OPSymbols : toIObject(it));\n  var result = [];\n  var i = 0;\n  var key;\n  while (names.length > i) {\n    if (has(AllSymbols, key = names[i++]) && (IS_OP ? has(ObjectProto, key) : true)) result.push(AllSymbols[key]);\n  } return result;\n};\n\n// ******** Symbol([description])\nif (!USE_NATIVE) {\n  $Symbol = function Symbol() {\n    if (this instanceof $Symbol) throw TypeError('Symbol is not a constructor!');\n    var tag = uid(arguments.length > 0 ? arguments[0] : undefined);\n    var $set = function (value) {\n      if (this === ObjectProto) $set.call(OPSymbols, value);\n      if (has(this, HIDDEN) && has(this[HIDDEN], tag)) this[HIDDEN][tag] = false;\n      setSymbolDesc(this, tag, createDesc(1, value));\n    };\n    if (DESCRIPTORS && setter) setSymbolDesc(ObjectProto, tag, { configurable: true, set: $set });\n    return wrap(tag);\n  };\n  redefine($Symbol[PROTOTYPE], 'toString', function toString() {\n    return this._k;\n  });\n\n  $GOPD.f = $getOwnPropertyDescriptor;\n  $DP.f = $defineProperty;\n  require('./_object-gopn').f = gOPNExt.f = $getOwnPropertyNames;\n  require('./_object-pie').f = $propertyIsEnumerable;\n  $GOPS.f = $getOwnPropertySymbols;\n\n  if (DESCRIPTORS && !require('./_library')) {\n    redefine(ObjectProto, 'propertyIsEnumerable', $propertyIsEnumerable, true);\n  }\n\n  wksExt.f = function (name) {\n    return wrap(wks(name));\n  };\n}\n\n$export($export.G + $export.W + $export.F * !USE_NATIVE, { Symbol: $Symbol });\n\nfor (var es6Symbols = (\n  // ********, ********, ********, ********, ********, ********, *********, *********, *********, *********, *********\n  'hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables'\n).split(','), j = 0; es6Symbols.length > j;)wks(es6Symbols[j++]);\n\nfor (var wellKnownSymbols = $keys(wks.store), k = 0; wellKnownSymbols.length > k;) wksDefine(wellKnownSymbols[k++]);\n\n$export($export.S + $export.F * !USE_NATIVE, 'Symbol', {\n  // ******** Symbol.for(key)\n  'for': function (key) {\n    return has(SymbolRegistry, key += '')\n      ? SymbolRegistry[key]\n      : SymbolRegistry[key] = $Symbol(key);\n  },\n  // ******** Symbol.keyFor(sym)\n  keyFor: function keyFor(sym) {\n    if (!isSymbol(sym)) throw TypeError(sym + ' is not a symbol!');\n    for (var key in SymbolRegistry) if (SymbolRegistry[key] === sym) return key;\n  },\n  useSetter: function () { setter = true; },\n  useSimple: function () { setter = false; }\n});\n\n$export($export.S + $export.F * !USE_NATIVE, 'Object', {\n  // ******** Object.create(O [, Properties])\n  create: $create,\n  // ******** Object.defineProperty(O, P, Attributes)\n  defineProperty: $defineProperty,\n  // ******** Object.defineProperties(O, Properties)\n  defineProperties: $defineProperties,\n  // ******** Object.getOwnPropertyDescriptor(O, P)\n  getOwnPropertyDescriptor: $getOwnPropertyDescriptor,\n  // ******** Object.getOwnPropertyNames(O)\n  getOwnPropertyNames: $getOwnPropertyNames,\n  // ******** Object.getOwnPropertySymbols(O)\n  getOwnPropertySymbols: $getOwnPropertySymbols\n});\n\n// Chrome 38 and 39 `Object.getOwnPropertySymbols` fails on primitives\n// https://bugs.chromium.org/p/v8/issues/detail?id=3443\nvar FAILS_ON_PRIMITIVES = $fails(function () { $GOPS.f(1); });\n\n$export($export.S + $export.F * FAILS_ON_PRIMITIVES, 'Object', {\n  getOwnPropertySymbols: function getOwnPropertySymbols(it) {\n    return $GOPS.f(toObject(it));\n  }\n});\n\n// 24.3.2 JSON.stringify(value [, replacer [, space]])\n$JSON && $export($export.S + $export.F * (!USE_NATIVE || $fails(function () {\n  var S = $Symbol();\n  // MS Edge converts symbol values to JSON as {}\n  // WebKit converts symbol values to JSON as null\n  // V8 throws on boxed symbols\n  return _stringify([S]) != '[null]' || _stringify({ a: S }) != '{}' || _stringify(Object(S)) != '{}';\n})), 'JSON', {\n  stringify: function stringify(it) {\n    var args = [it];\n    var i = 1;\n    var replacer, $replacer;\n    while (arguments.length > i) args.push(arguments[i++]);\n    $replacer = replacer = args[1];\n    if (!isObject(replacer) && it === undefined || isSymbol(it)) return; // IE8 returns string on undefined\n    if (!isArray(replacer)) replacer = function (key, value) {\n      if (typeof $replacer == 'function') value = $replacer.call(this, key, value);\n      if (!isSymbol(value)) return value;\n    };\n    args[1] = replacer;\n    return _stringify.apply($JSON, args);\n  }\n});\n\n// 19.4.3.4 Symbol.prototype[@@toPrimitive](hint)\n$Symbol[PROTOTYPE][TO_PRIMITIVE] || require('./_hide')($Symbol[PROTOTYPE], TO_PRIMITIVE, $Symbol[PROTOTYPE].valueOf);\n// 19.4.3.5 Symbol.prototype[@@toStringTag]\nsetToStringTag($Symbol, 'Symbol');\n// 20.2.1.9 Math[@@toStringTag]\nsetToStringTag(Math, 'Math', true);\n// 24.3.3 JSON[@@toStringTag]\nsetToStringTag(global.JSON, 'JSON', true);\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.symbol.js\n// module id = 127\n// module chunks = 0", "// all enumerable object keys, includes symbols\nvar getKeys = require('./_object-keys');\nvar gOPS = require('./_object-gops');\nvar pIE = require('./_object-pie');\nmodule.exports = function (it) {\n  var result = getKeys(it);\n  var getSymbols = gOPS.f;\n  if (getSymbols) {\n    var symbols = getSymbols(it);\n    var isEnum = pIE.f;\n    var i = 0;\n    var key;\n    while (symbols.length > i) if (isEnum.call(it, key = symbols[i++])) result.push(key);\n  } return result;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_enum-keys.js\n// module id = 128\n// module chunks = 0", "// 7.2.2 IsArray(argument)\nvar cof = require('./_cof');\nmodule.exports = Array.isArray || function isArray(arg) {\n  return cof(arg) == 'Array';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_is-array.js\n// module id = 129\n// module chunks = 0", "// fallback for IE11 buggy Object.getOwnPropertyNames with iframe and window\nvar toIObject = require('./_to-iobject');\nvar gOPN = require('./_object-gopn').f;\nvar toString = {}.toString;\n\nvar windowNames = typeof window == 'object' && window && Object.getOwnPropertyNames\n  ? Object.getOwnPropertyNames(window) : [];\n\nvar getWindowNames = function (it) {\n  try {\n    return gOPN(it);\n  } catch (e) {\n    return windowNames.slice();\n  }\n};\n\nmodule.exports.f = function getOwnPropertyNames(it) {\n  return windowNames && toString.call(it) == '[object Window]' ? getWindowNames(it) : gOPN(toIObject(it));\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_object-gopn-ext.js\n// module id = 130\n// module chunks = 0", "require('./_wks-define')('asyncIterator');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es7.symbol.async-iterator.js\n// module id = 131\n// module chunks = 0", "require('./_wks-define')('observable');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es7.symbol.observable.js\n// module id = 132\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/object/set-prototype-of\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/object/set-prototype-of.js\n// module id = 133\n// module chunks = 0", "require('../../modules/es6.object.set-prototype-of');\nmodule.exports = require('../../modules/_core').Object.setPrototypeOf;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/object/set-prototype-of.js\n// module id = 134\n// module chunks = 0", "// ********9 Object.setPrototypeOf(O, proto)\nvar $export = require('./_export');\n$export($export.S, 'Object', { setPrototypeOf: require('./_set-proto').set });\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.object.set-prototype-of.js\n// module id = 135\n// module chunks = 0", "// Works with __proto__ only. Old v8 can't work with null proto objects.\n/* eslint-disable no-proto */\nvar isObject = require('./_is-object');\nvar anObject = require('./_an-object');\nvar check = function (O, proto) {\n  anObject(O);\n  if (!isObject(proto) && proto !== null) throw TypeError(proto + \": can't set as prototype!\");\n};\nmodule.exports = {\n  set: Object.setPrototypeOf || ('__proto__' in {} ? // eslint-disable-line\n    function (test, buggy, set) {\n      try {\n        set = require('./_ctx')(Function.call, require('./_object-gopd').f(Object.prototype, '__proto__').set, 2);\n        set(test, []);\n        buggy = !(test instanceof Array);\n      } catch (e) { buggy = true; }\n      return function setPrototypeOf(O, proto) {\n        check(O, proto);\n        if (buggy) O.__proto__ = proto;\n        else set(O, proto);\n        return O;\n      };\n    }({}, false) : undefined),\n  check: check\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_set-proto.js\n// module id = 136\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/object/create\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/object/create.js\n// module id = 137\n// module chunks = 0", "require('../../modules/es6.object.create');\nvar $Object = require('../../modules/_core').Object;\nmodule.exports = function create(P, D) {\n  return $Object.create(P, D);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/object/create.js\n// module id = 138\n// module chunks = 0", "var $export = require('./_export');\n// ******** / 15.2.3.5 Object.create(O [, Properties])\n$export($export.S, 'Object', { create: require('./_object-create') });\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.object.create.js\n// module id = 139\n// module chunks = 0", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/process/browser.js\n// module id = 140\n// module chunks = 0", "require('../../modules/es6.object.freeze');\nmodule.exports = require('../../modules/_core').Object.freeze;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/object/freeze.js\n// module id = 141\n// module chunks = 0", "// 19.1.2.5 Object.freeze(O)\nvar isObject = require('./_is-object');\nvar meta = require('./_meta').onFreeze;\n\nrequire('./_object-sap')('freeze', function ($freeze) {\n  return function freeze(it) {\n    return $freeze && isObject(it) ? $freeze(meta(it)) : it;\n  };\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.object.freeze.js\n// module id = 142\n// module chunks = 0", "var v1 = require('./v1');\nvar v4 = require('./v4');\n\nvar uuid = v4;\nuuid.v1 = v1;\nuuid.v4 = v4;\n\nmodule.exports = uuid;\n\n\n\n// WEBPACK FOOTER //\n// ./node_modules/uuid/index.js", "var rng = require('./lib/rng');\nvar bytesToUuid = require('./lib/bytesToUuid');\n\n// **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\n\nvar _nodeId;\nvar _clockseq;\n\n// Previous uuid creation time\nvar _lastMSecs = 0;\nvar _lastNSecs = 0;\n\n// See https://github.com/uuidjs/uuid for API details\nfunction v1(options, buf, offset) {\n  var i = buf && offset || 0;\n  var b = buf || [];\n\n  options = options || {};\n  var node = options.node || _nodeId;\n  var clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq;\n\n  // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n  if (node == null || clockseq == null) {\n    var seedBytes = rng();\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [\n        seedBytes[0] | 0x01,\n        seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]\n      ];\n    }\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  }\n\n  // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n  var msecs = options.msecs !== undefined ? options.msecs : new Date().getTime();\n\n  // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n  var nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1;\n\n  // Time since last uuid creation (in msecs)\n  var dt = (msecs - _lastMSecs) + (nsecs - _lastNSecs)/10000;\n\n  // Per 4.2.1.2, Bump clockseq on clock regression\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  }\n\n  // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  }\n\n  // Per 4.2.1.2 Throw error if too many uuids are requested\n  if (nsecs >= 10000) {\n    throw new Error('uuid.v1(): Can\\'t create more than 10M uuids/sec');\n  }\n\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq;\n\n  // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n  msecs += 12219292800000;\n\n  // `time_low`\n  var tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff;\n\n  // `time_mid`\n  var tmh = (msecs / 0x100000000 * 10000) & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff;\n\n  // `time_high_and_version`\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n  b[i++] = tmh >>> 16 & 0xff;\n\n  // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n  b[i++] = clockseq >>> 8 | 0x80;\n\n  // `clock_seq_low`\n  b[i++] = clockseq & 0xff;\n\n  // `node`\n  for (var n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n\n  return buf ? buf : bytesToUuid(b);\n}\n\nmodule.exports = v1;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/uuid/v1.js\n// module id = 144\n// module chunks = 0", "var rng = require('./lib/rng');\nvar bytesToUuid = require('./lib/bytesToUuid');\n\nfunction v4(options, buf, offset) {\n  var i = buf && offset || 0;\n\n  if (typeof(options) == 'string') {\n    buf = options === 'binary' ? new Array(16) : null;\n    options = null;\n  }\n  options = options || {};\n\n  var rnds = options.random || (options.rng || rng)();\n\n  // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n  rnds[6] = (rnds[6] & 0x0f) | 0x40;\n  rnds[8] = (rnds[8] & 0x3f) | 0x80;\n\n  // Copy bytes to buffer, if provided\n  if (buf) {\n    for (var ii = 0; ii < 16; ++ii) {\n      buf[i + ii] = rnds[ii];\n    }\n  }\n\n  return buf || bytesToUuid(rnds);\n}\n\nmodule.exports = v4;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/uuid/v4.js\n// module id = 145\n// module chunks = 0", "'use strict';\n\nvar _freeze = require('babel-runtime/core-js/object/freeze');\n\nvar _freeze2 = _interopRequireDefault(_freeze);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// http://www.jsonrpc.org/specification#error_object\nvar PARSE_ERROR = (0, _freeze2.default)({ message: 'Parse error', code: -32700 });\nvar INVALID_REQUEST = (0, _freeze2.default)({ message: 'Invalid request', code: -32600 });\nvar METHOD_NOT_FOUND = (0, _freeze2.default)({ message: 'Method not found', code: -32601 });\nvar INVALID_PARAMS = (0, _freeze2.default)({ message: 'Invalid params', code: -32602 });\nvar INTERNAL_ERROR = (0, _freeze2.default)({ message: 'Internal error', code: -32603 });\n\nmodule.exports = (0, _freeze2.default)({\n  PARSE_ERROR: PARSE_ERROR,\n  INVALID_REQUEST: INVALID_REQUEST,\n  METHOD_NOT_FOUND: METHOD_NOT_FOUND,\n  INVALID_PARAMS: INVALID_PARAMS,\n  INTERNAL_ERROR: INTERNAL_ERROR\n});\n\n\n// WEBPACK FOOTER //\n// ./node_modules/jsonrpc-dispatch/lib/src/errors.js", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _provider = require('./provider');\n\nvar _provider2 = _interopRequireDefault(_provider);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = new _provider2.default();\n\n\n// WEBPACK FOOTER //\n// ./node_modules/xfc/lib/provider/index.js", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _classCallCheck2 = require('babel-runtime/helpers/classCallCheck');\n\nvar _classCallCheck3 = _interopRequireDefault(_classCallCheck2);\n\nvar _createClass2 = require('babel-runtime/helpers/createClass');\n\nvar _createClass3 = _interopRequireDefault(_createClass2);\n\nvar _application = require('./application');\n\nvar _application2 = _interopRequireDefault(_application);\n\nvar _logger = require('../lib/logger');\n\nvar _logger2 = _interopRequireDefault(_logger);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar Provider = function () {\n  function Provider() {\n    (0, _classCallCheck3.default)(this, Provider);\n  }\n\n  (0, _createClass3.default)(Provider, [{\n    key: 'init',\n    value: function init(config) {\n      var enforceSecurity = config.secret || config.acls.some(function (x) {\n        return x !== '*';\n      });\n\n      // Set hidden attribute with script if not present and security is being enforced\n      if (enforceSecurity && window.self !== window.top && !(document.documentElement.hasAttribute && document.documentElement.hasAttribute('hidden'))) {\n        document.documentElement.setAttribute('hidden', null);\n\n        // WARNING: Setting hidden attribute with script can be countered by\n        // hackers using iframe sandbox attribute OR a frame busting technique.\n        _logger2.default.warn('Security warning: Hidden attribute not detected on document and has been added.');\n      }\n\n      this.application = new _application2.default();\n      this.application.init(config);\n      this.application.launch();\n    }\n  }, {\n    key: 'on',\n    value: function on(eventName, listener) {\n      this.application.on(eventName, listener);\n    }\n  }, {\n    key: 'fullscreen',\n    value: function fullscreen(source) {\n      this.application.fullscreen(source);\n    }\n  }, {\n    key: 'httpError',\n    value: function httpError(error) {\n      this.application.httpError(error);\n    }\n  }, {\n    key: 'trigger',\n    value: function trigger(event, detail) {\n      this.application.trigger(event, detail);\n    }\n  }, {\n    key: 'loadPage',\n    value: function loadPage(url) {\n      this.application.loadPage(url);\n    }\n  }]);\n  return Provider;\n}();\n\nexports.default = Provider;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/xfc/lib/provider/provider.js\n// module id = 148\n// module chunks = 0", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _toConsumableArray2 = require('babel-runtime/helpers/toConsumableArray');\n\nvar _toConsumableArray3 = _interopRequireDefault(_toConsumableArray2);\n\nvar _promise = require('babel-runtime/core-js/promise');\n\nvar _promise2 = _interopRequireDefault(_promise);\n\nvar _getPrototypeOf = require('babel-runtime/core-js/object/get-prototype-of');\n\nvar _getPrototypeOf2 = _interopRequireDefault(_getPrototypeOf);\n\nvar _classCallCheck2 = require('babel-runtime/helpers/classCallCheck');\n\nvar _classCallCheck3 = _interopRequireDefault(_classCallCheck2);\n\nvar _createClass2 = require('babel-runtime/helpers/createClass');\n\nvar _createClass3 = _interopRequireDefault(_createClass2);\n\nvar _possibleConstructorReturn2 = require('babel-runtime/helpers/possibleConstructorReturn');\n\nvar _possibleConstructorReturn3 = _interopRequireDefault(_possibleConstructorReturn2);\n\nvar _inherits2 = require('babel-runtime/helpers/inherits');\n\nvar _inherits3 = _interopRequireDefault(_inherits2);\n\nvar _jsonrpcDispatch = require('jsonrpc-dispatch');\n\nvar _jsonrpcDispatch2 = _interopRequireDefault(_jsonrpcDispatch);\n\nvar _string = require('../lib/string');\n\nvar _events = require('events');\n\nvar _uri = require('../lib/uri');\n\nvar _uri2 = _interopRequireDefault(_uri);\n\nvar _logger = require('../lib/logger');\n\nvar _logger2 = _interopRequireDefault(_logger);\n\nvar _dimension = require('../lib/dimension');\n\nvar _mutationObserver = require('mutation-observer');\n\nvar _mutationObserver2 = _interopRequireDefault(_mutationObserver);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/** Application class which represents an embedded application. */\nvar Application = function (_EventEmitter) {\n  (0, _inherits3.default)(Application, _EventEmitter);\n\n  function Application() {\n    (0, _classCallCheck3.default)(this, Application);\n    return (0, _possibleConstructorReturn3.default)(this, (Application.__proto__ || (0, _getPrototypeOf2.default)(Application)).apply(this, arguments));\n  }\n\n  (0, _createClass3.default)(Application, [{\n    key: 'init',\n\n    /**\n     * init method\n     * @param  options.acls            An array that contains white listed origins\n     * @param  options.secret          A string or function used for authorization with Consumer\n     * @param  options.onReady         A function that will be called after App is authorized\n     * @param  options.targetSelectors A DOMString containing one or more selectors to match against.\n     *                                 This string must be a valid CSS selector string; if it's not,\n     *                                 a SyntaxError exception is thrown.\n     */\n    value: function init(_ref) {\n      var _ref$acls = _ref.acls,\n          acls = _ref$acls === undefined ? [] : _ref$acls,\n          _ref$secret = _ref.secret,\n          secret = _ref$secret === undefined ? null : _ref$secret,\n          _ref$onReady = _ref.onReady,\n          onReady = _ref$onReady === undefined ? null : _ref$onReady,\n          _ref$targetSelectors = _ref.targetSelectors,\n          targetSelectors = _ref$targetSelectors === undefined ? '' : _ref$targetSelectors;\n\n      this.acls = [].concat(acls);\n      this.secret = secret;\n      this.onReady = onReady;\n      this.targetSelectors = targetSelectors;\n      this.resizeConfig = null;\n      this.requestResize = this.requestResize.bind(this);\n      this.handleConsumerMessage = this.handleConsumerMessage.bind(this);\n      this.authorizeConsumer = this.authorizeConsumer.bind(this);\n      this.verifyChallenge = this.verifyChallenge.bind(this);\n      this.emitError = this.emitError.bind(this);\n      this.unload = this.unload.bind(this);\n\n      // Resize for slow loading images\n      document.addEventListener('load', this.imageRequestResize.bind(this), true);\n\n      var self = this;\n      this.JSONRPC = new _jsonrpcDispatch2.default(self.send.bind(self), {\n        event: function event(_event, detail) {\n          self.emit(_event, detail);\n          return _promise2.default.resolve();\n        },\n        resize: function resize() {\n          var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n          self.resizeConfig = config;\n\n          self.requestResize();\n\n          // Registers a mutation observer for body\n          var observer = new _mutationObserver2.default(function (mutations) {\n            return self.requestResize();\n          });\n          observer.observe(document.body, { attributes: true, childList: true, characterData: true, subtree: true });\n\n          // Registers a listener to window.onresize\n          // Optimizes the listener by debouncing (https://bencentra.com/code/2015/02/27/optimizing-window-resize.html#debouncing)\n          var interval = 100; // Resize event will be considered complete if no follow-up events within `interval` ms.\n          var resizeTimer = null;\n          window.onresize = function (event) {\n            clearTimeout(resizeTimer);\n            resizeTimer = setTimeout(function () {\n              return self.requestResize();\n            }, interval);\n          };\n\n          return _promise2.default.resolve();\n        }\n      });\n    }\n\n    /**\n     * imageRequestResize function to call requestResize event for slow loading image\n     * @param {object} event - event which triggered the listener\n     */\n\n  }, {\n    key: 'imageRequestResize',\n    value: function imageRequestResize(event) {\n      var tgt = event.target;\n      if (tgt.tagName === 'IMG' && !(tgt.hasAttribute('height') || tgt.hasAttribute('width'))) {\n        this.requestResize();\n      }\n    }\n  }, {\n    key: 'requestResize',\n    value: function requestResize() {\n      if (!this.resizeConfig) return;\n      if (this.resizeConfig.customCal) {\n        this.JSONRPC.notification('resize');\n      } else if (this.resizeConfig.autoResizeWidth) {\n        var width = (0, _dimension.calculateWidth)(this.resizeConfig.WidthCalculationMethod);\n        this.JSONRPC.notification('resize', [null, width + 'px']);\n      } else {\n        var height = (0, _dimension.calculateHeight)(this.resizeConfig.heightCalculationMethod);\n\n        // If targetSelectors is specified from Provider or Consumer or both,\n        // need to calculate the height based on specified target selectors\n        if (this.targetSelectors || this.resizeConfig.targetSelectors) {\n          // Combines target selectors from two sources\n          var targetSelectors = [this.targetSelectors, this.resizeConfig.targetSelectors].filter(function (val) {\n            return val;\n          }).join(', ');\n\n          var heights = [].slice.call(document.querySelectorAll(targetSelectors)).map(_dimension.getOffsetHeightToBody);\n\n          height = Math.max.apply(Math, (0, _toConsumableArray3.default)(heights).concat([height]));\n        }\n\n        this.JSONRPC.notification('resize', [height + 'px']);\n      }\n    }\n\n    /**\n    * Triggers an event in the parent application.\n    * @param {string} event - The event name to trigger.\n    * @param {object} detail - The data context to send with the event.\n    */\n\n  }, {\n    key: 'trigger',\n    value: function trigger(event, detail) {\n      this.JSONRPC.notification('event', [event, detail]);\n    }\n\n    /**\n    * Request to mount an application fullscreen.\n    * @param {string} url - The url of the application to mount.\n    */\n\n  }, {\n    key: 'fullscreen',\n    value: function fullscreen(url) {\n      this.trigger('xfc.fullscreen', url);\n    }\n\n    /**\n     * Sends http errors to consumer.\n     * @param  {object} error - an object containing error details\n     */\n\n  }, {\n    key: 'httpError',\n    value: function httpError() {\n      var error = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n      this.trigger('xfc.provider.httpError', error);\n    }\n\n    /**\n     * Request to load a new page of given url\n     * @param  {string} url - The url of the new page.\n     */\n\n  }, {\n    key: 'loadPage',\n    value: function loadPage(url) {\n      this.JSONRPC.notification('loadPage', [url]);\n    }\n\n    /**\n    * Launches the provider app and begins the authorization sequence.\n    */\n\n  }, {\n    key: 'launch',\n    value: function launch() {\n      if (window.self !== window.top) {\n        // 1: Setup listeners for all incoming communication and beforeunload\n        window.addEventListener('message', this.handleConsumerMessage);\n        window.addEventListener('beforeunload', this.unload);\n\n        // 2: Begin launch and authorization sequence\n        this.JSONRPC.notification('launch');\n\n        // 2a. We have a specific origin to trust (excluding wildcard *),\n        // wait for response to authorize.\n        if (this.acls.some(function (x) {\n          return x !== '*';\n        })) {\n          this.JSONRPC.request('authorizeConsumer', []).then(this.authorizeConsumer).catch(this.emitError);\n\n          // 2b. We don't know who to trust, challenge parent for secret\n        } else if (this.secret) {\n          this.JSONRPC.request('challengeConsumer', []).then(this.verifyChallenge).catch(this.emitError);\n\n          // 2c. acl is '*' and there is no secret, immediately authorize content\n        } else {\n          this.authorizeConsumer();\n        }\n\n        // If not embedded, immediately authorize content\n      } else {\n        this.authorizeConsumer();\n      }\n    }\n\n    /**\n    * Handles an incoming message event by processing the JSONRPC request\n    * @param {object} event - The emitted message event.\n    */\n\n  }, {\n    key: 'handleConsumerMessage',\n    value: function handleConsumerMessage(event) {\n      // Ignore Non-JSONRPC messages or messages not from the parent frame\n      if (!event.data.jsonrpc || event.source !== window.parent) {\n        return;\n      }\n\n      _logger2.default.log('<< provider', event.origin, event.data);\n      // For Chrome, the origin property is in the event.originalEvent object\n      var origin = event.origin || event.originalEvent.origin;\n      if (!this.activeACL && this.acls.indexOf(origin) !== -1) {\n        this.activeACL = origin;\n      }\n\n      if (this.acls.indexOf('*') !== -1 || this.acls.indexOf(origin) !== -1) {\n        this.JSONRPC.handle(event.data);\n      }\n    }\n\n    /**\n    * Send the given message to the frame parent.\n    * @param {object} message - The message to send.\n    */\n\n  }, {\n    key: 'send',\n    value: function send(message) {\n      // Dont' send messages if not embedded\n      if (window.self === window.top) {\n        return;\n      }\n\n      if (this.acls.length < 1) {\n        _logger2.default.error('Message not sent, no acls provided.');\n      }\n\n      if (message) {\n        _logger2.default.log('>> provider', this.acls, message);\n        if (this.activeACL) {\n          parent.postMessage(message, this.activeACL);\n        } else {\n          this.acls.forEach(function (uri) {\n            return parent.postMessage(message, uri);\n          });\n        }\n      }\n    }\n\n    /**\n    * Verify the challange made to the parent frame.\n    * @param {string} secretAttempt - The secret string to verify\n    */\n\n  }, {\n    key: 'verifyChallenge',\n    value: function verifyChallenge(secretAttempt) {\n      var _this2 = this;\n\n      var authorize = function authorize() {\n        _this2.acls = ['*'];\n        _this2.authorizeConsumer();\n      };\n\n      if (typeof this.secret === 'string' && (0, _string.fixedTimeCompare)(this.secret, secretAttempt)) {\n        authorize();\n      } else if (typeof this.secret === 'function') {\n        return this.secret.call(this, secretAttempt).then(authorize);\n      }\n      return _promise2.default.resolve();\n    }\n\n    /**\n    * Authorize the parent frame by unhiding the container.\n    */\n\n  }, {\n    key: 'authorizeConsumer',\n    value: function authorizeConsumer() {\n      document.documentElement.removeAttribute('hidden');\n\n      // Emit a ready event\n      this.emit('xfc.ready');\n      this.JSONRPC.notification('authorized', [{ url: window.location.href }]);\n\n      // If there is an onReady callback, execute it\n      if (typeof this.onReady === 'function') {\n        this.onReady.call(this);\n      }\n    }\n\n    /**\n     * Emit the given error\n     * @param  {object} error - an error object containing error code and error message\n     */\n\n  }, {\n    key: 'emitError',\n    value: function emitError(error) {\n      this.emit('xfc.error', error);\n    }\n  }, {\n    key: 'unload',\n    value: function unload() {\n      // These patterns trigger unload events but don't actually unload the page\n      var protocols = /^(tel|mailto|fax|sms|callto):/;\n      var element = document.activeElement;\n\n      if (!element || !(element.hasAttribute && element.hasAttribute('download') || protocols.test(element.href))) {\n        this.JSONRPC.notification('unload');\n        this.trigger('xfc.unload');\n      }\n    }\n  }]);\n  return Application;\n}(_events.EventEmitter);\n\nexports.default = Application;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/xfc/lib/provider/application.js\n// module id = 149\n// module chunks = 0", "module.exports = { \"default\": require(\"core-js/library/fn/array/from\"), __esModule: true };\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/babel-runtime/core-js/array/from.js\n// module id = 150\n// module chunks = 0", "require('../../modules/es6.string.iterator');\nrequire('../../modules/es6.array.from');\nmodule.exports = require('../../modules/_core').Array.from;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/fn/array/from.js\n// module id = 151\n// module chunks = 0", "'use strict';\nvar ctx = require('./_ctx');\nvar $export = require('./_export');\nvar toObject = require('./_to-object');\nvar call = require('./_iter-call');\nvar isArrayIter = require('./_is-array-iter');\nvar toLength = require('./_to-length');\nvar createProperty = require('./_create-property');\nvar getIterFn = require('./core.get-iterator-method');\n\n$export($export.S + $export.F * !require('./_iter-detect')(function (iter) { Array.from(iter); }), 'Array', {\n  // 22.1.2.1 Array.from(arrayLike, mapfn = undefined, thisArg = undefined)\n  from: function from(arrayLike /* , mapfn = undefined, thisArg = undefined */) {\n    var O = toObject(arrayLike);\n    var C = typeof this == 'function' ? this : Array;\n    var aLen = arguments.length;\n    var mapfn = aLen > 1 ? arguments[1] : undefined;\n    var mapping = mapfn !== undefined;\n    var index = 0;\n    var iterFn = getIterFn(O);\n    var length, result, step, iterator;\n    if (mapping) mapfn = ctx(mapfn, aLen > 2 ? arguments[2] : undefined, 2);\n    // if object isn't iterable or it's array with default iterator - use simple case\n    if (iterFn != undefined && !(C == Array && isArrayIter(iterFn))) {\n      for (iterator = iterFn.call(O), result = new C(); !(step = iterator.next()).done; index++) {\n        createProperty(result, index, mapping ? call(iterator, mapfn, [step.value, index], true) : step.value);\n      }\n    } else {\n      length = toLength(O.length);\n      for (result = new C(length); length > index; index++) {\n        createProperty(result, index, mapping ? mapfn(O[index], index) : O[index]);\n      }\n    }\n    result.length = index;\n    return result;\n  }\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/es6.array.from.js\n// module id = 152\n// module chunks = 0", "'use strict';\nvar $defineProperty = require('./_object-dp');\nvar createDesc = require('./_property-desc');\n\nmodule.exports = function (object, index, value) {\n  if (index in object) $defineProperty.f(object, index, createDesc(0, value));\n  else object[index] = value;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/library/modules/_create-property.js\n// module id = 153\n// module chunks = 0", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n/**\n* Comparison algorithm to prevent timing attacks.\n* See: https://en.wikipedia.org/wiki/Timing_attack\n* @param {String} v1 - The first value to compare\n* @param {String} v2 - The second value to compare\n* @return {boolean} True if the strings are equivalent, false otherwise.\n*/\nfunction fixedTimeCompare(v1, v2) {\n  var compare = function compare(value, current, index) {\n    return value | v1.charCodeAt(index) ^ v2.charCodeAt(index);\n  };\n\n  return v1.split('').reduce(compare, v1.length ^ v2.length) < 1;\n}\n\nexports.fixedTimeCompare = fixedTimeCompare;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/xfc/lib/lib/string.js\n// module id = 154\n// module chunks = 0", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _toConsumableArray2 = require('babel-runtime/helpers/toConsumableArray');\n\nvar _toConsumableArray3 = _interopRequireDefault(_toConsumableArray2);\n\nexports.calculateHeight = calculateHeight;\nexports.calculateWidth = calculateWidth;\nexports.getOffsetToBody = getOffsetToBody;\nexports.getOffsetHeightToBody = getOffsetHeightToBody;\n\nvar _logger = require('./logger');\n\nvar _logger2 = _interopRequireDefault(_logger);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction getComputedStyle(prop) {\n  var el = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : document.body;\n\n  var result = null;\n  if ('getComputedStyle' in window) {\n    result = window.getComputedStyle(el, null);\n  } else {\n    result = document.defaultView.getComputedStyle(el, null);\n  }\n  return result !== null ? parseInt(result[prop], 10) : 0;\n}\n\nfunction getAllMeasures(dimension) {\n  return [dimension.bodyOffset(), dimension.bodyScroll(), dimension.documentElementOffset(), dimension.documentElementScroll()];\n}\n\nvar getHeight = {\n  bodyOffset: function bodyOffset() {\n    return document.body.offsetHeight + getComputedStyle('marginTop') + getComputedStyle('marginBottom');\n  },\n\n  bodyScroll: function bodyScroll() {\n    return document.body.scrollHeight;\n  },\n\n  documentElementOffset: function documentElementOffset() {\n    return document.documentElement.offsetHeight;\n  },\n\n  documentElementScroll: function documentElementScroll() {\n    return document.documentElement.scrollHeight;\n  },\n\n  max: function max() {\n    return Math.max.apply(Math, (0, _toConsumableArray3.default)(getAllMeasures(getHeight)));\n  },\n\n  min: function min() {\n    return Math.min.apply(Math, (0, _toConsumableArray3.default)(getAllMeasures(getHeight)));\n  }\n};\n\nvar getWidth = {\n  bodyOffset: function bodyOffset() {\n    return document.body.offsetWidth;\n  },\n\n  bodyScroll: function bodyScroll() {\n    return document.body.scrollWidth;\n  },\n\n  documentElementOffset: function documentElementOffset() {\n    return document.documentElement.offsetWidth;\n  },\n\n  documentElementScroll: function documentElementScroll() {\n    return document.documentElement.scrollWidth;\n  },\n\n  scroll: function scroll() {\n    return Math.max(getWidth.bodyScroll(), getWidth.documentElementScroll());\n  },\n\n  max: function max() {\n    return Math.max.apply(Math, (0, _toConsumableArray3.default)(getAllMeasures(getWidth)));\n  },\n\n  min: function min() {\n    return Math.min.apply(Math, (0, _toConsumableArray3.default)(getAllMeasures(getWidth)));\n  }\n};\n\nfunction calculateHeight() {\n  var calMethod = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'bodyOffset';\n\n  if (!(calMethod in getHeight)) {\n    _logger2.default.error('\\'' + calMethod + '\\' is not a valid method name!');\n  }\n  return getHeight[calMethod]();\n}\n\nfunction calculateWidth() {\n  var calMethod = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'scroll';\n\n  if (!(calMethod in getWidth)) {\n    _logger2.default.error('\\'' + calMethod + '\\' is not a valid method name!');\n  }\n  return getWidth[calMethod]();\n}\n\n/**\n * This function returns the offset height of the given node relative to the top of document.body\n */\nfunction getOffsetToBody(node) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n\n  // If the given node is body or null, return 0\n  if (!node || node === window.document.body) {\n    return 0;\n  }\n\n  // Stops if the offset parent node is body;\n  // Otherwise keep searching up\n  // NOTE: offsetParent will return null on Webkit if the element is hidden\n  //       (the style.display of this element or any ancestor is \"none\") or\n  //       if the style.position of the element itself is set to \"fixed\"\n  //       See reference at https://developer.mozilla.org/en-US/docs/Web/API/HTMLelement/offsetParent#Compatibility\n  var calculatedOffset = node.offsetTop + offset;\n  var offsetParent = node.offsetParent;\n\n  if (offsetParent === window.document.body) {\n    return calculatedOffset;\n  }\n\n  return getOffsetToBody(offsetParent, calculatedOffset);\n}\n\n/**\n * This function returns the offset height of the given node relative to the top of document.body\n *\n * @note We are explicitly using the scrollHeight here since offsetHeight doesn't include any ::before or ::after\n * pseudo-elements, which can throw off the actual total height of the element. From the MDN web docs,\n *\n * scrollHeight:\n * > is a measurement of the height of an element's content, including content not visible on the screen due to\n * > overflow...is equal to the minimum height the element would require in order to fit all the content in the\n * > viewport without using a vertical scrollbar\n *\n * offsetHeight:\n * > is a measurement in pixels of the element's CSS height, including any borders, padding, and horizontal scrollbars\n * > (if rendered). It does not include the height of pseudo-elements such as ::before or ::after\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetHeight\n * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollHeight\n */\nfunction getOffsetHeightToBody(node) {\n  return !node ? 0 : getOffsetToBody(node) + node.scrollHeight;\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/xfc/lib/lib/dimension.js\n// module id = 155\n// module chunks = 0", "var MutationObserver = window.MutationObserver\n  || window.WebKitMutationObserver\n  || window.MozMutationObserver;\n\n/*\n * Copyright 2012 The Polymer Authors. All rights reserved.\n * Use of this source code is goverened by a BSD-style\n * license that can be found in the LICENSE file.\n */\n\nvar WeakMap = window.WeakMap;\n\nif (typeof WeakMap === 'undefined') {\n  var defineProperty = Object.defineProperty;\n  var counter = Date.now() % 1e9;\n\n  WeakMap = function() {\n    this.name = '__st' + (Math.random() * 1e9 >>> 0) + (counter++ + '__');\n  };\n\n  WeakMap.prototype = {\n    set: function(key, value) {\n      var entry = key[this.name];\n      if (entry && entry[0] === key)\n        entry[1] = value;\n      else\n        defineProperty(key, this.name, {value: [key, value], writable: true});\n      return this;\n    },\n    get: function(key) {\n      var entry;\n      return (entry = key[this.name]) && entry[0] === key ?\n          entry[1] : undefined;\n    },\n    'delete': function(key) {\n      var entry = key[this.name];\n      if (!entry) return false;\n      var hasValue = entry[0] === key;\n      entry[0] = entry[1] = undefined;\n      return hasValue;\n    },\n    has: function(key) {\n      var entry = key[this.name];\n      if (!entry) return false;\n      return entry[0] === key;\n    }\n  };\n}\n\nvar registrationsTable = new WeakMap();\n\n// We use setImmediate or postMessage for our future callback.\nvar setImmediate = window.msSetImmediate;\n\n// Use post message to emulate setImmediate.\nif (!setImmediate) {\n  var setImmediateQueue = [];\n  var sentinel = String(Math.random());\n  window.addEventListener('message', function(e) {\n    if (e.data === sentinel) {\n      var queue = setImmediateQueue;\n      setImmediateQueue = [];\n      queue.forEach(function(func) {\n        func();\n      });\n    }\n  });\n  setImmediate = function(func) {\n    setImmediateQueue.push(func);\n    window.postMessage(sentinel, '*');\n  };\n}\n\n// This is used to ensure that we never schedule 2 callas to setImmediate\nvar isScheduled = false;\n\n// Keep track of observers that needs to be notified next time.\nvar scheduledObservers = [];\n\n/**\n * Schedules |dispatchCallback| to be called in the future.\n * @param {MutationObserver} observer\n */\nfunction scheduleCallback(observer) {\n  scheduledObservers.push(observer);\n  if (!isScheduled) {\n    isScheduled = true;\n    setImmediate(dispatchCallbacks);\n  }\n}\n\nfunction wrapIfNeeded(node) {\n  return window.ShadowDOMPolyfill &&\n      window.ShadowDOMPolyfill.wrapIfNeeded(node) ||\n      node;\n}\n\nfunction dispatchCallbacks() {\n  // http://dom.spec.whatwg.org/#mutation-observers\n\n  isScheduled = false; // Used to allow a new setImmediate call above.\n\n  var observers = scheduledObservers;\n  scheduledObservers = [];\n  // Sort observers based on their creation UID (incremental).\n  observers.sort(function(o1, o2) {\n    return o1.uid_ - o2.uid_;\n  });\n\n  var anyNonEmpty = false;\n  observers.forEach(function(observer) {\n\n    // 2.1, 2.2\n    var queue = observer.takeRecords();\n    // 2.3. Remove all transient registered observers whose observer is mo.\n    removeTransientObserversFor(observer);\n\n    // 2.4\n    if (queue.length) {\n      observer.callback_(queue, observer);\n      anyNonEmpty = true;\n    }\n  });\n\n  // 3.\n  if (anyNonEmpty)\n    dispatchCallbacks();\n}\n\nfunction removeTransientObserversFor(observer) {\n  observer.nodes_.forEach(function(node) {\n    var registrations = registrationsTable.get(node);\n    if (!registrations)\n      return;\n    registrations.forEach(function(registration) {\n      if (registration.observer === observer)\n        registration.removeTransientObservers();\n    });\n  });\n}\n\n/**\n * This function is used for the \"For each registered observer observer (with\n * observer's options as options) in target's list of registered observers,\n * run these substeps:\" and the \"For each ancestor ancestor of target, and for\n * each registered observer observer (with options options) in ancestor's list\n * of registered observers, run these substeps:\" part of the algorithms. The\n * |options.subtree| is checked to ensure that the callback is called\n * correctly.\n *\n * @param {Node} target\n * @param {function(MutationObserverInit):MutationRecord} callback\n */\nfunction forEachAncestorAndObserverEnqueueRecord(target, callback) {\n  for (var node = target; node; node = node.parentNode) {\n    var registrations = registrationsTable.get(node);\n\n    if (registrations) {\n      for (var j = 0; j < registrations.length; j++) {\n        var registration = registrations[j];\n        var options = registration.options;\n\n        // Only target ignores subtree.\n        if (node !== target && !options.subtree)\n          continue;\n\n        var record = callback(options);\n        if (record)\n          registration.enqueue(record);\n      }\n    }\n  }\n}\n\nvar uidCounter = 0;\n\n/**\n * The class that maps to the DOM MutationObserver interface.\n * @param {Function} callback.\n * @constructor\n */\nfunction JsMutationObserver(callback) {\n  this.callback_ = callback;\n  this.nodes_ = [];\n  this.records_ = [];\n  this.uid_ = ++uidCounter;\n}\n\nJsMutationObserver.prototype = {\n  observe: function(target, options) {\n    target = wrapIfNeeded(target);\n\n    // 1.1\n    if (!options.childList && !options.attributes && !options.characterData ||\n\n        // 1.2\n        options.attributeOldValue && !options.attributes ||\n\n        // 1.3\n        options.attributeFilter && options.attributeFilter.length &&\n            !options.attributes ||\n\n        // 1.4\n        options.characterDataOldValue && !options.characterData) {\n\n      throw new SyntaxError();\n    }\n\n    var registrations = registrationsTable.get(target);\n    if (!registrations)\n      registrationsTable.set(target, registrations = []);\n\n    // 2\n    // If target's list of registered observers already includes a registered\n    // observer associated with the context object, replace that registered\n    // observer's options with options.\n    var registration;\n    for (var i = 0; i < registrations.length; i++) {\n      if (registrations[i].observer === this) {\n        registration = registrations[i];\n        registration.removeListeners();\n        registration.options = options;\n        break;\n      }\n    }\n\n    // 3.\n    // Otherwise, add a new registered observer to target's list of registered\n    // observers with the context object as the observer and options as the\n    // options, and add target to context object's list of nodes on which it\n    // is registered.\n    if (!registration) {\n      registration = new Registration(this, target, options);\n      registrations.push(registration);\n      this.nodes_.push(target);\n    }\n\n    registration.addListeners();\n  },\n\n  disconnect: function() {\n    this.nodes_.forEach(function(node) {\n      var registrations = registrationsTable.get(node);\n      for (var i = 0; i < registrations.length; i++) {\n        var registration = registrations[i];\n        if (registration.observer === this) {\n          registration.removeListeners();\n          registrations.splice(i, 1);\n          // Each node can only have one registered observer associated with\n          // this observer.\n          break;\n        }\n      }\n    }, this);\n    this.records_ = [];\n  },\n\n  takeRecords: function() {\n    var copyOfRecords = this.records_;\n    this.records_ = [];\n    return copyOfRecords;\n  }\n};\n\n/**\n * @param {string} type\n * @param {Node} target\n * @constructor\n */\nfunction MutationRecord(type, target) {\n  this.type = type;\n  this.target = target;\n  this.addedNodes = [];\n  this.removedNodes = [];\n  this.previousSibling = null;\n  this.nextSibling = null;\n  this.attributeName = null;\n  this.attributeNamespace = null;\n  this.oldValue = null;\n}\n\nfunction copyMutationRecord(original) {\n  var record = new MutationRecord(original.type, original.target);\n  record.addedNodes = original.addedNodes.slice();\n  record.removedNodes = original.removedNodes.slice();\n  record.previousSibling = original.previousSibling;\n  record.nextSibling = original.nextSibling;\n  record.attributeName = original.attributeName;\n  record.attributeNamespace = original.attributeNamespace;\n  record.oldValue = original.oldValue;\n  return record;\n};\n\n// We keep track of the two (possibly one) records used in a single mutation.\nvar currentRecord, recordWithOldValue;\n\n/**\n * Creates a record without |oldValue| and caches it as |currentRecord| for\n * later use.\n * @param {string} oldValue\n * @return {MutationRecord}\n */\nfunction getRecord(type, target) {\n  return currentRecord = new MutationRecord(type, target);\n}\n\n/**\n * Gets or creates a record with |oldValue| based in the |currentRecord|\n * @param {string} oldValue\n * @return {MutationRecord}\n */\nfunction getRecordWithOldValue(oldValue) {\n  if (recordWithOldValue)\n    return recordWithOldValue;\n  recordWithOldValue = copyMutationRecord(currentRecord);\n  recordWithOldValue.oldValue = oldValue;\n  return recordWithOldValue;\n}\n\nfunction clearRecords() {\n  currentRecord = recordWithOldValue = undefined;\n}\n\n/**\n * @param {MutationRecord} record\n * @return {boolean} Whether the record represents a record from the current\n * mutation event.\n */\nfunction recordRepresentsCurrentMutation(record) {\n  return record === recordWithOldValue || record === currentRecord;\n}\n\n/**\n * Selects which record, if any, to replace the last record in the queue.\n * This returns |null| if no record should be replaced.\n *\n * @param {MutationRecord} lastRecord\n * @param {MutationRecord} newRecord\n * @param {MutationRecord}\n */\nfunction selectRecord(lastRecord, newRecord) {\n  if (lastRecord === newRecord)\n    return lastRecord;\n\n  // Check if the the record we are adding represents the same record. If\n  // so, we keep the one with the oldValue in it.\n  if (recordWithOldValue && recordRepresentsCurrentMutation(lastRecord))\n    return recordWithOldValue;\n\n  return null;\n}\n\n/**\n * Class used to represent a registered observer.\n * @param {MutationObserver} observer\n * @param {Node} target\n * @param {MutationObserverInit} options\n * @constructor\n */\nfunction Registration(observer, target, options) {\n  this.observer = observer;\n  this.target = target;\n  this.options = options;\n  this.transientObservedNodes = [];\n}\n\nRegistration.prototype = {\n  enqueue: function(record) {\n    var records = this.observer.records_;\n    var length = records.length;\n\n    // There are cases where we replace the last record with the new record.\n    // For example if the record represents the same mutation we need to use\n    // the one with the oldValue. If we get same record (this can happen as we\n    // walk up the tree) we ignore the new record.\n    if (records.length > 0) {\n      var lastRecord = records[length - 1];\n      var recordToReplaceLast = selectRecord(lastRecord, record);\n      if (recordToReplaceLast) {\n        records[length - 1] = recordToReplaceLast;\n        return;\n      }\n    } else {\n      scheduleCallback(this.observer);\n    }\n\n    records[length] = record;\n  },\n\n  addListeners: function() {\n    this.addListeners_(this.target);\n  },\n\n  addListeners_: function(node) {\n    var options = this.options;\n    if (options.attributes)\n      node.addEventListener('DOMAttrModified', this, true);\n\n    if (options.characterData)\n      node.addEventListener('DOMCharacterDataModified', this, true);\n\n    if (options.childList)\n      node.addEventListener('DOMNodeInserted', this, true);\n\n    if (options.childList || options.subtree)\n      node.addEventListener('DOMNodeRemoved', this, true);\n  },\n\n  removeListeners: function() {\n    this.removeListeners_(this.target);\n  },\n\n  removeListeners_: function(node) {\n    var options = this.options;\n    if (options.attributes)\n      node.removeEventListener('DOMAttrModified', this, true);\n\n    if (options.characterData)\n      node.removeEventListener('DOMCharacterDataModified', this, true);\n\n    if (options.childList)\n      node.removeEventListener('DOMNodeInserted', this, true);\n\n    if (options.childList || options.subtree)\n      node.removeEventListener('DOMNodeRemoved', this, true);\n  },\n\n  /**\n   * Adds a transient observer on node. The transient observer gets removed\n   * next time we deliver the change records.\n   * @param {Node} node\n   */\n  addTransientObserver: function(node) {\n    // Don't add transient observers on the target itself. We already have all\n    // the required listeners set up on the target.\n    if (node === this.target)\n      return;\n\n    this.addListeners_(node);\n    this.transientObservedNodes.push(node);\n    var registrations = registrationsTable.get(node);\n    if (!registrations)\n      registrationsTable.set(node, registrations = []);\n\n    // We know that registrations does not contain this because we already\n    // checked if node === this.target.\n    registrations.push(this);\n  },\n\n  removeTransientObservers: function() {\n    var transientObservedNodes = this.transientObservedNodes;\n    this.transientObservedNodes = [];\n\n    transientObservedNodes.forEach(function(node) {\n      // Transient observers are never added to the target.\n      this.removeListeners_(node);\n\n      var registrations = registrationsTable.get(node);\n      for (var i = 0; i < registrations.length; i++) {\n        if (registrations[i] === this) {\n          registrations.splice(i, 1);\n          // Each node can only have one registered observer associated with\n          // this observer.\n          break;\n        }\n      }\n    }, this);\n  },\n\n  handleEvent: function(e) {\n    // Stop propagation since we are managing the propagation manually.\n    // This means that other mutation events on the page will not work\n    // correctly but that is by design.\n    e.stopImmediatePropagation();\n\n    switch (e.type) {\n      case 'DOMAttrModified':\n        // http://dom.spec.whatwg.org/#concept-mo-queue-attributes\n\n        var name = e.attrName;\n        var namespace = e.relatedNode.namespaceURI;\n        var target = e.target;\n\n        // 1.\n        var record = new getRecord('attributes', target);\n        record.attributeName = name;\n        record.attributeNamespace = namespace;\n\n        // 2.\n        var oldValue = null;\n        if (!(typeof MutationEvent !== 'undefined' && e.attrChange === MutationEvent.ADDITION))\n          oldValue = e.prevValue;\n\n        forEachAncestorAndObserverEnqueueRecord(target, function(options) {\n          // 3.1, 4.2\n          if (!options.attributes)\n            return;\n\n          // 3.2, 4.3\n          if (options.attributeFilter && options.attributeFilter.length &&\n              options.attributeFilter.indexOf(name) === -1 &&\n              options.attributeFilter.indexOf(namespace) === -1) {\n            return;\n          }\n          // 3.3, 4.4\n          if (options.attributeOldValue)\n            return getRecordWithOldValue(oldValue);\n\n          // 3.4, 4.5\n          return record;\n        });\n\n        break;\n\n      case 'DOMCharacterDataModified':\n        // http://dom.spec.whatwg.org/#concept-mo-queue-characterdata\n        var target = e.target;\n\n        // 1.\n        var record = getRecord('characterData', target);\n\n        // 2.\n        var oldValue = e.prevValue;\n\n\n        forEachAncestorAndObserverEnqueueRecord(target, function(options) {\n          // 3.1, 4.2\n          if (!options.characterData)\n            return;\n\n          // 3.2, 4.3\n          if (options.characterDataOldValue)\n            return getRecordWithOldValue(oldValue);\n\n          // 3.3, 4.4\n          return record;\n        });\n\n        break;\n\n      case 'DOMNodeRemoved':\n        this.addTransientObserver(e.target);\n        // Fall through.\n      case 'DOMNodeInserted':\n        // http://dom.spec.whatwg.org/#concept-mo-queue-childlist\n        var target = e.relatedNode;\n        var changedNode = e.target;\n        var addedNodes, removedNodes;\n        if (e.type === 'DOMNodeInserted') {\n          addedNodes = [changedNode];\n          removedNodes = [];\n        } else {\n\n          addedNodes = [];\n          removedNodes = [changedNode];\n        }\n        var previousSibling = changedNode.previousSibling;\n        var nextSibling = changedNode.nextSibling;\n\n        // 1.\n        var record = getRecord('childList', target);\n        record.addedNodes = addedNodes;\n        record.removedNodes = removedNodes;\n        record.previousSibling = previousSibling;\n        record.nextSibling = nextSibling;\n\n        forEachAncestorAndObserverEnqueueRecord(target, function(options) {\n          // 2.1, 3.2\n          if (!options.childList)\n            return;\n\n          // 2.2, 3.3\n          return record;\n        });\n\n    }\n\n    clearRecords();\n  }\n};\n\nif (!MutationObserver) {\n  MutationObserver = JsMutationObserver;\n}\n\nmodule.exports = MutationObserver;\n\n\n\n// WEBPACK FOOTER //\n// ./node_modules/mutation-observer/index.js", "/* global window */\n\n/**\n * Wrapper object to override com objects.\n */\nconst ComOverrider = {\n  /**\n   * Overrides the COM objects if the SMART app is loaded in embedded mode and within Edge browser.\n   * Currently overrides APPLINK only.\n   * @param cernerSmartEmbeddableLib The Cerner Smart Embeddable Lib object\n   */\n  override: (cernerSmartEmbeddableLib) => {\n    const isEdge = window.navigator.userAgent.indexOf('Edg') !== -1;\n    if (window.self !== window.top && isEdge) {\n      /** APPLINK API definition - https://wiki.cerner.com/display/public/MPDEVWIKI/APPLINK */\n      window.APPLINK = (linkMode, launchObject, commandLineArgs) =>\n        cernerSmartEmbeddableLib.invokeAPI('APPLINK', {\n          linkMode,\n          launchObject,\n          commandLineArgs,\n        });\n    }\n  },\n};\nexport default ComOverrider;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/com-overrider.js"], "sourceRoot": ""}