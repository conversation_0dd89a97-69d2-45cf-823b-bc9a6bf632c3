{"version": 3, "file": "fhir-client.pure.js", "mappings": ";;;;;;;;;;;;;;;AAAA,MAAAA,KAAA,GAAAC,mBAAA;AAgBA,MAAAC,SAAA,GAAAD,mBAAA;AACA,MAAAE,UAAA,GAAAF,mBAAA;AAIA,MAAAG,YAAA,GAAAH,mBAAA;AAEA;AACA;AACA,MAAM;EAAEI;AAAQ,CAAE,GAAG,KAAsC,GAAGE,MAAM,GAAGN,CAAsB;AAC7F;AAEA,MAAMO,KAAK,GAAGR,KAAA,CAAAQ,KAAM,CAACC,MAAM,CAAC,QAAQ,CAAC;AAErC;;;;;;;AAOA,eAAeC,aAAaA,CACxBC,cAAwD,EACxDC,MAAc;EAGd,MAAMC,IAAI,GAAG,IAAAb,KAAA,CAAAc,QAAQ,EAAC,GAAG,EAAEF,MAAM,CAACG,KAAK,CAACC,SAAS,CAAC;EAElD,eAAeC,aAAaA,CAACC,IAAS;IAClC,MAAMC,YAAY,GAAGD,IAAI,CAACE,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;IACnD,IAAAtB,KAAA,CAAAuB,MAAM,EAACJ,YAAY,EAAE,gBAAgBD,IAAI,GAAG,CAAC;IAC7C,IAAAlB,KAAA,CAAAuB,MAAM,EAACpB,UAAA,CAAAqB,kBAAkB,CAACC,OAAO,CAACN,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,kBAAkBA,YAAY,wBAAwB,CAAC;IAC7G,MAAMO,WAAW,GAAG,MAAM,IAAA1B,KAAA,CAAA2B,yBAAyB,EAACf,MAAM,CAACG,KAAK,CAACC,SAAS,CAAC;IAC3E,MAAMY,WAAW,GAAG,IAAA5B,KAAA,CAAA6B,eAAe,EAACH,WAAW,EAAEP,YAAY,CAAC;IAC9DD,IAAI,CAACY,YAAY,CAACC,GAAG,CAACH,WAAW,EAAEhB,MAAM,CAACoB,OAAO,CAACC,EAAY,CAAC;IAC/D,OAAOf,IAAI,CAACgB,IAAI;EACpB;EAEA,IAAI,OAAOvB,cAAc,IAAI,QAAQ,IAAIA,cAAc,YAAYwB,GAAG,EAAE;IACpE,OAAO;MAAEC,GAAG,EAAE,MAAMnB,aAAa,CAAC,IAAIkB,GAAG,CAACxB,cAAc,GAAG,EAAE,EAAEE,IAAI,CAAC;IAAC,CAAE;;EAG3EF,cAAc,CAACyB,GAAG,GAAG,MAAMnB,aAAa,CAAC,IAAIkB,GAAG,CAACxB,cAAc,CAACyB,GAAG,GAAG,EAAE,EAAEvB,IAAI,CAAC,CAAC;EAChF,OAAOF,cAAc;AACzB;AAEA;;;;;;;;;;;;AAYA,MAAqB0B,MAAO,SAAQjC,YAAA,CAAAkC,OAAU;EA6I1C;;;;EAIAC,YAAYC,WAA+B,EAAEzB,KAAsC;IAE/E,MAAM0B,MAAM,GAAG,OAAO1B,KAAK,IAAI,QAAQ,GAAG;MAAEC,SAAS,EAAED;IAAK,CAAE,GAAGA,KAAK;IAEtE;IACA,IAAAf,KAAA,CAAAuB,MAAM,EACFkB,MAAM,CAACzB,SAAS,IAAIyB,MAAM,CAACzB,SAAS,CAAC0B,KAAK,CAAC,eAAe,CAAC,EAC3D,oEAAoE,CACvE;IAED,KAAK,CAACD,MAAM,CAACzB,SAAS,CAAC;IAgpB3B;;;IAGA,KAAA2B,KAAK,GAAG3C,KAAA,CAAA2C,KAAK;IAjpBT,IAAI,CAAC5B,KAAK,GAAG0B,MAAM;IACnB,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACI,YAAY,GAAG,IAAI;IAExB,MAAMhC,MAAM,GAAG,IAAI;IAEnB;IACA,IAAI,CAACoB,OAAO,GAAG;MACX,IAAIC,EAAEA,CAAA;QAAK,OAAOrB,MAAM,CAACiC,YAAY,EAAE;MAAE,CAAC;MAC1CC,IAAI,EAAGnC,cAAc,IAAI;QACrB,MAAMsB,EAAE,GAAG,IAAI,CAACD,OAAO,CAACC,EAAE;QAC1B,OAAOA,EAAE,GACL,IAAI,CAACc,OAAO,CAAC;UAAE,GAAGpC,cAAc;UAAEyB,GAAG,EAAE,WAAWH,EAAE;QAAE,CAAE,CAAC,GACzDe,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,0BAA0B,CAAC,CAAC;MAC7D,CAAC;MACDH,OAAO,EAAEA,CAACpC,cAAc,EAAEwC,WAAW,GAAG,EAAE,KAAI;QAC1C,IAAI,IAAI,CAACnB,OAAO,CAACC,EAAE,EAAE;UACjB,OAAO,CAAC,YAAW;YACf,MAAMmB,OAAO,GAAG,MAAM1C,aAAa,CAACC,cAAc,EAAE,IAAI,CAAC;YACzD,OAAO,IAAI,CAACoC,OAAO,CAACK,OAAO,EAAED,WAAW,CAAC;UAC7C,CAAC,EAAC,CAAE;SACP,MAAM;UACH,OAAOH,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;MAEpE;KACH;IAED;IACA,IAAI,CAACG,SAAS,GAAG;MACb,IAAIpB,EAAEA,CAAA;QAAK,OAAOrB,MAAM,CAAC0C,cAAc,EAAE;MAAE,CAAC;MAC5CR,IAAI,EAAEnC,cAAc,IAAG;QACnB,MAAMsB,EAAE,GAAG,IAAI,CAACoB,SAAS,CAACpB,EAAE;QAC5B,OAAOA,EAAE,GACL,IAAI,CAACc,OAAO,CAAC;UAAE,GAAGpC,cAAc;UAAEyB,GAAG,EAAE,aAAaH,EAAE;QAAE,CAAE,CAAC,GAC3De,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,4BAA4B,CAAC,CAAC;MAC/D;KACH;IAED;IACA,IAAI,CAACK,IAAI,GAAG;MACR,IAAIC,QAAQA,CAAA;QAAK,OAAO5C,MAAM,CAAC6C,WAAW,EAAE;MAAE,CAAC;MAC/C,IAAIxB,EAAEA,CAAA;QAAK,OAAOrB,MAAM,CAAC8C,SAAS,EAAE;MAAE,CAAC;MACvC,IAAIvC,YAAYA,CAAA;QAAK,OAAOP,MAAM,CAAC+C,WAAW,EAAE;MAAE,CAAC;MACnDb,IAAI,EAAEnC,cAAc,IAAG;QACnB,MAAM6C,QAAQ,GAAG,IAAI,CAACD,IAAI,CAACC,QAAQ;QACnC,OAAOA,QAAQ,GACX,IAAI,CAACT,OAAO,CAAC;UAAE,GAAGpC,cAAc;UAAEyB,GAAG,EAAEoB;QAAQ,CAAE,CAAC,GAClDR,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,uBAAuB,CAAC,CAAC;MAC1D;KACH;IAED;IACA;IACA,IAAI,CAACU,OAAO,CAAEpB,WAA8B,CAACqB,IAAI,CAAC;EACtD;EAEA;;;;;;;;EAQAD,OAAOA,CAACE,MAA8D;IAElE,IAAI,OAAOA,MAAM,IAAI,UAAU,EAAE;MAC7B,MAAMV,OAAO,GAAwB;QACjCW,OAAO,EAAE,IAAI,CAAChD,KAAK,CAACC,SAAS,CAACgD,OAAO,CAAC,KAAK,EAAE,EAAE;OAClD;MAED,MAAMC,WAAW,GAAG,IAAI,CAACC,QAAQ,CAAC,4BAA4B,CAAC;MAC/D,IAAID,WAAW,EAAE;QACbb,OAAO,CAACe,IAAI,GAAG;UAAEC,KAAK,EAAEH;QAAW,CAAE;OACxC,MACI;QACD,MAAM;UAAEI,QAAQ;UAAEC;QAAQ,CAAE,GAAG,IAAI,CAACvD,KAAK;QACzC,IAAIsD,QAAQ,IAAIC,QAAQ,EAAE;UACtBlB,OAAO,CAACe,IAAI,GAAG;YACXZ,IAAI,EAAEc,QAAQ;YACdE,IAAI,EAAED;WACT;;;MAGT,IAAI,CAACE,GAAG,GAAGV,MAAM,CAACV,OAAO,CAAC;MAE1B,MAAMqB,SAAS,GAAG,IAAI,CAACP,QAAQ,CAAC,uBAAuB,CAAC;MACxD,IAAIO,SAAS,EAAE;QACX,IAAI,CAACzC,OAAO,CAACwC,GAAG,GAAGV,MAAM,CAAC;UACtB,GAAGV,OAAO;UACVpB,OAAO,EAAEyC;SACZ,CAAC;;;IAGV,OAAO,IAAI;EACf;EAEA;;;;EAIA5B,YAAYA,CAAA;IAER,MAAM6B,aAAa,GAAG,IAAI,CAAC3D,KAAK,CAAC2D,aAAa;IAC9C,IAAIA,aAAa,EAAE;MACf;MACA;MACA,IAAI,CAACA,aAAa,CAAC1C,OAAO,EAAE;QACxB,IAAI,CAAC,CAAC,IAAI,CAACjB,KAAK,CAAC4D,KAAK,IAAI,EAAE,EAAEjC,KAAK,CAAC,wBAAwB,CAAC,EAAE;UAC3DlC,KAAK,CAACN,SAAA,CAAAoC,OAAG,CAACsC,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC;SAChD,MACI;UACD;UACApE,KAAK,CAAC,6FAA6F,CAAC;;QAExG,OAAO,IAAI;;MAEf,OAAOkE,aAAa,CAAC1C,OAAO;;IAGhC,IAAI,IAAI,CAACjB,KAAK,CAAC8D,YAAY,EAAE;MACzBrE,KAAK,CAACN,SAAA,CAAAoC,OAAG,CAACwC,UAAU,EAAE,gCAAgC,CAAC;KAC1D,MACI;MACDtE,KAAK,CAACN,SAAA,CAAAoC,OAAG,CAACyC,aAAa,EAAE,kBAAkB,CAAC;;IAEhD,OAAO,IAAI;EACf;EAEA;;;;;;EAMAzB,cAAcA,CAAA;IAEV,MAAMoB,aAAa,GAAG,IAAI,CAAC3D,KAAK,CAAC2D,aAAa;IAC9C,IAAIA,aAAa,EAAE;MACf;MACA;MACA,IAAI,CAACA,aAAa,CAACrB,SAAS,EAAE;QAC1B,IAAI,CAAC,CAAC,IAAI,CAACtC,KAAK,CAAC4D,KAAK,IAAI,EAAE,EAAEjC,KAAK,CAAC,0BAA0B,CAAC,EAAE;UAC7DlC,KAAK,CAACN,SAAA,CAAAoC,OAAG,CAACsC,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC;SACpD,MACI;UACD;UACApE,KAAK,CAAC,0JAA0J,CAAC;;QAErK,OAAO,IAAI;;MAEf,OAAOkE,aAAa,CAACrB,SAAS;;IAGlC,IAAI,IAAI,CAACtC,KAAK,CAAC8D,YAAY,EAAE;MACzBrE,KAAK,CAACN,SAAA,CAAAoC,OAAG,CAACwC,UAAU,EAAE,kCAAkC,CAAC;KAC5D,MACI;MACDtE,KAAK,CAACN,SAAA,CAAAoC,OAAG,CAACyC,aAAa,EAAE,oBAAoB,CAAC;;IAElD,OAAO,IAAI;EACf;EAEA;;;;;EAKAC,UAAUA,CAAA;IAEN,MAAMN,aAAa,GAAG,IAAI,CAAC3D,KAAK,CAAC2D,aAAa;IAC9C,IAAIA,aAAa,EAAE;MACf,MAAMO,OAAO,GAAGP,aAAa,CAACQ,QAAQ;MACtC,MAAMP,KAAK,GAAG,IAAI,CAAC5D,KAAK,CAAC4D,KAAK,IAAI,EAAE;MAEpC;MACA;MACA,IAAI,CAACM,OAAO,EAAE;QACV,MAAME,SAAS,GAAKR,KAAK,CAACjC,KAAK,CAAC,YAAY,CAAC;QAC7C,MAAM0C,UAAU,GAAIT,KAAK,CAACjC,KAAK,CAAC,aAAa,CAAC;QAC9C,MAAM2C,WAAW,GAAGV,KAAK,CAACjC,KAAK,CAAC,cAAc,CAAC;QAC/C,IAAI,CAACyC,SAAS,IAAI,EAAEE,WAAW,IAAID,UAAU,CAAC,EAAE;UAC5C5E,KAAK,CACD,qDAAqD,GACrD,kDAAkD,GAClD,gDAAgD,GAChD,aAAa,CAChB;SACJ,MACI;UACD;UACAA,KAAK,CAAC,2EAA2E,CAAC;;QAEtF,OAAO,IAAI;;MAEf,OAAO,IAAAR,KAAA,CAAAsF,SAAS,EAACL,OAAO,EAAE,IAAI,CAACzC,WAAW,CAAuB;;IAErE,IAAI,IAAI,CAACzB,KAAK,CAAC8D,YAAY,EAAE;MACzBrE,KAAK,CAACN,SAAA,CAAAoC,OAAG,CAACwC,UAAU,EAAE,cAAc,CAAC;KACxC,MACI;MACDtE,KAAK,CAACN,SAAA,CAAAoC,OAAG,CAACyC,aAAa,EAAE,UAAU,CAAC;;IAExC,OAAO,IAAI;EACf;EAEA;;;;;EAKAtB,WAAWA,CAAA;IAEP,MAAMwB,OAAO,GAAG,IAAI,CAACD,UAAU,EAAE;IACjC,IAAIC,OAAO,EAAE;MACT;MACA;MACA,IAAIA,OAAO,CAACzB,QAAQ,EAAE;QAClB,OAAOyB,OAAO,CAACzB,QAAQ,CAACnC,KAAK,CAAC,GAAG,CAAC,CAACkE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;;MAE1D,OAAOP,OAAO,CAACQ,OAAO;;IAE1B,OAAO,IAAI;EACf;EAEA;;;EAGA/B,SAASA,CAAA;IAEL,MAAM+B,OAAO,GAAG,IAAI,CAAChC,WAAW,EAAE;IAClC,IAAIgC,OAAO,EAAE;MACT,OAAOA,OAAO,CAACpE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEhC,OAAO,IAAI;EACf;EAEA;;;;EAIAsC,WAAWA,CAAA;IAEP,MAAM8B,OAAO,GAAG,IAAI,CAAChC,WAAW,EAAE;IAClC,IAAIgC,OAAO,EAAE;MACT,OAAOA,OAAO,CAACpE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEhC,OAAO,IAAI;EACf;EAEA;;;;EAIAqE,sBAAsBA,CAAA;IAElB,MAAMzB,WAAW,GAAG,IAAI,CAACC,QAAQ,CAAC,4BAA4B,CAAC;IAC/D,IAAID,WAAW,EAAE;MACb,OAAO,SAAS,GAAGA,WAAW;;IAElC,MAAM;MAAEI,QAAQ;MAAEC;IAAQ,CAAE,GAAG,IAAI,CAACvD,KAAK;IACzC,IAAIsD,QAAQ,IAAIC,QAAQ,EAAE;MACtB,OAAO,QAAQ,GAAG,IAAI,CAAC9B,WAAW,CAACmD,IAAI,CAACtB,QAAQ,GAAG,GAAG,GAAGC,QAAQ,CAAC;;IAEtE,OAAO,IAAI;EACf;EAEA;;;;EAIQ,MAAMsB,WAAWA,CAAA;IACrB,MAAMC,OAAO,GAAG,IAAI,CAACrD,WAAW,CAACsD,UAAU,EAAE;IAC7C,MAAMC,GAAG,GAAG,MAAMF,OAAO,CAACG,GAAG,CAAC7F,UAAA,CAAA8F,SAAS,CAAC;IACxC,IAAIF,GAAG,EAAE;MACL,MAAMF,OAAO,CAACK,KAAK,CAACH,GAAG,CAAC;;IAE5B,MAAMF,OAAO,CAACK,KAAK,CAAC/F,UAAA,CAAA8F,SAAS,CAAC;IAC9B,IAAI,CAAClF,KAAK,CAAC2D,aAAa,GAAG,EAAE;EACjC;EAEA;;;;;;;EAOA,MAAM3B,OAAOA,CACTpC,cAAoD,EACpDwC,WAAA,GAAsC,EAAE,EACxCgD,aAAA,GAAuC,EAAE;;IAGzC,MAAMC,YAAY,GAAGpG,KAAA,CAAAQ,KAAM,CAACC,MAAM,CAAC,gBAAgB,CAAC;IACpD,IAAAT,KAAA,CAAAuB,MAAM,EAACZ,cAAc,EAAE,wDAAwD,CAAC;IAEhF;IACA,IAAIyB,GAAW;IACf,IAAI,OAAOzB,cAAc,IAAI,QAAQ,IAAIA,cAAc,YAAYwB,GAAG,EAAE;MACpEC,GAAG,GAAGiE,MAAM,CAAC1F,cAAc,CAAC;MAC5BA,cAAc,GAAG,EAA+B;KACnD,MACI;MACDyB,GAAG,GAAGiE,MAAM,CAAC1F,cAAc,CAACyB,GAAG,CAAC;;IAGpCA,GAAG,GAAG,IAAApC,KAAA,CAAAc,QAAQ,EAACsB,GAAG,EAAE,IAAI,CAACrB,KAAK,CAACC,SAAS,CAAC;IAEzC,MAAMoC,OAAO,GAAG;MACZkD,KAAK,EAAEnD,WAAW,CAACmD,KAAK,KAAK,KAAK;MAClCC,IAAI,EAAG,CAAC,CAACpD,WAAW,CAACoD,IAAI;MACzBC,SAAS,EAAE,CAAAC,EAAA,GAAAtD,WAAW,CAACqD,SAAS,cAAAC,EAAA,cAAAA,EAAA,GAAI,CAAC;MACrCC,iBAAiB,EAAE,IAAA1G,KAAA,CAAA2G,SAAS,EAACxD,WAAW,CAACuD,iBAAiB,IAAI,EAAE,CAAa;MAC7EE,eAAe,EAAEzD,WAAW,CAACyD,eAAe,KAAK,KAAK;MACtDC,MAAM,EAAE,OAAO1D,WAAW,CAAC0D,MAAM,IAAI,UAAU,GAC3C1D,WAAW,CAAC0D,MAE8C,GAC1DC;KACP;IAED,MAAMC,MAAM,GAAIpG,cAA8B,CAACoG,MAAM,IAAID,SAAS;IAElE;IACA,IAAI1D,OAAO,CAACwD,eAAe,EAAE;MACzB,MAAM,IAAI,CAACI,eAAe,CAAC;QAAED;MAAM,CAAE,CAAC;;IAG1C;IACA;IACA,MAAME,UAAU,GAAG,IAAI,CAACvB,sBAAsB,EAAE;IAChD,IAAIuB,UAAU,EAAE;MACZtG,cAAc,CAACuG,OAAO,GAAG;QACrB,GAAGvG,cAAc,CAACuG,OAAO;QACzBC,aAAa,EAAEF;OAClB;;IAGLb,YAAY,CAAC,kCAAkC,EAAEhE,GAAG,EAAEzB,cAAc,EAAEyC,OAAO,CAAC;IAE9E,IAAIgE,QAA8B;IAElC,OAAO,KAAK,CAACC,WAAW,CAAyBjF,GAAG,EAAEzB,cAAc,CAAC,CAAC2G,IAAI,CAACC,MAAM,IAAG;MAChF,IAAK5G,cAA4C,CAAC6G,eAAe,EAAE;QAC/DJ,QAAQ,GAAIG,MAAyC,CAACH,QAAQ;QAC9D,OAAQG,MAAyC,CAACE,IAAI;;MAE1D,OAAOF,MAAM;IACjB,CAAC;IAED;IAAA,CACCG,KAAK,CAAC,MAAOC,KAAgB,IAAI;MAC9B,IAAIA,KAAK,CAACC,MAAM,IAAI,GAAG,EAAE;QAErB;QACA,IAAI,CAAC,IAAI,CAAC1D,QAAQ,CAAC,4BAA4B,CAAC,EAAE;UAC9CyD,KAAK,CAACE,OAAO,IAAI,wEAAwE;UACzF,MAAMF,KAAK;;QAGf;QACA;QACA,IAAI,CAACvE,OAAO,CAACwD,eAAe,EAAE;UAC1BR,YAAY,CAAC,oGAAoG,CAAC;UAClH,MAAM,IAAI,CAACR,WAAW,EAAE;UACxB+B,KAAK,CAACE,OAAO,IAAI,IAAI,GAAG3H,SAAA,CAAAoC,OAAG,CAACwF,OAAO;UACnC,MAAMH,KAAK;;QAGf;QACA;QACA;QAEA;QACA;QACAvB,YAAY,CAAC,gDAAgD,CAAC;QAC9D,MAAM,IAAI,CAACR,WAAW,EAAE;QACxB+B,KAAK,CAACE,OAAO,IAAI,IAAI,GAAG3H,SAAA,CAAAoC,OAAG,CAACwF,OAAO;QACnC,MAAMH,KAAK;;MAEf,MAAMA,KAAK;IACf,CAAC;IAED;IAAA,CACCD,KAAK,CAAEC,KAAgB,IAAI;MACxB,IAAIA,KAAK,CAACC,MAAM,IAAI,GAAG,EAAE;QACrBxB,YAAY,CAAC,gFAAgF,CAAC;;MAElG,MAAMuB,KAAK;IACf,CAAC,CAAC,CAEDL,IAAI,CAAC,MAAOS,IAAS,IAAI;MAEtB;MACA;MACA;MACA,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,IAAI,QAAQ,IAAIA,IAAI,YAAY1H,QAAQ,EAAE;QAC9D,IAAKM,cAA0C,CAAC6G,eAAe,EAAE;UAC7D,OAAO;YACHC,IAAI,EAAEM,IAAI;YACVX;WACH;;QAEL,OAAOW,IAAI;;MAGf;MACA,MAAM,IAAI,CAACC,eAAe,CACtBD,IAAW,EACX3E,OAAO,CAACsD,iBAAiB,EACzBtD,OAAO,CAACkD,KAAK,EACbH,aAAa,EACbxF,cAA2C,CAC9C;MAED,OAAOqC,OAAO,CAACiF,OAAO,CAACF,IAAI;MAE3B;MAAA,CACCT,IAAI,CAAC,MAAMY,KAAK,IAAG;QAChB,IAAIA,KAAK,IAAIA,KAAK,CAAC/G,YAAY,IAAI,QAAQ,EAAE;UACzC,MAAMgH,KAAK,GAAID,KAAK,CAACE,IAAI,IAAI,EAAmC;UAEhE,IAAIhF,OAAO,CAACmD,IAAI,EAAE;YACd2B,KAAK,GAAG,CAACA,KAAK,CAACG,KAAK,IAAI,EAAE,EAAEC,GAAG,CAC1BD,KAAkC,IAAKA,KAAK,CAACE,QAAQ,CACzD;;UAGL,IAAInF,OAAO,CAACyD,MAAM,EAAE;YAChB,MAAMzD,OAAO,CAACyD,MAAM,CAACqB,KAAK,EAAE;cAAE,GAAG/B;YAAa,CAAE,CAAC;;UAGrD,IAAI,EAAE/C,OAAO,CAACoD,SAAS,EAAE;YACrB,MAAMgC,IAAI,GAAGL,KAAK,CAACM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,IAAI,MAAM,CAAC;YAClDT,KAAK,GAAG,IAAAlI,KAAA,CAAA2G,SAAS,EAACuB,KAAK,CAAC;YACxB,IAAIM,IAAI,IAAIA,IAAI,CAACpG,GAAG,EAAE;cAClB,MAAMwG,QAAQ,GAAG,MAAM,IAAI,CAAC7F,OAAO,CAC/B;gBACIX,GAAG,EAAEoG,IAAI,CAACpG,GAAG;gBAEb;gBACA;gBACA;gBACA;gBACA2E;eACH,EACD3D,OAAO,EACP+C,aAAa,CAChB;cAED,IAAI/C,OAAO,CAACyD,MAAM,EAAE;gBAChB,OAAO,IAAI;;cAGf,IAAIzD,OAAO,CAACsD,iBAAiB,CAACmC,MAAM,EAAE;gBAClCC,MAAM,CAACC,MAAM,CAAC5C,aAAa,EAAEyC,QAAQ,CAACI,UAAU,CAAC;gBACjD,OAAOd,KAAK,CAACe,MAAM,CAAC,IAAAjJ,KAAA,CAAA2G,SAAS,EAACiC,QAAQ,CAACb,IAAI,IAAIa,QAAQ,CAAC,CAAC;;cAE7D,OAAOV,KAAK,CAACe,MAAM,CAAC,IAAAjJ,KAAA,CAAA2G,SAAS,EAACiC,QAAQ,CAAC,CAAC;;;;QAIpD,OAAOV,KAAK;MAChB,CAAC;MAED;MAAA,CACCZ,IAAI,CAACY,KAAK,IAAG;QACV,IAAI9E,OAAO,CAACkD,KAAK,EAAE;UACfH,aAAa,GAAG,EAAE;SACrB,MACI,IAAI,CAAC/C,OAAO,CAACyD,MAAM,IAAIzD,OAAO,CAACsD,iBAAiB,CAACmC,MAAM,EAAE;UAC1D,OAAO;YACHd,IAAI,EAAEG,KAAK;YACXc,UAAU,EAAE7C;WACf;;QAEL,OAAO+B,KAAK;MAChB,CAAC,CAAC,CACDZ,IAAI,CAACY,KAAK,IAAG;QACV,IAAKvH,cAA0C,CAAC6G,eAAe,EAAE;UAC7D,OAAO;YACHC,IAAI,EAAES,KAAK;YACXd;WACH;;QAEL,OAAOc,KAAK;MAChB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEA;;;;;;;;;EASAlB,eAAeA,CAACrG,cAAA,GAA8B,EAAE;IAE5C,MAAMsD,WAAW,GAAI,IAAI,CAACC,QAAQ,CAAC,4BAA4B,CAAC;IAChE,MAAMgF,YAAY,GAAG,IAAI,CAAChF,QAAQ,CAAC,6BAA6B,CAAC;IACjE,MAAMiF,SAAS,GAAM,IAAI,CAACpI,KAAK,CAACoI,SAAS,IAAI,CAAC;IAE9C,IAAIlF,WAAW,IAAIiF,YAAY,IAAIC,SAAS,GAAG,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,EAAE;MACnE,OAAO,IAAI,CAACC,OAAO,CAAC3I,cAAc,CAAC;;IAGvC,OAAOqC,OAAO,CAACiF,OAAO,CAAC,IAAI,CAAClH,KAAK,CAAC;EACtC;EAEA;;;;;;;;;;;;;EAaAuI,OAAOA,CAAC3I,cAAA,GAA8B,EAAE;;IAEpC,MAAM4I,YAAY,GAAGvJ,KAAA,CAAAQ,KAAM,CAACC,MAAM,CAAC,gBAAgB,CAAC;IACpD8I,YAAY,CAAC,6CAA6C,CAAC;IAE3D,MAAML,YAAY,GAAG,CAAAM,EAAA,IAAA/C,EAAA,OAAI,CAAC1F,KAAK,cAAA0F,EAAA,uBAAAA,EAAA,CAAE/B,aAAa,cAAA8E,EAAA,uBAAAA,EAAA,CAAEC,aAAa;IAC7D,IAAAzJ,KAAA,CAAAuB,MAAM,EAAC2H,YAAY,EAAE,4CAA4C,CAAC;IAElE,MAAMQ,QAAQ,GAAG,IAAI,CAAC3I,KAAK,CAAC2I,QAAQ;IACpC,IAAA1J,KAAA,CAAAuB,MAAM,EAACmI,QAAQ,EAAE,uCAAuC,CAAC;IAEzD,MAAMC,MAAM,GAAG,IAAI,CAACzF,QAAQ,CAAC,qBAAqB,CAAC,IAAI,EAAE;IACzD,MAAM0F,gBAAgB,GAAGD,MAAM,CAACE,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;IACjE,MAAMC,eAAe,GAAGH,MAAM,CAACE,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;IAC/D,IAAA7J,KAAA,CAAAuB,MAAM,EAACqI,gBAAgB,IAAIE,eAAe,EAAE,oEAAoE,CAAC;IAEjH;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAClH,YAAY,EAAE;MACpB,IAAI6E,IAAI,GAAG,0CAA0CsC,kBAAkB,CAACb,YAAY,CAAC,EAAE;MACvF,IAAI,IAAI,CAAC1G,WAAW,CAACY,OAAO,CAAC4G,wBAAwB,EAAE;QACnDvC,IAAI,IAAI,cAAc,IAAI,CAAC1G,KAAK,CAACkJ,QAAQ,EAAE;;MAE/C,MAAMC,qBAAqB,GAAG;QAC1BC,WAAW,EAAE,IAAI,CAAC3H,WAAW,CAACY,OAAO,CAACgH,2BAA2B,IAAI,aAAa;QAClF,GAAGzJ,cAAc;QACjB0J,MAAM,EAAG,MAAM;QACfC,IAAI,EAAK,MAAqB;QAC9BpD,OAAO,EAAE;UACL,IAAIvG,cAAc,CAACuG,OAAO,IAAI,EAAE,CAAC;UACjC,cAAc,EAAE;SACnB;QACDO,IAAI,EAAEA;OACT;MAED;MACA,IAAI,EAAE,eAAe,IAAIyC,qBAAqB,CAAChD,OAAO,CAAC,EAAE;QACrD,MAAM;UAAEqD,YAAY;UAAEN;QAAQ,CAAE,GAAG,IAAI,CAAClJ,KAAK;QAC7C,IAAIwJ,YAAY,EAAE;UACd;UACAL,qBAAqB,CAAChD,OAAO,CAACC,aAAa,GAAG,QAAQ,GAAG,IAAI,CAAC3E,WAAW,CAACmD,IAAI,CAC1EsE,QAAQ,GAAG,GAAG,GAAGM,YAAY,CAChC;;;MAIT,IAAI,CAAC3H,YAAY,GAAG,IAAA5C,KAAA,CAAA+C,OAAO,EAA2B2G,QAAQ,EAAEQ,qBAAqB,CAAC,CACrF5C,IAAI,CAACS,IAAI,IAAG;QACT,IAAA/H,KAAA,CAAAuB,MAAM,EAACwG,IAAI,CAACyC,YAAY,EAAE,0BAA0B,CAAC;QACrDjB,YAAY,CAAC,uCAAuC,EAAExB,IAAI,CAAC;QAC3D,IAAI,CAAChH,KAAK,CAAC2D,aAAa,GAAG;UAAE,GAAG,IAAI,CAAC3D,KAAK,CAAC2D,aAAa;UAAE,GAAGqD;QAAI,CAAE;QACnE,IAAI,CAAChH,KAAK,CAACoI,SAAS,GAAG,IAAAnJ,KAAA,CAAAyK,wBAAwB,EAAC1C,IAAI,EAAE,IAAI,CAACvF,WAAW,CAAC;QACvE,OAAO,IAAI,CAACzB,KAAK;MACrB,CAAC,CAAC,CACD2G,KAAK,CAAEC,KAAY,IAAI;;QACpB,IAAI,CAAA6B,EAAA,IAAA/C,EAAA,OAAI,CAAC1F,KAAK,cAAA0F,EAAA,uBAAAA,EAAA,CAAE/B,aAAa,cAAA8E,EAAA,uBAAAA,EAAA,CAAEC,aAAa,EAAE;UAC1CF,YAAY,CAAC,gDAAgD,CAAC;UAC9D,OAAO,IAAI,CAACxI,KAAK,CAAC2D,aAAa,CAAC+E,aAAa;;QAEjD,MAAM9B,KAAK;MACf,CAAC,CAAC,CACD+C,OAAO,CAAC,MAAK;QACV,IAAI,CAAC9H,YAAY,GAAG,IAAI;QACxB,MAAMmD,GAAG,GAAG,IAAI,CAAChF,KAAK,CAACgF,GAAG;QAC1B,IAAIA,GAAG,EAAE;UACL,IAAI,CAACvD,WAAW,CAACsD,UAAU,EAAE,CAAC/D,GAAG,CAACgE,GAAG,EAAE,IAAI,CAAChF,KAAK,CAAC;SACrD,MAAM;UACHwI,YAAY,CAAC,6DAA6D,CAAC;;MAEnF,CAAC,CAAC;;IAGN,OAAO,IAAI,CAAC3G,YAAY;EAC5B;EAEA;EAEA;;;;;;;;;;;;;;;;EAgBA+H,MAAMA,CACFC,YAAyE,EACzEC,QAAgB;IAGhB,OAAO,IAAA7K,KAAA,CAAA2K,MAAM,EAACC,YAAY,EAAEC,QAAQ,CAAC;EACzC;EAEA;;;;;;;;;;;;;;;;;EAiBAC,OAAOA,CACHF,YAAyE,EACzEC,QAAgB;IAGhB,OAAO,IAAA7K,KAAA,CAAA8K,OAAO,EAACF,YAAY,EAAEC,QAAQ,CAAC;EAC1C;EAOA;;;;;;;;;;;;;EAaAE,OAAOA,CAACC,GAAwB,EAAEC,IAAI,GAAG,EAAE;IACvC,OAAO,IAAAjL,KAAA,CAAA+K,OAAO,EAACC,GAAG,EAAEC,IAAI,CAAC;EAC7B;EAEA;;;;;;;;;;;;EAYA/G,QAAQA,CAAC+G,IAAI,GAAG,EAAE;IACd,OAAO,IAAAjL,KAAA,CAAA+K,OAAO,EAAC;MAAE,GAAG,IAAI,CAAChK;IAAK,CAAE,EAAEkK,IAAI,CAAC;EAC3C;;AA/0BJC,kBAAA,GAAA7I,MAAA;;;;;;;;;;;;;;;;ACzEA,MAAAlC,UAAA,GAAAF,mBAAA;AAEA,MAAAD,KAAA,GAAAC,mBAAA;AAaA,MAAMO,KAAK,GAAGR,KAAA,CAAAQ,KAAM,CAACC,MAAM,CAAC,YAAY,CAAC;AAyBzC;;;AAGA,MAAqB0K,UAAU;EAU3B;;;;EAIA5I,YAAY6I,WAAmB;IAE3B,IAAApL,KAAA,CAAAuB,MAAM,EACF6J,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAAC1I,KAAK,CAAC,eAAe,CAAC,EACpF,gFAAgF,CACnF;IACD,IAAI,CAAC0I,WAAW,GAAGA,WAAW;EAClC;EAEA;;;;;;;;EAQA,MAAMC,MAAMA,CACR9C,QAAkC,EAClC5H,cAAkB;IAGlB,OAAO,IAAI,CAAC0G,WAAW,CAACkB,QAAQ,CAACpH,YAAa,EAAE;MAC5C,GAAGR,cAAc;MACjB0J,MAAM,EAAE,MAAM;MACd5C,IAAI,EAAE6D,IAAI,CAACC,SAAS,CAAChD,QAAQ,CAAC;MAC9BrB,OAAO,EAAE;QACL,cAAc,EAAE,kBAAkB;QAClC,GAAG,CAACvG,cAAc,IAAI,EAAE,EAAEuG;;KAEjC,CAAC;EACN;EAEA;;;;;;;;;EASA,MAAMsE,MAAMA,CACRjD,QAAkC,EAClC5H,cAAkB;IAGlB,OAAO,IAAI,CAAC0G,WAAW,CAAC,GAAGkB,QAAQ,CAACpH,YAAY,IAAIoH,QAAQ,CAACtG,EAAE,EAAE,EAAE;MAC/D,GAAGtB,cAAc;MACjB0J,MAAM,EAAE,KAAK;MACb5C,IAAI,EAAE6D,IAAI,CAACC,SAAS,CAAChD,QAAQ,CAAC;MAC9BrB,OAAO,EAAE;QACL,cAAc,EAAE,kBAAkB;QAClC,GAAG,CAACvG,cAAc,IAAI,EAAE,EAAEuG;;KAEjC,CAAC;EACN;EAEA;;;;;;;;;EASA,MAAMuE,MAAMA,CAAcrJ,GAAW,EAAEzB,cAAA,GAA0C,EAAE;IAE/E,OAAO,IAAI,CAAC0G,WAAW,CAAIjF,GAAG,EAAE;MAAE,GAAGzB,cAAc;MAAE0J,MAAM,EAAE;IAAQ,CAAE,CAAC;EAC5E;EAEA;;;;;;;;;;;;;;;;;;EAkBA,MAAMqB,KAAKA,CACPtJ,GAAW,EACXsJ,KAA2B,EAC3B/K,cAAA,GAA0C,EAAE;IAG5C,IAAAX,KAAA,CAAA2L,eAAe,EAACD,KAAK,CAAC;IACtB,OAAO,IAAI,CAACrE,WAAW,CAAcjF,GAAG,EAAE;MACtC,GAAGzB,cAAc;MACjB0J,MAAM,EAAE,OAAO;MACf5C,IAAI,EAAE6D,IAAI,CAACC,SAAS,CAACG,KAAK,CAAC;MAC3BxE,OAAO,EAAE;QACL,QAAQ,EAAE,qBAAqB;QAC/B,cAAc,EAAE,4CAA4C;QAC5D,GAAGvG,cAAc,CAACuG;;KAEzB,CAAC;EACN;EAEQ,MAAM0E,UAAUA,CACpBZ,GAAe,EACfC,IAAa,EACb3E,KAAc,EACduF,KAA0B,EAC1BlL,cAAA,GAAyD,EAAE;IAE3D,MAAMmL,IAAI,GAAG,IAAA9L,KAAA,CAAA+K,OAAO,EAACC,GAAG,EAAEC,IAAI,CAAC;IAC/B,IAAIa,IAAI,EAAE;MACN,MAAMC,OAAO,GAAGC,KAAK,CAACD,OAAO,CAACD,IAAI,CAAC;MACnC,OAAO9I,OAAO,CAACiJ,GAAG,CAAC,IAAAjM,KAAA,CAAA2G,SAAS,EAACmF,IAAI,CAAC,CAACI,MAAM,CAACC,OAAO,CAAC,CAAC7D,GAAG,CAAC,CAAC8D,IAAI,EAAEC,CAAC,KAAI;QAC/D,MAAMC,GAAG,GAAGF,IAAI,CAACG,SAAS;QAC1B,IAAID,GAAG,EAAE;UACL,OAAO,IAAI,CAACjF,WAAW,CAACiF,GAAG,EAAE;YAAE,GAAG3L,cAAc;YAAE6G,eAAe,EAAE,KAAK;YAAEgF,QAAQ,EAAEX;UAAK,CAAE,CAAC,CAACvE,IAAI,CAACmF,GAAG,IAAG;YACpG,IAAInG,KAAK,EAAE;cACP,IAAIyF,OAAO,EAAE;gBACT,IAAId,IAAI,CAACxJ,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;kBACzB,IAAAzB,KAAA,CAAA0M,OAAO,EAAC1B,GAAG,EAAE,GAAGC,IAAI,CAACjH,OAAO,CAAC,IAAI,EAAE,IAAIqI,CAAC,GAAG,CAAC,EAAE,EAAEI,GAAG,CAAC;iBACvD,MAAM;kBACH,IAAAzM,KAAA,CAAA0M,OAAO,EAAC1B,GAAG,EAAE,GAAGC,IAAI,IAAIoB,CAAC,EAAE,EAAEI,GAAG,CAAC;;eAExC,MAAM;gBACH,IAAAzM,KAAA,CAAA0M,OAAO,EAAC1B,GAAG,EAAEC,IAAI,EAAEwB,GAAG,CAAC;;;UAGnC,CAAC,CAAC,CAAC/E,KAAK,CAAEiF,EAAE,IAAI;YACZ,IAAI,CAAAA,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAE/E,MAAM,MAAK,GAAG,EAAE;cACpBgF,OAAO,CAACC,IAAI,CAAC,qBAAqBP,GAAG,KAAKK,EAAE,EAAE,CAAC;aAClD,MAAM;cACH,MAAMA,EAAE;;UAEhB,CAAC,CAAC;;MAEV,CAAC,CAAC,CAAC;;EAEX;EAEA;;;;EAIA,MAAMjG,iBAAiBA,CACnB6B,QAAkB,EAClBS,UAAoB,EACpBrI,cAAA,GAAyD,EAAE;IAE3D,MAAM,IAAI,CAACqH,eAAe,CAACO,QAAQ,EAAES,UAAU,EAAE,IAAI,EAAE,EAAE,EAAErI,cAAc,CAAC;EAC9E;EAEU,MAAMqH,eAAeA,CAC3BO,QAAkB,EAClBS,UAAoB,EACpB1C,KAAc,EACduF,KAAA,GAA6B,EAAE,EAC/BlL,cAAA,GAAyD,EAAE;IAG3D,IAAI4H,QAAQ,CAACpH,YAAY,IAAI,QAAQ,EAAE;MACnC,KAAK,MAAMiL,IAAI,IAAM7D,QAAmB,CAACF,KAAK,IAAI,EAAE,EAAG;QACnD,IAAI+D,IAAI,CAAC7D,QAAQ,EAAE;UACf,MAAM,IAAI,CAACP,eAAe,CAACoE,IAAI,CAAC7D,QAAQ,EAAES,UAAU,EAAE1C,KAAK,EAAEuF,KAAK,EAAElL,cAAc,CAAC;;;MAG3F,OAAOkL,KAAK;;IAGhB;IACA,IAAIiB,KAAK,GAAG9D,UAAU,CAACV,GAAG,CAAC2C,IAAI,IAAI5E,MAAM,CAAC4E,IAAI,CAAC,CAAC8B,IAAI,EAAE,CAAC,CAACb,MAAM,CAACC,OAAO,CAAC;IAEvE;IACAW,KAAK,GAAGA,KAAK,CAACE,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAI;MAC/B,IAAID,IAAI,CAACE,QAAQ,CAACD,GAAG,CAAC,EAAE;QACpB1M,KAAK,CAAC,kCAAkC,EAAE0M,GAAG,CAAC;OACjD,MAAM;QACHD,IAAI,CAACG,IAAI,CAACF,GAAG,CAAC;;MAElB,OAAOD,IAAI;IACf,CAAC,EAAE,EAAc,CAAC;IAElB;IACA,IAAI,CAACH,KAAK,CAACjE,MAAM,EAAE;MACf,OAAO7F,OAAO,CAACiF,OAAO,CAAC4D,KAAK,CAAC;;IAGjC;IACA;IACA,MAAMwB,MAAM,GAAwB,EAAE;IACtCP,KAAK,CAACQ,OAAO,CAACrC,IAAI,IAAG;MACjB,MAAMsC,GAAG,GAAGtC,IAAI,CAAC5J,KAAK,CAAC,GAAG,CAAC,CAACwH,MAAM;MAClC,IAAI,CAACwE,MAAM,CAACE,GAAG,CAAC,EAAE;QACdF,MAAM,CAACE,GAAG,CAAC,GAAG,EAAE;;MAEpBF,MAAM,CAACE,GAAG,CAAC,CAACH,IAAI,CAACnC,IAAI,CAAC;IAC1B,CAAC,CAAC;IAEF;IACA;IACA,IAAIuC,IAAI,GAAiBxK,OAAO,CAACiF,OAAO,EAAE;IAC1Ca,MAAM,CAAC2E,IAAI,CAACJ,MAAM,CAAC,CAACK,IAAI,EAAE,CAACJ,OAAO,CAACC,GAAG,IAAG;MACrC,MAAMI,KAAK,GAAGN,MAAM,CAACE,GAAG,CAAC;MACzBC,IAAI,GAAGA,IAAI,CAAClG,IAAI,CAAC,MAAMtE,OAAO,CAACiJ,GAAG,CAAC0B,KAAK,CAACrF,GAAG,CAAE2C,IAAY,IAAI;QAC1D,OAAO,IAAI,CAACW,UAAU,CAACrD,QAAQ,EAAE0C,IAAI,EAAE3E,KAAK,EAAEuF,KAAK,EAAElL,cAAc,CAAC;MACxE,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC;IACF,MAAM6M,IAAI;IACV,OAAO3B,KAAK;EAChB;EAEA;;;EAGA,MAAM+B,aAAaA,CACfrF,QAAkB,EAClBS,UAAoB,EACpBrI,cAAA,GAAyD,EAAE;IAE3D,MAAMkN,IAAI,GAAG,MAAM,IAAI,CAAC7F,eAAe,CAACO,QAAQ,EAAES,UAAU,EAAE,KAAK,EAAE,EAAE,EAAErI,cAAc,CAAC;IACxF,MAAMmN,GAAG,GAAQ,EAAE;IACnB,KAAK,MAAM/H,GAAG,IAAI8H,IAAI,EAAE;MACpBC,GAAG,CAAC/H,GAAG,CAAC,GAAG,MAAM8H,IAAI,CAAC9H,GAAG,CAAC;;IAE9B,OAAO+H,GAAG;EACd;EAEA;;;;;EAKA,OAAOC,SAASA,CAACC,WAAkC,EAAE5K,OAAwB;IACzE,IAAI6K,KAAK,GAAG,CAAC;IACb,WAAU,MAAMC,IAAI,IAAI,IAAI,CAACC,KAAK,CAACH,WAAW,EAAE5K,OAAO,CAAC,EAAE;MACtD,KAAK,MAAMiF,KAAK,IAAK6F,IAAI,CAAC7F,KAAK,IAAI,EAAE,EAAG;QACpC,IAAI,CAAAjF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgL,KAAK,KAAI,EAAEH,KAAK,GAAG7K,OAAO,CAACgL,KAAK,EAAE;UAC3C;;QAEJ,MAAM/F,KAAK,CAACE,QAAQ;;;EAGhC;EAEA;;;;;;EAMA,OAAO4F,KAAKA,CACRH,WAAsC,EACtCrN,cAA+B;;IAE/B,MAAM;MAAEyN,KAAK;MAAE,GAAGhL;IAAO,CAAE,GAAGzC,cAAc,IAAI,EAAE;IAElD,MAAM0N,SAAS,GAAIjM,GAAiB,IAAK,IAAI,CAACiF,WAAW,CAACjF,GAAG,EAAEgB,OAAO,CAAC;IAEvE,IAAI8K,IAAI,GAAW,OAAOF,WAAW,KAAK,QAAQ,IAAIA,WAAW,YAAY7L,GAAG,GAC5E,MAAMkM,SAAS,CAACL,WAAW,CAAC,GAC5BA,WAAW;IAEf,IAAIC,KAAK,GAAG,CAAC;IAEb,OAAOC,IAAI,IAAIA,IAAI,CAAC/M,YAAY,KAAK,QAAQ,KAAK,CAACiN,KAAK,IAAI,EAAEH,KAAK,IAAIG,KAAK,CAAC,EAAE;MAE3E;MACA,MAAMF,IAAI;MAEV;MACA,IAAI,CAAAzH,EAAA,GAAArD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2D,MAAM,cAAAN,EAAA,uBAAAA,EAAA,CAAE6H,OAAO,EAAE;QAC1B;;MAGJ;MACA,MAAMC,QAAQ,GAAG,CAAC,CAAA/E,EAAA,GAAA0E,IAAI,CAAC9F,IAAI,cAAAoB,EAAA,cAAAA,EAAA,GAAI,EAAE,EAAEf,IAAI,CAClCC,CAAa,IAAKA,CAAC,CAACC,QAAQ,KAAK,MAAM,IAAI,OAAOD,CAAC,CAACtG,GAAG,KAAK,QAAQ,CACxE;MAED,IAAI,CAACmM,QAAQ,EAAE;QACX,MAAM,CAAC;;MAGX;MACAL,IAAI,GAAG,MAAMG,SAAS,CAACE,QAAQ,CAACnM,GAAI,CAAC;;EAE7C;EAEA;;;EAGA,MAAMiF,WAAWA,CAAUmH,GAAiB,EAAEpL,OAAA,GAA0B,EAAE;IAEtE,IAAApD,KAAA,CAAAuB,MAAM,EAAC6B,OAAO,EAAE,8CAA8C,CAAC;IAE/D,MAAM6H,IAAI,GAAGuD,GAAG,GAAG,EAAE;IACrB,MAAMpM,GAAG,GAAI,IAAApC,KAAA,CAAAc,QAAQ,EAACmK,IAAI,EAAE,IAAI,CAACG,WAAW,CAAC;IAC7C,MAAM;MAAEoB;IAAQ,CAAE,GAAGpJ,OAAO;IAE5B,IAAIoJ,QAAQ,EAAE;MACV,IAAI,EAAEvB,IAAI,IAAIuB,QAAQ,CAAC,EAAE;QACrBA,QAAQ,CAACvB,IAAI,CAAC,GAAG,IAAAjL,KAAA,CAAA+C,OAAO,EAAIX,GAAG,EAAEgB,OAAO,CAAC,CACxCkE,IAAI,CAACmH,GAAG,IAAG;UACRjC,QAAQ,CAACvB,IAAI,CAAC,GAAGwD,GAAG;UACpB,OAAOA,GAAG;QACd,CAAC,CAAC,CACD/G,KAAK,CAACC,KAAK,IAAG;UACX,OAAO6E,QAAQ,CAACvB,IAAI,CAAC;UACrB,MAAMtD,KAAK;QACf,CAAC,CAAC;;MAEN,OAAO6E,QAAQ,CAACvB,IAAI,CAAC;;IAEzB,OAAO,IAAAjL,KAAA,CAAA+C,OAAO,EAAIX,GAAG,EAAEgB,OAAO,CAAC;EACnC;EAEA;;;;EAIA,MAAMsL,cAAcA,CAAA;IAEhB,OAAO,IAAA1O,KAAA,CAAA2B,yBAAyB,EAAC,IAAI,CAACyJ,WAAW,CAAC,CAC7C9D,IAAI,CAAEqH,QAAQ,IAAKA,QAAQ,CAACC,WAAW,CAAC;EACjD;EAEA;;;;;;;EAOA,MAAMC,cAAcA,CAAA;IAEhB,OAAO,IAAI,CAACH,cAAc,EAAE,CAACpH,IAAI,CAACwH,CAAC,IAAG;MAAA,IAAArI,EAAA;MAAC,QAAAA,EAAA,GAACtG,UAAA,CAAA4O,YAAoB,CAACD,CAAC,CAAC,cAAArI,EAAA,cAAAA,EAAA,GAAI,CAAC;IAAA,EAAC;EACzE;;AApWJyE,kBAAA,GAAAC,UAAA;;;;;;;;;;;;;;;;ACzCA,MAAqB6D,SAAU,SAAQ9L,KAAK;EAwBxCX,YAAY6E,QAAkB;IAC1B,KAAK,CAAC,GAAGA,QAAQ,CAACQ,MAAM,IAAIR,QAAQ,CAAC6H,UAAU,UAAU7H,QAAQ,CAAChF,GAAG,EAAE,CAAC;IACxE,IAAI,CAAC8M,IAAI,GAAS,WAAW;IAC7B,IAAI,CAAC9H,QAAQ,GAAKA,QAAQ;IAC1B,IAAI,CAAC+H,UAAU,GAAG/H,QAAQ,CAACQ,MAAM;IACjC,IAAI,CAACA,MAAM,GAAOR,QAAQ,CAACQ,MAAM;IACjC,IAAI,CAACqH,UAAU,GAAG7H,QAAQ,CAAC6H,UAAU;EACzC;EAEA,MAAMG,KAAKA,CAAA;IAEP,IAAI,CAAC,IAAI,CAAChI,QAAQ,CAACiI,QAAQ,EAAE;MACzB,IAAI;QACA,MAAMC,IAAI,GAAG,IAAI,CAAClI,QAAQ,CAACF,OAAO,CAAClB,GAAG,CAAC,cAAc,CAAC,IAAI,YAAY;QACtE,IAAIsJ,IAAI,CAAC5M,KAAK,CAAC,WAAW,CAAC,EAAE;UACzB,IAAI+E,IAAI,GAAG,MAAM,IAAI,CAACL,QAAQ,CAACmI,IAAI,EAAE;UACrC,IAAI9H,IAAI,CAACE,KAAK,EAAE;YACZ,IAAI,CAACE,OAAO,IAAI,IAAI,GAAGJ,IAAI,CAACE,KAAK;YACjC,IAAIF,IAAI,CAAC+H,iBAAiB,EAAE;cACxB,IAAI,CAAC3H,OAAO,IAAI,IAAI,GAAGJ,IAAI,CAAC+H,iBAAiB;;WAEpD,MACI;YACD,IAAI,CAAC3H,OAAO,IAAI,MAAM,GAAGyD,IAAI,CAACC,SAAS,CAAC9D,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;;SAE7D,MACI,IAAI6H,IAAI,CAAC5M,KAAK,CAAC,UAAU,CAAC,EAAE;UAC7B,IAAI+E,IAAI,GAAG,MAAM,IAAI,CAACL,QAAQ,CAACqI,IAAI,EAAE;UACrC,IAAIhI,IAAI,EAAE;YACN,IAAI,CAACI,OAAO,IAAI,MAAM,GAAGJ,IAAI;;;OAGxC,CAAC,MAAM;QACJ;MAAA;;IAIR,OAAO,IAAI;EACf;EAEAiI,MAAMA,CAAA;IACF,OAAO;MACHR,IAAI,EAAQ,IAAI,CAACA,IAAI;MACrBC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BvH,MAAM,EAAM,IAAI,CAACA,MAAM;MACvBqH,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BpH,OAAO,EAAK,IAAI,CAACA;KACpB;EACL;;AAxEJqD,kBAAA,GAAA8D,SAAA;;;;;;;;;;;;;;;;ACHA,MAAAW,OAAA,GAAA1P,mBAAA;AACA,MAAA2P,QAAA,GAAA3P,mBAAA;AACA,MAAA4P,gBAAA,GAAA5P,mBAAA;AAEA,MAAA6P,QAAA,GAAA7P,mBAAA;AACA,MAAA8P,WAAA,GAAA9P,mBAAA;AAEA;;;AAGA,MAAqB+P,cAAc;EAmB/B;;;EAGAzN,YAAYa,OAAA,GAA0C,EAAE;IApBxD;;;IAGQ,KAAAlC,IAAI,GAAe,IAAI;IAE/B;;;IAGQ,KAAA+O,QAAQ,GAA8B,IAAI;IAOlD,KAAAH,QAAQ,GAAGA,QAAQ;IAOf,IAAI,CAAC1M,OAAO,GAAG;MACX;MACA;MACA8M,qBAAqB,EAAE,IAAI;MAE3B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC,yBAAyB,EAAE,IAAI;MAE/B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA/F,2BAA2B,EAAE,aAAa;MAE1C,GAAGhH;KACN;EACL;EAEA;;;EAGAgN,QAAQA,CAACnF,IAAY;IAEjB,OAAO,IAAI9I,GAAG,CAAC8I,IAAI,EAAE,IAAI,CAACoF,MAAM,EAAE,CAACnO,IAAI,CAAC,CAACA,IAAI;EACjD;EAEA;;;;;EAKA,IAAI2B,IAAIA,CAAA;IAEJ;IACA,OAAO,OAAOA,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAG,IAAI;EACnD;EAEA;;;;EAIAwM,MAAMA,CAAA;IAEF,IAAI,CAAC,IAAI,CAACnP,IAAI,EAAE;MACZ,IAAI,CAACA,IAAI,GAAG,IAAIiB,GAAG,CAACmO,QAAQ,GAAG,EAAE,CAAC;;IAEtC,OAAO,IAAI,CAACpP,IAAI;EACpB;EAEA;;;;EAIAqP,QAAQA,CAACC,EAAU;IAEfF,QAAQ,CAACpO,IAAI,GAAGsO,EAAE;EACtB;EAEA;;;;EAIA1K,UAAUA,CAAA;IAEN,IAAI,CAAC,IAAI,CAACmK,QAAQ,EAAE;MAChB,IAAI,CAACA,QAAQ,GAAG,IAAIJ,gBAAA,CAAAvN,OAAc,EAAE;;IAExC,OAAO,IAAI,CAAC2N,QAAQ;EACxB;EAEA;;;;EAIAQ,kBAAkBA,CAAA;IAEd,OAAOC,eAAe;EAC1B;EAEA;;;EAGAC,IAAIA,CAACC,GAAW;IAEZ,OAAOrQ,MAAM,CAACoQ,IAAI,CAACC,GAAG,CAAC;EAC3B;EAEA;;;EAGAjL,IAAIA,CAACiL,GAAW;IAEZ,OAAOrQ,MAAM,CAACoF,IAAI,CAACiL,GAAG,CAAC;EAC3B;EAEAC,eAAeA,CAACC,KAA0B;IAEtC,IAAI,OAAOA,KAAK,IAAI,QAAQ,EAAE;MAC1B,OAAO,IAAAf,WAAA,CAAAgB,SAAS,EAACD,KAAK,CAAC;;IAE3B,OAAO,IAAAf,WAAA,CAAAiB,cAAc,EAACF,KAAK,EAAE,IAAI,CAAC;EACtC;EAEAG,eAAeA,CAACH,KAAa;IAEzB,OAAO,IAAAf,WAAA,CAAAmB,MAAM,EAACJ,KAAK,CAAC;EACxB;EAEA;;;;;;;EAOAK,WAAWA,CAAA;IAEP,OAAO;MACHC,KAAK,EAAMA,CAAC,GAAGC,IAAW,KAAK,IAAA1B,OAAA,CAAAyB,KAAK,EAAC,IAAI,EAAE,GAAGC,IAAI,CAAC;MACnDC,SAAS,EAAElO,OAAO,IAAI,IAAAuM,OAAA,CAAA2B,SAAS,EAAC,IAAI,EAAElO,OAAO,CAAC;MAC9CmO,IAAI,EAAOnO,OAAO,IAAI,IAAAuM,OAAA,CAAA4B,IAAI,EAAC,IAAI,EAAEnO,OAAO,CAAC;MACzCxC,MAAM,EAAMG,KAAsC,IAAK,IAAI6O,QAAA,CAAAtN,OAAM,CAAC,IAAI,EAAEvB,KAAK,CAAC;MAC9EqC,OAAO,EAAI,IAAI,CAACA,OAAO;MACvBoO,KAAK,EAAE;QACH1B;;KAEP;EACL;;AAxKJ5E,kBAAA,GAAA8E,cAAA;;;;;;;;;;;;;ACJA;AACA;AACA,MAAAyB,gBAAA,GAAAxR,mBAAA;AACA,MAAAG,YAAA,GAAAH,mBAAA;AAEA,MAAMyR,OAAO,GAAG,IAAID,gBAAA,CAAAnP,OAAc,EAAE;AACpC,MAAM;EAAE8O,KAAK;EAAEE,SAAS;EAAEC,IAAI;EAAE3Q,MAAM;EAAEwC,OAAO;EAAEoO;AAAK,CAAE,GAAGE,OAAO,CAACP,WAAW,EAAE;AAEhF;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC,EAAE;AAW3C;AACA,MAAMW,IAAI,GAAG;EACTpB,eAAe,EAAEnQ,MAAM,CAACmQ,eAAe;EAEvC9P,MAAM;EAEN;;;;EAIAuK,UAAU,EAAV/K,YAAA,CAAAkC,OAAU;EAEVkP,KAAK;EACLO,MAAM,EAAE;IACJC,QAAQ,EAAE5O,OAAO;IACjBgO,KAAK;IACLE,SAAS;IACTC;;CAEP;AAEDU,MAAA,CAAA/G,OAAA,GAAS4G,IAAI;AACb;;;;;;;;;;;;;ACrDA;;;;;;;;AAKA,MAAAI,WAAA,GAAAjS,mBAAA;AACA,MAAAE,UAAA,GAAAF,mBAAA;AAEA,MAAMO,KAAK,GAAGP,mBAAO,CAAC,kDAAO,CAAC;AAE9B;AACA;AACA,MAAM;EAAE0R;AAAK,CAAE,GAAG,KAAsC,GAAGpR,MAAM,GAAGN,CAAsB;AAC1F;AAEA,MAAMkS,MAAM,GAAO3R,KAAK,CAAC,MAAM,CAAC;AACb0K,aAAA,GAAAiH,MAAA;AAEnB;;;AAGA,MAAMtG,KAAK,GAAwB,EAAE;AAErC;;;AAGaX,aAAK,GAAG;EACjBkH,EAAEA,CAAC;IAAEC,IAAI;IAAEC;EAAK,CAAwB;IACpCC,eAAe,CAAC;MAAEF,IAAI;MAAEC;IAAK,CAAE,CAAC;IAChC,IAAID,IAAI,IAAI,IAAI,EAAO,OAAOC,KAAK;IACnC,IAAID,IAAI,IAAI,GAAG,EAAQ,OAAOC,KAAK,GAAK,GAAG;IAC3C,IAAID,IAAI,IAAI,IAAI,EAAO,OAAOC,KAAK,GAAI,IAAI;IAC3C,IAAID,IAAI,IAAI,SAAS,EAAE,OAAOC,KAAK,GAAI,IAAI;IAC3C,IAAID,IAAI,IAAI,QAAQ,EAAG,OAAOC,KAAK,GAAI,IAAI;IAC3C,IAAID,IAAI,IAAI,IAAI,EAAO,OAAOC,KAAK,GAAG,KAAK;IAC3C,IAAID,IAAI,IAAI,SAAS,EAAE,OAAOC,KAAK,GAAG,KAAK;IAC3C,MAAM,IAAIpP,KAAK,CAAC,4BAA4B,GAAGmP,IAAI,CAAC;EACxD,CAAC;EACDG,EAAEA,CAAC;IAAEH,IAAI;IAAEC;EAAK,CAAwB;IACpCC,eAAe,CAAC;MAAEF,IAAI;MAAEC;IAAK,CAAE,CAAC;IAChC,IAAID,IAAI,IAAI,IAAI,EAAM,OAAOC,KAAK;IAClC,IAAID,IAAI,IAAI,GAAG,EAAO,OAAOC,KAAK,GAAG,IAAI;IACzC,IAAID,IAAI,CAAC3P,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO4P,KAAK,GAAG,OAAO;IAC5C,IAAID,IAAI,CAAC3P,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO4P,KAAK,GAAG,MAAM;IAC3C,MAAM,IAAIpP,KAAK,CAAC,4BAA4B,GAAGmP,IAAI,CAAC;EACxD,CAAC;EACDI,GAAGA,CAACC,EAAwB;IACxBH,eAAe,CAACG,EAAE,CAAC;IACnB,OAAOA,EAAE,CAACJ,KAAK;EACnB;CACH;AAED;;;AAGA,SAASC,eAAeA,CAAC;EAAED,KAAK;EAAED;AAAI,CAAwB;EAC1D,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIpP,KAAK,CAAC,8BAA8B,GAAGoP,KAAK,GAAG,GAAG,GAAGD,IAAI,CAAC;;AAE5E;AAEA;;;AAGO,eAAeM,aAAaA,CAACC,IAAc;EAC9C,IAAI,CAACA,IAAI,CAACC,EAAE,EAAE;IACV,MAAMlL,KAAK,GAAG,IAAIuK,WAAA,CAAA5P,OAAS,CAACsQ,IAAI,CAAC;IACjC,MAAMjL,KAAK,CAACyH,KAAK,EAAE;IACnB,MAAMzH,KAAK;;EAEf,OAAOiL,IAAI;AACf;AAPA1H,qBAAA,GAAAyH,aAAA;AASA;;;;;AAKA,SAAgBG,cAAcA,CAACF,IAAc;EACzC,OAAOA,IAAI,CAACnD,IAAI,EAAE,CAACnI,IAAI,CAACmI,IAAI,IAAIA,IAAI,CAAC5G,MAAM,GAAGyC,IAAI,CAAC8D,KAAK,CAACK,IAAI,CAAC,GAAG,EAAE,CAAC;AACxE;AAFAvE,sBAAA,GAAA4H,cAAA;AAIA,SAAgBC,YAAYA,CAA4C/H,GAAM;EAE1E;EACA,IAAI,CAACA,GAAG,EAAE;IACN,OAAOA,GAAQ;;EAGnB;EACA,IAAIgB,KAAK,CAACD,OAAO,CAACf,GAAG,CAAC,EAAE;IACpB,OAAOA,GAAG,CAAC1C,GAAG,CAACwG,CAAC,IAAIA,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,GAAGiE,YAAY,CAACjE,CAAC,CAAC,GAAGA,CAAC,CAAiB;;EAGzF;EACA,IAAIhB,GAAG,GAAwB,EAAE;EACjChF,MAAM,CAAC2E,IAAI,CAACzC,GAAG,CAAC,CAACsC,OAAO,CAACvH,GAAG,IAAG;IAC3B,MAAMiN,QAAQ,GAAGjN,GAAG,CAACkN,WAAW,EAAE;IAClC,MAAMnE,CAAC,GAAI9D,GAA2B,CAACjF,GAAG,CAAC;IAC3C+H,GAAG,CAACkF,QAAQ,CAAC,GAAGlE,CAAC,IAAI,OAAOA,CAAC,IAAI,QAAQ,GAAGiE,YAAY,CAACjE,CAAC,CAAC,GAAGA,CAAC;EACnE,CAAC,CAAC;EACF,OAAOhB,GAAQ;AACnB;AApBA5C,oBAAA,GAAA6H,YAAA;AAsBA;;;;;;;;;;AAUA,SAAgBhQ,OAAOA,CACnBX,GAAqB,EACrBzB,cAAA,GAA0C,EAAE;EAG5C,MAAM;IAAE6G,eAAe;IAAE,GAAGpE;EAAO,CAAE,GAAGzC,cAAc;EACtD,OAAOgR,KAAK,CAACvP,GAAG,EAAE;IACdkI,IAAI,EAAE,MAAM;IACZ,GAAGlH,OAAO;IACV8D,OAAO,EAAE;MACLgM,MAAM,EAAE,kBAAkB;MAC1B,GAAGH,YAAY,CAAC3P,OAAO,CAAC8D,OAAO;;GAEtC,CAAC,CACDI,IAAI,CAACqL,aAAa,CAAC,CACnBrL,IAAI,CAAEmH,GAAa,IAAI;IACpB,MAAMa,IAAI,GAAGb,GAAG,CAACvH,OAAO,CAAClB,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE;IACjD,IAAIsJ,IAAI,CAAC5M,KAAK,CAAC,WAAW,CAAC,EAAE;MACzB,OAAOoQ,cAAc,CAACrE,GAAG,CAAC,CAACnH,IAAI,CAACG,IAAI,KAAK;QAAEgH,GAAG;QAAEhH;MAAI,CAAE,CAAC,CAAC;;IAE5D,IAAI6H,IAAI,CAAC5M,KAAK,CAAC,UAAU,CAAC,EAAE;MACxB,OAAO+L,GAAG,CAACgB,IAAI,EAAE,CAACnI,IAAI,CAACG,IAAI,KAAK;QAAEgH,GAAG;QAAEhH;MAAI,CAAE,CAAC,CAAC;;IAEnD,OAAO;MAAEgH;IAAG,CAAE;EAClB,CAAC,CAAC,CACDnH,IAAI,CAAC,CAAC;IAACmH,GAAG;IAAEhH;EAAI,CAAqD,KAAI;IAEtE;IACA;IACA;IACA,IAAI,CAACA,IAAI,IAAIgH,GAAG,CAAC7G,MAAM,IAAI,GAAG,EAAE;MAC5B,MAAM0I,QAAQ,GAAG7B,GAAG,CAACvH,OAAO,CAAClB,GAAG,CAAC,UAAU,CAAC;MAC5C,IAAIsK,QAAQ,EAAE;QACV,OAAOvN,OAAO,CAACuN,QAAQ,EAAE;UAAE,GAAGlN,OAAO;UAAEiH,MAAM,EAAE,KAAK;UAAE5C,IAAI,EAAE,IAAI;UAAED;QAAe,CAAE,CAAC;;;IAI5F,IAAIA,eAAe,EAAE;MACjB,OAAO;QAAEC,IAAI;QAAEL,QAAQ,EAAEqH;MAAG,CAAE;;IAGlC;IACA;IACA;IACA,IAAIhH,IAAI,KAAKX,SAAS,EAAE;MACpB,OAAO2H,GAAG;;IAGd;IACA,OAAOhH,IAAI;EACf,CAAC,CAAC;AACN;AAnDAyD,eAAA,GAAAnI,OAAA;AAqDA;;;;;;;;AAQA,SAAgBoQ,WAAWA,CAAC/Q,GAAW,EAAEzB,cAA4B,EAAEyS,KAAA,GAAiBC,aAAoB,KAAK,MAAM;EACnH,IAAID,KAAK,IAAI,CAACvH,KAAK,CAACzJ,GAAG,CAAC,EAAE;IACtByJ,KAAK,CAACzJ,GAAG,CAAC,GAAGW,OAAO,CAACX,GAAG,EAAEzB,cAAc,CAAC;IACzC,OAAOkL,KAAK,CAACzJ,GAAG,CAAC;;EAErB,OAAOY,OAAO,CAACiF,OAAO,CAAC4D,KAAK,CAACzJ,GAAG,CAAC,CAAC;AACtC;AANA8I,mBAAA,GAAAiI,WAAA;AAQA;;;;;;;AAOA,SAAgBxR,yBAAyBA,CAACoC,OAAO,GAAG,GAAG,EAAEpD,cAA4B;EAEjF,MAAMyB,GAAG,GAAGiE,MAAM,CAACtC,OAAO,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,UAAU;EAC7D,OAAOmP,WAAW,CAAC/Q,GAAG,EAAEzB,cAAc,CAAC,CAAC+G,KAAK,CAAEiF,EAAS,IAAI;IACxD,MAAM,IAAIzJ,KAAK,CACX,mDAAmDd,GAAG,MAAMuK,EAAE,EAAE,CACnE;EACL,CAAC,CAAC;AACN;AARAzB,iCAAA,GAAAvJ,yBAAA;AAWA;;;;;;;;;AASA,SAAgBoJ,OAAOA,CAACC,GAAwB,EAAEC,IAAI,GAAG,EAAE;EACvDA,IAAI,GAAGA,IAAI,CAAC8B,IAAI,EAAE;EAClB,IAAI,CAAC9B,IAAI,EAAE;IACP,OAAOD,GAAG;;EAGd,IAAIwI,QAAQ,GAAGvI,IAAI,CAAC5J,KAAK,CAAC,GAAG,CAAC;EAC9B,IAAIkG,MAAM,GAAGyD,GAAG;EAEhB,OAAOzD,MAAM,IAAIiM,QAAQ,CAAC3K,MAAM,EAAE;IAC9B,MAAM9C,GAAG,GAAGyN,QAAQ,CAACC,KAAK,EAAE;IAC5B,IAAI,CAAC1N,GAAG,IAAIiG,KAAK,CAACD,OAAO,CAACxE,MAAM,CAAC,EAAE;MAC/B,OAAOA,MAAM,CAACe,GAAG,CAACoL,CAAC,IAAI3I,OAAO,CAAC2I,CAAC,EAAEF,QAAQ,CAAChO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;KACzD,MAAM;MACH+B,MAAM,GAAGA,MAAM,CAACxB,GAAa,CAAC;;;EAItC,OAAOwB,MAAM;AACjB;AAnBA2D,eAAA,GAAAH,OAAA;AAqBA;;;;;;;;AAQA,SAAgB2B,OAAOA,CAAC1B,GAAwB,EAAEC,IAAY,EAAEqH,KAAU,EAAEqB,WAAW,GAAG,KAAK;EAC3F1I,IAAI,CAAC8B,IAAI,EAAE,CAAC1L,KAAK,CAAC,GAAG,CAAC,CAAC2L,MAAM,CACzB,CAACc,GAAG,EAAE/H,GAAG,EAAE6N,GAAG,EAAEC,GAAG,KAAI;IACnB,IAAI/F,GAAG,IAAI8F,GAAG,KAAKC,GAAG,CAAChL,MAAM,GAAG,CAAC,EAAE;MAC/BiF,GAAG,CAAC/H,GAAG,CAAC,GAAGuM,KAAK;KACnB,MACI;MACD,IAAIxE,GAAG,IAAIA,GAAG,CAAC/H,GAAG,CAAC,KAAKe,SAAS,IAAI6M,WAAW,EAAE;QAC9C7F,GAAG,CAAC/H,GAAG,CAAC,GAAG8N,GAAG,CAACD,GAAG,GAAG,CAAC,CAAC,CAAClR,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE;;MAEvD,OAAOoL,GAAG,GAAGA,GAAG,CAAC/H,GAAG,CAAC,GAAGe,SAAS;;EAEzC,CAAC,EACDkE,GAAG,CACN;EACD,OAAOA,GAAG;AACd;AAhBAE,eAAA,GAAAwB,OAAA;AAkBA;;;;;;AAMA,SAAgB/F,SAASA,CAAUmN,GAAQ;EACvC,IAAI9H,KAAK,CAACD,OAAO,CAAC+H,GAAG,CAAC,EAAE;IACpB,OAAOA,GAAG;;EAEd,OAAO,CAACA,GAAG,CAAC;AAChB;AALA5I,iBAAA,GAAAvE,SAAA;AAOA;;;;;;AAMA,SAAgB7F,QAAQA,CAACmK,IAAY,EAAElH,OAAgB;EAEnD,IAAIkH,IAAI,CAACvI,KAAK,CAAC,OAAO,CAAC,EAAE,OAAOuI,IAAI;EACpC,IAAIA,IAAI,CAACvI,KAAK,CAAC,MAAM,CAAC,EAAE,OAAOuI,IAAI;EACnC,OAAO5E,MAAM,CAACtC,OAAO,IAAI,EAAE,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,GAAGiH,IAAI,CAACjH,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;AACrF;AALAkH,gBAAA,GAAApK,QAAA;AAOA;;;;;;;;AAQA,SAAgBiT,YAAYA,CACxBC,SAAS,GAAG,CAAC,EACbC,OAAO,GAAG,gEAAgE;EAG1E,MAAM1M,MAAM,GAAG,EAAE;EACjB,MAAMgG,GAAG,GAAG0G,OAAO,CAACpL,MAAM;EAC1B,OAAOmL,SAAS,EAAE,EAAE;IAChBzM,MAAM,CAAC6F,IAAI,CAAC6G,OAAO,CAACC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG9G,GAAG,CAAC,CAAC,CAAC;;EAEhE,OAAOhG,MAAM,CAAC/B,IAAI,CAAC,EAAE,CAAC;AAC1B;AAXA0F,oBAAA,GAAA6I,YAAA;AAaA;;;;;;AAMA,SAAgBzO,SAASA,CAAClB,KAAa,EAAEkP,GAAuB;EAE5D,MAAMgB,OAAO,GAAGlQ,KAAK,CAAC/C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACnC,OAAOiT,OAAO,GAAGhJ,IAAI,CAAC8D,KAAK,CAACkE,GAAG,CAAC3C,IAAI,CAAC2D,OAAO,CAAC,CAAC,GAAG,IAAI;AACzD;AAJApJ,iBAAA,GAAA5F,SAAA;AAMA;;;;;;AAMA,SAAgBiP,eAAeA,CAACC,YAAA,GAAuB,GAAG,EAAEC,IAAoB;EAC5E,OAAON,IAAI,CAACC,KAAK,CAAC,EAAEK,IAAI,IAAI,IAAIrL,IAAI,EAAE,CAAC,GAAG,IAAI,GAAGoL,YAAY,CAAC;AAClE;AAFAtJ,uBAAA,GAAAqJ,eAAA;AAIA;;;;;;;AAOA,SAAgB9J,wBAAwBA,CAAC/F,aAAuC,EAAE4O,GAAuB;EAErG,MAAMjK,GAAG,GAAG8K,IAAI,CAACC,KAAK,CAAChL,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;EAEzC;EACA,IAAI3E,aAAa,CAACgQ,UAAU,EAAE;IAC1B,OAAOrL,GAAG,GAAG3E,aAAa,CAACgQ,UAAU;;EAGzC;EACA,IAAIhQ,aAAa,CAAC8F,YAAY,EAAE;IAC5B,IAAImK,SAAS,GAAGrP,SAAS,CAACZ,aAAa,CAAC8F,YAAY,EAAE8I,GAAG,CAAC;IAC1D,IAAIqB,SAAS,IAAIA,SAAS,CAACC,GAAG,EAAE;MAC5B,OAAOD,SAAS,CAACC,GAAG;;;EAI5B;EACA,OAAOvL,GAAG,GAAG,GAAG;AACpB;AAnBA6B,gCAAA,GAAAT,wBAAA;AAqBA;;;;;;;;;;;;AAYA,SAAgBE,MAAMA,CAClBC,YAAyE,EACzEC,QAAgB;EAGhB,MAAMgK,GAAG,GAA8B,EAAE;EAEzC,SAASC,qBAAqBA,CAACC,OAAwC,EAAEC,WAAwC;IAC7G,IAAID,OAAO,IAAI/I,KAAK,CAACD,OAAO,CAACgJ,OAAO,CAACE,MAAM,CAAC,EAAE;MAC1CF,OAAO,CAACE,MAAM,CAAC3H,OAAO,CAAC,CAAC;QAAE+E;MAAI,CAAE,KAAI;QAChC,IAAIA,IAAI,EAAE;UACNwC,GAAG,CAACxC,IAAI,CAAC,GAAGwC,GAAG,CAACxC,IAAI,CAAC,IAAI,EAAmC;UAC5DwC,GAAG,CAACxC,IAAI,CAAC,CAACjF,IAAI,CAAC4H,WAAW,CAAC;;MAEnC,CAAC,CAAC;;EAEV;EAEArO,SAAS,CAACiE,YAAY,CAAC,CAAC0C,OAAO,CAACoG,CAAC,IAAG;IAChC,IAAIA,CAAC,CAACvS,YAAY,KAAK,aAAa,IAAIuS,CAAC,CAAC7I,QAAQ,CAAC,EAAE;MACjD,IAAImB,KAAK,CAACD,OAAO,CAAC2H,CAAC,CAAC7I,QAAQ,CAAC,CAAC,EAAE;QAC5B6I,CAAC,CAAC7I,QAAQ,CAAC,CAACyC,OAAO,CAAEyH,OAAwC,IAAKD,qBAAqB,CAACC,OAAO,EAAErB,CAAC,CAAC,CAAC;OACvG,MAAM;QACHoB,qBAAqB,CAACpB,CAAC,CAAC7I,QAAQ,CAAC,EAAE6I,CAAC,CAAC;;;EAGjD,CAAC,CAAC;EAEF,OAAOmB,GAAG;AACd;AA7BA3J,cAAA,GAAAP,MAAA;AA+BA;;;;;;;;;;;;;AAaA,SAAgBG,OAAOA,CACnBF,YAAyE,EACzEC,QAAgB;EAGhB,MAAMqK,IAAI,GAAGvK,MAAM,CAACC,YAAY,EAAEC,QAAQ,CAAC;EAC3C,OAAO,CAAC,GAAGsK,KAAK,KAAKA,KAAK,CACrBjJ,MAAM,CAACmG,IAAI,IAAKA,IAAI,GAAG,EAAE,IAAK6C,IAAI,CAAC,CACnClI,MAAM,CACH,CAACC,IAAI,EAAEoF,IAAI,KAAKpF,IAAI,CAAChE,MAAM,CAACiM,IAAI,CAAC7C,IAAI,GAAG,EAAE,CAAC,CAAC,EAC5C,EAAmC,CACtC;AACT;AAZAnH,eAAA,GAAAJ,OAAA;AAcA;;;;AAIA,SAAgBjJ,eAAeA,CAACH,WAAgD,EAAEP,YAAoB;EAElG;EACA,MAAM4M,SAAS,GAAGhD,OAAO,CAACrJ,WAAW,EAAE,iBAAiB,CAAC,IAAI,EAAE;EAE/D;EACA,MAAM0T,IAAI,GAAGrH,SAAS,CAACtF,IAAI,CAAE4M,CAAM,IAAKA,CAAC,CAAC/F,IAAI,KAAKnO,YAAY,CAAC;EAChE,IAAI,CAACiU,IAAI,EAAE;IACP,MAAM,IAAIlS,KAAK,CAAC,aAAa/B,YAAY,wCAAwC,CAAC;;EAGtF;EACA,IAAI,CAAC6K,KAAK,CAACD,OAAO,CAACqJ,IAAI,CAACxT,WAAW,CAAC,EAAE;IAClC,MAAM,IAAIsB,KAAK,CAAC,uCAAuC/B,YAAY,uBAAuB,CAAC;;EAG/F;EACA,IAAIA,YAAY,IAAI,SAAS,IAAIiU,IAAI,CAACxT,WAAW,CAAC6G,IAAI,CAAE6M,CAAM,IAAKA,CAAC,CAACpG,IAAI,IAAI,KAAK,CAAC,EAAE;IACjF,OAAO,KAAK;;EAGhB;EACA,MAAMpB,GAAG,GAAG3N,UAAA,CAAAoV,aAAa,CAAC9M,IAAI,CAAC+M,CAAC,IAAIJ,IAAI,CAACxT,WAAW,CAAC6G,IAAI,CAAE6M,CAAM,IAAKA,CAAC,CAACpG,IAAI,IAAIsG,CAAC,CAAC,CAAC;EAEnF;EACA,IAAI,CAAC1H,GAAG,EAAE;IACN,MAAM,IAAI5K,KAAK,CAAC,qCAAqC,GAAG/B,YAAY,CAAC;;EAGzE,OAAO2M,GAAG;AACd;AA9BA5C,uBAAA,GAAArJ,eAAA;AAgCA;;;;;;;AAOO,eAAe4T,eAAeA,CAACC,MAA+B,EAAEC,KAAA,GAAgB,GAAG,EAAEC,MAAA,GAAiB,GAAG;EAE5G;EACA;EACA;EACA,IAAI,OAAOF,MAAM,IAAI,UAAU,EAAE;IAC7BA,MAAM,GAAG,MAAMA,MAAM,EAAE;;EAG3B;EACA,IAAIA,MAAM,IAAI,OAAOA,MAAM,IAAI,QAAQ,EAAE;IACrC,OAAOA,MAAM;;EAGjB;EACA,IAAI,OAAOA,MAAM,IAAI,QAAQ,EAAE;IAC3BvD,MAAM,CAAC,oDAAoD,EAAE,OAAOuD,MAAM,CAAC;IAC3E,OAAOG,IAAI;;EAGf;EACA,IAAIH,MAAM,IAAI,OAAO,EAAE;IACnB,OAAOG,IAAI;;EAGf;EACA,IAAIH,MAAM,IAAI,SAAS,EAAE;IACrB,OAAOI,MAAM;;EAGjB;EACA,IAAIJ,MAAM,IAAI,MAAM,EAAE;IAClB,OAAOK,GAAG,IAAIF,IAAI;;EAGtB;EACA,IAAIH,MAAM,IAAI,QAAQ,EAAE;IACpB,IAAI/N,KAAK;MAAEqO,YAAY,GAAkB,IAAI;IAC7C,IAAI;MACAA,YAAY,GAAGzV,MAAM,CAAC0V,IAAI,CAAC,EAAE,EAAE,gBAAgB,CAAC;MAChD,IAAI,CAACD,YAAY,EAAE;QACf,MAAM,IAAI9S,KAAK,CAAC,iCAAiC,CAAC;;KAEzD,CAAC,OAAOgT,CAAC,EAAE;MACRvO,KAAK,GAAGuO,CAAC;;IAGb,IAAI,CAACF,YAAY,EAAE;MACf7D,MAAM,CAAC,iDAAiD,EAAExK,KAAK,CAAC;MAChE,OAAOkO,IAAI;KACd,MAAM;MACH,OAAOG,YAAY;;;EAI3B;EACA,IAAIN,MAAM,IAAI,OAAO,EAAE;IACnB,IAAI/N,KAAK;MAAEqO,YAAY,GAAkB,IAAI;IAC7C;IACA,IAAI;MACAA,YAAY,GAAGzV,MAAM,CAAC0V,IAAI,CAAC,EAAE,EAAE,gBAAgB,EAAE,CAC7C,SAAS,GAAGL,MAAM,EAClB,QAAQ,GAAGD,KAAK,EAChB,WAAW,EACX,aAAa,EACb,UAAU,EACV,MAAM,GAAG,CAACQ,MAAM,CAACP,MAAM,GAAGA,MAAM,IAAI,CAAC,EACrC,OAAO,GAAG,CAACO,MAAM,CAACR,KAAK,GAAGA,KAAK,IAAI,CAAC,CACvC,CAACnQ,IAAI,CAAC,GAAG,CAAC,CAAC;MACZ,IAAI,CAACwQ,YAAY,EAAE;QACf,MAAM,IAAI9S,KAAK,CAAC,sCAAsC,CAAC;;KAE9D,CAAC,OAAOgT,CAAC,EAAE;MACRvO,KAAK,GAAGuO,CAAC;;IAGb,IAAI,CAACF,YAAY,EAAE;MACf7D,MAAM,CAAC,iDAAiD,EAAExK,KAAK,CAAC;MAChE,OAAOkO,IAAI;KACd,MAAM;MACH,OAAOG,YAAY;;;EAI3B;EACA,MAAMI,UAAU,GAAWC,MAAM,CAACX,MAAa,CAAC;EAChD,IAAIU,UAAU,EAAE;IACZ,OAAOA,UAAU;;EAGrBjE,MAAM,CAAC,+CAA+C,EAAEuD,MAAM,CAAC;EAC/D,OAAOG,IAAI;AACf;AA5FA3K,uBAAA,GAAAuK,eAAA;AA8FA,SAAgBlU,MAAMA,CAAC+U,SAAc,EAAEzO,OAAe;EAClD,IAAI,CAAEyO,SAAU,EAAE;IACd,MAAM,IAAIpT,KAAK,CAAC2E,OAAO,CAAC;;AAEhC;AAJAqD,cAAA,GAAA3J,MAAA;AAMA,SAAgBoK,eAAeA,CAACD,KAA2B;EACvDnK,MAAM,CAACyK,KAAK,CAACD,OAAO,CAACL,KAAK,CAAC,EAAE,iCAAiC,CAAC;EAC/DnK,MAAM,CAACmK,KAAK,CAAC7C,MAAM,GAAG,CAAC,EAAE,0CAA0C,CAAC;EACpE6C,KAAK,CAAC4B,OAAO,CAAEiJ,SAAwC,IAAI;IACvDhV,MAAM,CACF,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAACE,OAAO,CAAC8U,SAAS,CAACC,EAAE,CAAC,GAAG,CAAC,CAAC,EAC/E,0HAA0H,CAC7H;IACDjV,MAAM,CAACgV,SAAS,CAACtL,IAAI,IAAI,OAAOsL,SAAS,CAACtL,IAAI,EAAE,YAAYsL,SAAS,CAACC,EAAE,sCAAsC,CAAC;IAE/G,IAAID,SAAS,CAACC,EAAE,IAAI,KAAK,IAAID,SAAS,CAACC,EAAE,IAAI,SAAS,IAAID,SAAS,CAACC,EAAE,IAAI,MAAM,EAAE;MAC9EjV,MAAM,CAAC,OAAO,IAAIgV,SAAS,EAAE,YAAYA,SAAS,CAACC,EAAE,uCAAuC,CAAC;MAC7FjV,MAAM,CAACuH,MAAM,CAAC2E,IAAI,CAAC8I,SAAS,CAAC,CAAC1N,MAAM,IAAI,CAAC,EAAE,YAAY0N,SAAS,CAACC,EAAE,0CAA0C,CAAC;KACjH,MAEI,IAAID,SAAS,CAACC,EAAE,IAAI,MAAM,IAAID,SAAS,CAACC,EAAE,IAAI,MAAM,EAAE;MACvDjV,MAAM,CAAC,OAAOgV,SAAS,CAAC9B,IAAI,IAAI,QAAQ,EAAE,YAAY8B,SAAS,CAACC,EAAE,gDAAgD,CAAC;MACnHjV,MAAM,CAACuH,MAAM,CAAC2E,IAAI,CAAC8I,SAAS,CAAC,CAAC1N,MAAM,IAAI,CAAC,EAAE,YAAY0N,SAAS,CAACC,EAAE,0CAA0C,CAAC;KACjH,MAEI;MACDjV,MAAM,CAACuH,MAAM,CAAC2E,IAAI,CAAC8I,SAAS,CAAC,CAAC1N,MAAM,IAAI,CAAC,EAAE,YAAY0N,SAAS,CAACC,EAAE,0CAA0C,CAAC;;EAEtH,CAAC,CAAC;AACN;AAxBAtL,uBAAA,GAAAS,eAAA;;;;;;;;;;;;;;;;;ACvjBA,MAAAoE,WAAA,GAAA9P,mBAAA;AAIA,MAAMwW,MAAM,GAAW,OAAOC,UAAU,KAAK,QAAQ,IAAIA,UAAU,CAACD,MAAM,GACtEC,UAAU,CAACD,MAAM,GACjBxW,mHAAuC;AAE3C,MAAM0W,MAAM,GAAGA,CAAA,KAAK;EAChB,IAAI,CAACF,MAAM,CAACE,MAAM,EAAE;IAChB,IAAI,CAACD,UAAU,CAACE,eAAe,EAAE;MAC7B,MAAM,IAAI1T,KAAK,CACX,0DAA0D,GAC1D,6DAA6D,GAC7D,iCAAiC,GACjC,uEAAuE,CAC1E;;IAEL,MAAM,IAAIA,KAAK,CACX,0DAA0D,GAC1D,yDAAyD,CAC5D;;EAEL,OAAOuT,MAAM,CAACE,MAAM;AACxB,CAAC;AAQD,MAAME,IAAI,GAAG;EACTC,KAAK,EAAE;IACH5H,IAAI,EAAE,OAAO;IACb6H,UAAU,EAAE;GACG;EACnBC,KAAK,EAAE;IACH9H,IAAI,EAAE,mBAAmB;IACzB+H,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE,IAAIC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzCC,IAAI,EAAE;MACFlI,IAAI,EAAE;;;CAGjB;AAED,SAAgBmI,WAAWA,CAACpJ,KAAa;EACrC,OAAOwI,MAAM,CAACa,eAAe,CAAC,IAAIH,UAAU,CAAClJ,KAAK,CAAC,CAAC;AACxD;AAFA/C,mBAAA,GAAAmM,WAAA;AAIO,eAAeE,YAAYA,CAACjD,OAAe;EAC9C,MAAMkD,QAAQ,GAAG,IAAIC,WAAW,EAAE,CAACC,MAAM,CAACpD,OAAO,CAAC;EAClD,MAAM8C,IAAI,GAAG,MAAMT,MAAM,EAAE,CAACgB,MAAM,CAAC,SAAS,EAAEH,QAAQ,CAAC;EACvD,OAAO,IAAIL,UAAU,CAACC,IAAI,CAAC;AAC/B;AAJAlM,oBAAA,GAAAqM,YAAA;AAMO,MAAMK,qBAAqB,GAAG,MAAAA,CAAOC,OAAO,GAAG,EAAE,KAAuB;EAC3E,MAAMC,UAAU,GAAMT,WAAW,CAACQ,OAAO,CAAC;EAC1C,MAAME,YAAY,GAAI,IAAAhI,WAAA,CAAAiB,cAAc,EAAC8G,UAAU,EAAE,IAAI,CAAC;EACtD,MAAME,aAAa,GAAG,IAAAjI,WAAA,CAAAiB,cAAc,EAAC,MAAMuG,YAAY,CAACQ,YAAY,CAAC,EAAE,IAAI,CAAC;EAC5E,OAAO;IAAEC,aAAa;IAAED;EAAY,CAAE;AAC1C,CAAC;AALY7M,6BAAqB,GAAA0M,qBAAA;AAO3B,eAAeK,SAASA,CAACC,GAAmB;EAC/C;EACA,IAAI,CAACA,GAAG,CAACC,GAAG,EAAE;IACV,MAAM,IAAIjV,KAAK,CAAC,iEAAiE,CAAC;;EAGtF;EACA;EACA;EACA;EACA,IAAI,CAAC8I,KAAK,CAACD,OAAO,CAACmM,GAAG,CAACE,OAAO,CAAC,EAAE;IAC7BF,GAAG,CAACE,OAAO,GAAG,CAAC,MAAM,CAAC;;EAG1B;EACA,IAAI,CAACF,GAAG,CAACE,OAAO,CAACjL,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC/B,MAAM,IAAIjK,KAAK,CAAC,2DAA2D,CAAC;;EAGhF,IAAI;IACA,OAAO,MAAMyT,MAAM,EAAE,CAAC0B,SAAS,CAC3B,KAAK,EACLH,GAAG,EACHrB,IAAI,CAACqB,GAAG,CAACC,GAAG,CAAC,EACbD,GAAG,CAACI,GAAG,KAAK,IAAI,EAChBJ,GAAG,CAACE,OAAO;KACd;GACJ,CAAC,OAAOlC,CAAC,EAAE;IACR,MAAM,IAAIhT,KAAK,CAAC,OAAOgV,GAAG,CAACC,GAAG,sCAAsCjC,CAAC,EAAE,CAAC;;AAEhF;AA9BAhL,iBAAA,GAAA+M,SAAA;AAgCO,eAAeM,cAAcA,CAACJ,GAAsB,EAAEK,UAAqB,EAAEC,MAAW,EAAEnE,OAAY;EAEzG,MAAMoE,SAAS,GAAIpN,IAAI,CAACC,SAAS,CAAC;IAAE,GAAGkN,MAAM;IAAEN;EAAG,CAAE,CAAC;EACrD,MAAMQ,UAAU,GAAGrN,IAAI,CAACC,SAAS,CAAC+I,OAAO,CAAC;EAC1C,MAAMsE,uBAAuB,GAAG,GAAG,IAAA7I,WAAA,CAAAgB,SAAS,EAAC2H,SAAS,CAAC,IAAI,IAAA3I,WAAA,CAAAgB,SAAS,EAAC4H,UAAU,CAAC,EAAE;EAElF,MAAME,SAAS,GAAG,MAAMlC,MAAM,EAAE,CAACmC,IAAI,CACjC;IAAE,GAAGN,UAAU,CAACO,SAAS;IAAE3B,IAAI,EAAE;EAAS,CAAE,EAC5CoB,UAAU,EACV,IAAIf,WAAW,EAAE,CAACC,MAAM,CAACkB,uBAAuB,CAAC,CACpD;EAED,OAAO,GAAGA,uBAAuB,IAAI,IAAA7I,WAAA,CAAAiB,cAAc,EAAC,IAAImG,UAAU,CAAC0B,SAAS,CAAC,EAAE,IAAI,CAAC,EAAE;AAC1F;AAbA3N,sBAAA,GAAAqN,cAAA;;;;;;;;;;;;;;;;;AChGA;;;AAGarN,0BAAkB,GAAG,CAC9B,SAAS,EACT,cAAc,EACd,oBAAoB,EACpB,aAAa,EACb,qBAAqB,EACrB,YAAY,EACZ,OAAO,EACP,UAAU,EACV,eAAe,EACf,UAAU,EACV,UAAU,EACV,YAAY,EACZ,OAAO,EACP,eAAe,EACf,oBAAoB,EACpB,eAAe,EACf,sBAAsB,EACtB,aAAa,EACb,WAAW,EACX,SAAS,EACT,UAAU,EACV,4BAA4B,EAC5B,6BAA6B,EAC7B,eAAe,EACf,eAAe,EACf,kBAAkB,EAClB,oBAAoB,EACpB,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,EACnB,oBAAoB,EACpB,WAAW,EACX,mBAAmB,EACnB,eAAe,EACf,sBAAsB,EACtB,qBAAqB,EACrB,MAAM,EACN,MAAM,EACN,OAAO,EACP,iBAAiB,EACjB,wBAAwB,EACxB,cAAc,EACd,cAAc,EACd,wBAAwB,EACxB,4BAA4B,EAC5B,SAAS,EACT,MAAM,EACN,eAAe,EACf,OAAO,EACP,0BAA0B,EAC1B,oBAAoB,EACpB,iBAAiB,EACjB,mBAAmB,EACnB,qBAAqB,EACrB,mBAAmB,EACnB,gBAAgB,EAChB,aAAa,EACb,OAAO,EACP,SAAS,EACT,QAAQ,EACR,WAAW,EACX,kBAAkB,EAClB,YAAY,EACZ,uBAAuB,EACvB,iBAAiB,EACjB,eAAe,EACf,cAAc,EACd,iBAAiB,EACjB,gBAAgB,EAChB,UAAU,EACV,gBAAgB,EAChB,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,oBAAoB,CACvB;AAED;;;AAGaA,oBAAY,GAAG;EACxB,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE,CAAC;EACV,OAAO,EAAE;CACZ;AAED;;;;AAIaA,qBAAa,GAAG,CACzB,SAAS,EACT,SAAS,EACT,WAAW,EACX,QAAQ,EACR,OAAO,EACP,aAAa,CAChB;AAED;;;AAGaA,iBAAS,GAAG,WAAW;;;;;;;;;;;;;;;;;ACvHpC;AACA,MAAAlL,KAAA,GAAAC,mBAAA;AAYA,MAAA2P,QAAA,GAAA3P,mBAAA;AACA,MAAAE,UAAA,GAAAF,mBAAA;AAKsB6I,uCAAA;EAAAmQ,UAAA;EAAAjT,GAAA,WAAAA,CAAA;IAAA,OALb7F,UAAA,CAAA8F,SAAS;EAAA;AAAA;AAGlB,MAAMzF,KAAK,GAAGR,KAAA,CAAAQ,KAAM,CAACC,MAAM,CAAC,QAAQ,CAAC;AAIrC,SAASyY,SAASA,CAAA;EACd,OAAO,OAAO3Y,MAAM,KAAK,QAAQ;AACrC;AAEA;;;;;;AAMA,SAAgB4Y,kBAAkBA,CAACpV,OAAO,GAAG,GAAG,EAAEpD,cAA4B;EAE1E,MAAMyB,GAAG,GAAGiE,MAAM,CAACtC,OAAO,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,iCAAiC;EACpF,OAAO,IAAAhE,KAAA,CAAAmT,WAAW,EAAC/Q,GAAG,EAAEzB,cAAc,CAAC,CAAC+G,KAAK,CAAEiF,EAAS,IAAI;IACxD,MAAM,IAAIzJ,KAAK,CAAC,wCAAwCd,GAAG,MAAMuK,EAAE,CAAC9E,OAAO,EAAE,CAAC;EAClF,CAAC,CAAC;AACN;AANAqD,0BAAA,GAAAiO,kBAAA;AAQA;;;AAGA,SAASC,sCAAsCA,CAACrV,OAAO,GAAG,GAAG,EAAEpD,cAA4B;EAEvF,OAAOwY,kBAAkB,CAACpV,OAAO,EAAEpD,cAAc,CAAC,CAAC2G,IAAI,CAAC8N,IAAI,IAAG;IAC3D,IAAI,CAACA,IAAI,CAACiE,sBAAsB,IAAI,CAACjE,IAAI,CAACkE,cAAc,EAAE;MACtD,MAAM,IAAIpW,KAAK,CAAC,uBAAuB,CAAC;;IAE5C,OAAO;MACHqW,eAAe,EAAOnE,IAAI,CAACoE,qBAAqB,IAAK,EAAE;MACvD3U,YAAY,EAAUuQ,IAAI,CAACiE,sBAAsB;MACjD3P,QAAQ,EAAc0L,IAAI,CAACkE,cAAc;MACzCG,oBAAoB,EAAErE,IAAI,CAACsE,gCAAgC,IAAI;KAClE;EACL,CAAC,CAAC;AACN;AAEA;;;AAGA,SAASC,6CAA6CA,CAAC5V,OAAO,GAAG,GAAG,EAAEpD,cAA4B;EAE9F,OAAO,IAAAX,KAAA,CAAA2B,yBAAyB,EAACoC,OAAO,EAAEpD,cAAc,CAAC,CAAC2G,IAAI,CAAC8N,IAAI,IAAG;IAClE,MAAMwE,KAAK,GAAG,uEAAuE;IACrF,MAAMC,UAAU,GAAI,CAAC,IAAA7Z,KAAA,CAAA+K,OAAO,EAACqK,IAAI,IAAI,EAAE,EAAE,2BAA2B,CAAC,IAAI,EAAE,EACtElJ,MAAM,CAACgK,CAAC,IAAIA,CAAC,CAAC9T,GAAG,KAAKwX,KAAK,CAAC,CAC5BtR,GAAG,CAACoL,CAAC,IAAIA,CAAC,CAACoG,SAAS,CAAC,CAAC,CAAC,CAAC;IAE7B,MAAMhM,GAAG,GAAsC;MAC3CyL,eAAe,EAAO,EAAE;MACxB1U,YAAY,EAAU,EAAE;MACxB6E,QAAQ,EAAc,EAAE;MACxB+P,oBAAoB,EAAE;KACzB;IAED,IAAII,UAAU,EAAE;MACZA,UAAU,CAACvM,OAAO,CAACgL,GAAG,IAAG;QACrB,IAAIA,GAAG,CAAClW,GAAG,KAAK,UAAU,EAAE;UACxB0L,GAAG,CAACyL,eAAe,GAAGjB,GAAG,CAACyB,QAAQ;;QAEtC,IAAIzB,GAAG,CAAClW,GAAG,KAAK,WAAW,EAAE;UACzB0L,GAAG,CAACjJ,YAAY,GAAGyT,GAAG,CAACyB,QAAQ;;QAEnC,IAAIzB,GAAG,CAAClW,GAAG,KAAK,OAAO,EAAE;UACrB0L,GAAG,CAACpE,QAAQ,GAAG4O,GAAG,CAACyB,QAAQ;;MAEnC,CAAC,CAAC;;IAGN,OAAOjM,GAAG;EACd,CAAC,CAAC;AACN;AAGA;;;;;;;AAOA,SAAgBkM,qBAAqBA,CAACjW,OAAO,GAAG,GAAG;EAE/C,OAAOqV,sCAAsC,CAACrV,OAAO,CAAC,CACjD2D,KAAK,CAAC,MAAMiS,6CAA6C,CAAC5V,OAAO,CAAC,CAAC;AAC5E;AAJAmH,6BAAA,GAAA8O,qBAAA;AAMA;;;;;;;;;;AAUO,eAAe1I,SAASA,CAC3BgC,GAAuB,EACvB2G,MAAA,GAAoE,EAAE;EAGtE,MAAM7X,GAAG,GAAGkR,GAAG,CAACjD,MAAM,EAAE;EAExB;EACA,IAAIrE,KAAK,CAACD,OAAO,CAACkO,MAAM,CAAC,EAAE;IACvB,MAAMC,MAAM,GAAG9X,GAAG,CAACN,YAAY,CAACkE,GAAG,CAAC,KAAK,CAAC,IAAI5D,GAAG,CAACN,YAAY,CAACkE,GAAG,CAAC,gBAAgB,CAAC;IACpF,IAAI,CAACkU,MAAM,EAAE;MACT,MAAM,IAAIhX,KAAK,CACX,6DAA6D,GAC7D,8BAA8B,CACjC;;IAEL;IACA,MAAMiX,GAAG,GAAGF,MAAM,CAACxR,IAAI,CAAC6M,CAAC,IAAG;MACxB,IAAIA,CAAC,CAAC8E,QAAQ,EAAE;QACZ,IAAI,OAAO9E,CAAC,CAAC8E,QAAQ,KAAK,UAAU,EAAE;UAClC,OAAO,CAAC,CAAC9E,CAAC,CAAC8E,QAAQ,CAACF,MAAM,CAAC;;QAE/B,IAAI,OAAO5E,CAAC,CAAC8E,QAAQ,KAAK,QAAQ,EAAE;UAChC,OAAO9E,CAAC,CAAC8E,QAAQ,KAAKF,MAAM;;QAEhC,IAAI5E,CAAC,CAAC8E,QAAQ,YAAYC,MAAM,EAAE;UAC9B,OAAO/E,CAAC,CAAC8E,QAAQ,CAACE,IAAI,CAACJ,MAAM,CAAC;;;MAGtC,OAAO,KAAK;IAChB,CAAC,CAAC;IACF,IAAAla,KAAA,CAAAuB,MAAM,EAAC4Y,GAAG,EAAE,gEAAgED,MAAM,GAAG,CAAC;IACtF,OAAO,MAAM5I,SAAS,CAACgC,GAAG,EAAE6G,GAAG,CAAC;;EAEpC;EAEA;EACA,MAAM;IACF5P,YAAY;IACZgQ,iBAAiB;IACjBC,WAAW;IACX9E,MAAM;IACNC,KAAK;IACLC,MAAM;IACN6E,QAAQ;IACRC,qBAAqB;IACrB;IACAC,YAAY;IACZC;EAAS,CACZ,GAAGX,MAAM;EAEV,IAAI;IACAY,GAAG;IACHC,MAAM;IACNrW,SAAS;IACTsW,cAAc;IACdC,WAAW;IACXC,UAAU;IACVtW,KAAK,GAAG,EAAE;IACVsF,QAAQ;IACRiR,gBAAgB;IAChBC,gBAAgB;IAChBC;EAAQ,CACX,GAAGnB,MAAM;EAEV,MAAMpU,OAAO,GAAGyN,GAAG,CAACxN,UAAU,EAAE;EAEhC;EACA+U,GAAG,GAAczY,GAAG,CAACN,YAAY,CAACkE,GAAG,CAAC,KAAK,CAAC,IAAe6U,GAAG;EAC9DE,cAAc,GAAG3Y,GAAG,CAACN,YAAY,CAACkE,GAAG,CAAC,gBAAgB,CAAC,IAAI+U,cAAc;EACzED,MAAM,GAAW1Y,GAAG,CAACN,YAAY,CAACkE,GAAG,CAAC,QAAQ,CAAC,IAAY8U,MAAM;EACjErW,SAAS,GAAQrC,GAAG,CAACN,YAAY,CAACkE,GAAG,CAAC,WAAW,CAAC,IAASvB,SAAS;EACpEwF,QAAQ,GAAS7H,GAAG,CAACN,YAAY,CAACkE,GAAG,CAAC,UAAU,CAAC,IAAUiE,QAAQ;EAEnE;EACA,IAAI,CAACA,QAAQ,EAAE;IACXA,QAAQ,GAAG2Q,SAAS;;EAExB,IAAI,CAACI,WAAW,EAAE;IACdA,WAAW,GAAGL,YAAY;;EAG9B,IAAI,CAACK,WAAW,EAAE;IACdA,WAAW,GAAG1H,GAAG,CAAClD,QAAQ,CAAC,GAAG,CAAC;GAClC,MAAM,IAAI,CAAC4K,WAAW,CAACtY,KAAK,CAAC,eAAe,CAAC,EAAE;IAC5CsY,WAAW,GAAG1H,GAAG,CAAClD,QAAQ,CAAC4K,WAAW,CAAC;;EAG3C,MAAMha,SAAS,GAAGqF,MAAM,CAACwU,GAAG,IAAIE,cAAc,IAAI,EAAE,CAAC;EAErD;EACA,IAAI,CAAC/Z,SAAS,EAAE;IACZ,MAAM,IAAIkC,KAAK,CACX,2DAA2D,GAC3D,4BAA4B,CAC/B;;EAGL,IAAI2X,GAAG,EAAE;IACLra,KAAK,CAAC,qBAAqB,EAAEsa,MAAM,GAAG,KAAK,GAAG,YAAY,CAAC;;EAG/D;EACA,IAAIA,MAAM,IAAI,CAACnW,KAAK,CAACjC,KAAK,CAAC,QAAQ,CAAC,EAAE;IAClCiC,KAAK,IAAI,SAAS;;EAGtB,IAAIuU,SAAS,EAAE,EAAE;IACb,MAAMmC,OAAO,GAAGC,SAAS,EAAE;IAC3B,MAAMC,OAAO,GAAGC,SAAS,EAAE;IAE3B,IAAI,CAACH,OAAO,IAAIE,OAAO,KAAKL,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,EAAE;MAEjF;MACA;MACA;MACA;MACAA,gBAAgB,GAAGG,OAAO;MAE1B;MACA;MACAzO,OAAO,CAACC,IAAI,CACR,8DAA8D,GAC9D,8DAA8D,GAC9D,0DAA0D,GAC1D,8DAA8D,GAC9D,4EAA4E,CAC/E;;;EAIT;EACA;EACA,MAAM4O,MAAM,GAAG,MAAM5V,OAAO,CAACG,GAAG,CAAC7F,UAAA,CAAA8F,SAAS,CAAC;EAC3C,MAAMJ,OAAO,CAACK,KAAK,CAACuV,MAAM,CAAC;EAE3BL,QAAQ,GAAGA,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAI,IAAApb,KAAA,CAAA+T,YAAY,EAAC,EAAE,CAAC;EAEvC;EACA,MAAMhT,KAAK,GAA2B;IAClCkJ,QAAQ;IACRtF,KAAK;IACLqW,WAAW;IACXha,SAAS;IACTuJ,YAAY;IACZ4Q,gBAAgB;IAChBzW,aAAa,EAAE,EAAE;IACjBqB,GAAG,EAAEqV,QAAQ;IACbF,gBAAgB;IAChBR;GACH;EAED,MAAMvK,yBAAyB,GAAG+I,SAAS,EAAE,GACzC,IAAAlZ,KAAA,CAAA+K,OAAO,EAACuI,GAAG,EAAE,mCAAmC,CAAC,GACjD,IAAI;EAER,IAAInD,yBAAyB,EAAE;IAC3B,MAAMtK,OAAO,CAAC9D,GAAG,CAAC5B,UAAA,CAAA8F,SAAS,EAAEmV,QAAQ,CAAC;;EAG1C;EACA,IAAIb,iBAAiB,EAAE;IACnBzR,MAAM,CAACC,MAAM,CAAChI,KAAK,CAAC2D,aAAc,EAAE6V,iBAAiB,CAAC;;EAG1D;EACA,IAAI9V,SAAS,EAAE;IACXqE,MAAM,CAACC,MAAM,CAAChI,KAAK,CAAC2D,aAAc,EAAE;MAAE1C,OAAO,EAAEyC;IAAS,CAAE,CAAC;;EAG/D;EACA,IAAI+V,WAAW,EAAE;IACb1R,MAAM,CAACC,MAAM,CAAChI,KAAK,CAAC2D,aAAc,EAAE;MAAErB,SAAS,EAAEmX;IAAW,CAAE,CAAC;;EAGnE,IAAIkB,WAAW,GAAGV,WAAW,GAAG,SAAS,GAAGjR,kBAAkB,CAACqR,QAAQ,CAAC;EAExE;EACA,IAAIL,cAAc,IAAI,CAACF,GAAG,EAAE;IACxBra,KAAK,CAAC,uBAAuB,CAAC;IAC9B,MAAMqF,OAAO,CAAC9D,GAAG,CAACqZ,QAAQ,EAAEra,KAAK,CAAC;IAClC,IAAIka,UAAU,EAAE;MACZ,OAAOS,WAAW;;IAEtB,OAAO,MAAMpI,GAAG,CAAC/C,QAAQ,CAACmL,WAAW,CAAC;;EAG1C;EACA,MAAM7B,UAAU,GAAG,MAAMG,qBAAqB,CAAChZ,SAAS,CAAC;EACzD8H,MAAM,CAACC,MAAM,CAAChI,KAAK,EAAE8Y,UAAU,CAAC;EAChC,MAAMhU,OAAO,CAAC9D,GAAG,CAACqZ,QAAQ,EAAEra,KAAK,CAAC;EAElC;EACA,IAAI,CAACA,KAAK,CAAC8D,YAAY,EAAE;IACrB,IAAIoW,UAAU,EAAE;MACZ,OAAOS,WAAW;;IAEtB,OAAO,MAAMpI,GAAG,CAAC/C,QAAQ,CAACmL,WAAW,CAAC;;EAG1C;EACA,MAAMC,cAAc,GAAG,CACnB,oBAAoB,EACpB,YAAY,GAAM5R,kBAAkB,CAACE,QAAQ,IAAI,EAAE,CAAC,EACpD,QAAQ,GAAUF,kBAAkB,CAACpF,KAAK,CAAC,EAC3C,eAAe,GAAGoF,kBAAkB,CAACiR,WAAW,CAAC,EACjD,MAAM,GAAYjR,kBAAkB,CAAC/I,SAAS,CAAC,EAC/C,QAAQ,GAAU+I,kBAAkB,CAACqR,QAAQ,CAAC,CACjD;EAED;EACA,IAAIN,MAAM,EAAE;IACRa,cAAc,CAACvO,IAAI,CAAC,SAAS,GAAGrD,kBAAkB,CAAC+Q,MAAM,CAAC,CAAC;;EAG/D,IAAIc,sBAAsB,CAAC/B,UAAU,CAACJ,oBAAoB,CAACtM,QAAQ,CAAC,MAAM,CAAC,EAAEsN,QAAQ,CAAC,EAAE;IACpF,IAAItF,KAAK,GAAG,MAAM7B,GAAG,CAACxD,QAAQ,CAAC8H,qBAAqB,EAAE;IACtD9O,MAAM,CAACC,MAAM,CAAChI,KAAK,EAAEoU,KAAK,CAAC;IAC3B,MAAMtP,OAAO,CAAC9D,GAAG,CAACqZ,QAAQ,EAAEra,KAAK,CAAC;IAClC4a,cAAc,CAACvO,IAAI,CAAC,iBAAiB,GAAGrM,KAAK,CAACiX,aAAa,CAAC,CAAC;IAC7D2D,cAAc,CAACvO,IAAI,CAAC,4BAA4B,CAAC;;EAGrDsO,WAAW,GAAG3a,KAAK,CAAC8D,YAAY,GAAG,GAAG,GAAG8W,cAAc,CAACnW,IAAI,CAAC,GAAG,CAAC;EAEjE,IAAIyV,UAAU,EAAE;IACZ,OAAOS,WAAW;;EAGtB,IAAIhG,MAAM,IAAIwD,SAAS,EAAE,EAAE;IACvB,IAAI2C,GAAW;IAEfA,GAAG,GAAG,MAAM,IAAA7b,KAAA,CAAAyV,eAAe,EAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,CAAC;IAElD,IAAIiG,GAAG,KAAKhG,IAAI,EAAE;MACd,IAAI;QACA;QACA;QACAgG,GAAG,CAACC,cAAc,CAACC,UAAU,CAACN,MAAM,CAAC;QACrCI,GAAG,CAACC,cAAc,CAACE,OAAO,CAACZ,QAAQ,EAAE9P,IAAI,CAACC,SAAS,CAACxK,KAAK,CAAC,CAAC;OAC9D,CAAC,OAAO4L,EAAE,EAAE;QACT,IAAA3M,KAAA,CAAAQ,KAAM,EAAC,2GAA2G,EAAEmM,EAAE,CAAC;QACvHkP,GAAG,GAAGhG,IAAI;;;IAIlB,IAAIgG,GAAG,KAAKhG,IAAI,EAAE;MACd,IAAI;QACAgG,GAAG,CAACvL,QAAQ,CAACpO,IAAI,GAAGwZ,WAAW;QAC/B7F,IAAI,CAACoG,gBAAgB,CAAC,SAAS,EAAEC,SAAS,CAAC;OAC9C,CAAC,OAAOvP,EAAE,EAAE;QACT,IAAA3M,KAAA,CAAAQ,KAAM,EAAC,qGAAqG,EAAEmM,EAAE,CAAC;QACjHkJ,IAAI,CAACvF,QAAQ,CAACpO,IAAI,GAAGwZ,WAAW;;KAEvC,MAAM;MACH7F,IAAI,CAACvF,QAAQ,CAACpO,IAAI,GAAGwZ,WAAW;;IAGpC;GACH,MACI;IACD,OAAO,MAAMpI,GAAG,CAAC/C,QAAQ,CAACmL,WAAW,CAAC;;AAE9C;AAvQAxQ,iBAAA,GAAAoG,SAAA;AAyQA,SAASsK,sBAAsBA,CAACO,aAAsB,EAAE1B,QAAiB;EACrE,IAAIA,QAAQ,KAAK,UAAU,EAAE;IACzB,OAAO,KAAK;;EAEhB,IAAIA,QAAQ,KAAK,UAAU,EAAE;IACzB,OAAO,IAAI;;EAEf,IAAIA,QAAQ,KAAK,UAAU,EAAE;IACzB,IAAI,CAAC0B,aAAa,EAAE;MAChB,MAAM,IAAIjZ,KAAK,CAAC,8GAA8G,CAAC;;IAEnI,OAAO,IAAI;;EAEf,OAAOiZ,aAAa;AACxB;AAEA;;;;;;AAMA,SAAgBb,SAASA,CAAA;EACrB,IAAI;IACA,OAAOzF,IAAI,KAAKE,GAAG,IAAID,MAAM,KAAKD,IAAI;GACzC,CAAC,OAAOK,CAAC,EAAE;IACR,OAAO,IAAI;;AAEnB;AANAhL,iBAAA,GAAAoQ,SAAA;AAQA;;;;;;;AAOA,SAAgBE,SAASA,CAAA;EACrB,IAAI;IACA,OAAO3F,IAAI,KAAKE,GAAG,IACZ,CAAC,CAACqG,MAAM,IACRA,MAAM,KAAKvG,IAAI,IACf,CAAC,CAACtV,MAAM,CAAC2O,IAAI;GACvB,CAAC,OAAOgH,CAAC,EAAE;IACR,OAAO,KAAK;;AAEpB;AATAhL,iBAAA,GAAAsQ,SAAA;AAWA;;;;;AAKA,SAAgBU,SAASA,CAAChG,CAAe;EACrC,IAAIA,CAAC,CAACnO,IAAI,CAACuH,IAAI,IAAI,cAAc,IAAI4G,CAAC,CAACmG,MAAM,KAAK,IAAIla,GAAG,CAAC0T,IAAI,CAACvF,QAAQ,CAACpO,IAAI,CAAC,CAACma,MAAM,EAAE;IAClF9b,MAAM,CAAC+b,mBAAmB,CAAC,SAAS,EAAEJ,SAAS,CAAC;IAChD3b,MAAM,CAAC+P,QAAQ,CAACpO,IAAI,GAAGgU,CAAC,CAACnO,IAAI,CAAC3F,GAAG;;AAEzC;AALA8I,iBAAA,GAAAgR,SAAA;AAOA;;;;;;AAMO,eAAe9K,KAAKA,CAACkC,GAAuB,EAAElQ,OAAA,GAAmC,EAAE;;EAEtF,MAAMhB,GAAG,GAAGkR,GAAG,CAACjD,MAAM,EAAE;EACxB,MAAMkM,OAAO,GAAGjJ,GAAG,CAACxN,UAAU,EAAE;EAChC,MAAMmU,MAAM,GAAG7X,GAAG,CAACN,YAAY;EAE/B,IAAIiE,GAAG,GAAsBkU,MAAM,CAACjU,GAAG,CAAC,OAAO,CAAC,IAAI5C,OAAO,CAACgY,QAAQ;EACpE,MAAM/I,IAAI,GAAmB4H,MAAM,CAACjU,GAAG,CAAC,MAAM,CAAC,IAAK5C,OAAO,CAACiP,IAAI;EAChE,MAAMmK,SAAS,GAAcvC,MAAM,CAACjU,GAAG,CAAC,OAAO,CAAC;EAChD,MAAMyW,oBAAoB,GAAGxC,MAAM,CAACjU,GAAG,CAAC,mBAAmB,CAAC;EAE5D,IAAI,CAACD,GAAG,EAAE;IACNA,GAAG,GAAG,MAAMwW,OAAO,CAACvW,GAAG,CAAC7F,UAAA,CAAA8F,SAAS,CAAC;;EAGtC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIuW,SAAS,IAAIC,oBAAoB,EAAE;IACnC,MAAM,IAAIvZ,KAAK,CAAC,CACZsZ,SAAS,EACTC,oBAAoB,CACvB,CAACvQ,MAAM,CAACC,OAAO,CAAC,CAAC3G,IAAI,CAAC,IAAI,CAAC,CAAC;;EAGjChF,KAAK,CAAC,mBAAmB,EAAEuF,GAAG,EAAEsM,IAAI,CAAC;EAErC;EACA,IAAArS,KAAA,CAAAuB,MAAM,EAACwE,GAAG,EAAE,wDAAwD,CAAC;EAErE;EACA,IAAIhF,KAAK,GAAI,MAAMwb,OAAO,CAACvW,GAAG,CAACD,GAAG,CAA4B;EAE9D,MAAMoK,yBAAyB,GAAG+I,SAAS,EAAE,GACzC,IAAAlZ,KAAA,CAAA+K,OAAO,EAACuI,GAAG,EAAE,mCAAmC,CAAC,GACjD,IAAI;EAER;EACA;EACA,IAAI4F,SAAS,EAAE,IAAInY,KAAK,IAAI,CAACA,KAAK,CAACma,gBAAgB,EAAE;IAEjD,MAAMG,OAAO,GAAGC,SAAS,EAAE;IAC3B,MAAMC,OAAO,GAAGC,SAAS,EAAE;IAE3B;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACH,OAAO,IAAIE,OAAO,KAAK,CAACnZ,GAAG,CAACN,YAAY,CAACkE,GAAG,CAAC,UAAU,CAAC,EAAE;MAC3D5D,GAAG,CAACN,YAAY,CAACC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;MACrC,MAAM;QAAEG,IAAI;QAAEma;MAAM,CAAE,GAAGja,GAAG;MAC5B,IAAIiZ,OAAO,EAAE;QACTvF,MAAM,CAAC4G,WAAW,CAAC;UAAEpN,IAAI,EAAE,cAAc;UAAElN,GAAG,EAAEF;QAAI,CAAE,EAAEma,MAAM,CAAC;;MAEnE,IAAId,OAAO,EAAE;QACTa,MAAM,CAACM,WAAW,CAAC;UAAEpN,IAAI,EAAE,cAAc;UAAElN,GAAG,EAAEF;QAAI,CAAE,EAAEma,MAAM,CAAC;QAC/D9b,MAAM,CAACoc,KAAK,EAAE;;MAGlB,OAAO,IAAI3Z,OAAO,CAAC,MAAK,CAA6B,CAAC,CAAC;;;EAI/DZ,GAAG,CAACN,YAAY,CAAC2J,MAAM,CAAC,UAAU,CAAC;EAEnC;EACA,MAAMmR,QAAQ,GAAG3C,MAAM,CAAC4C,GAAG,CAAC,OAAO,CAAC,IAAIzZ,OAAO,CAACgY,QAAQ,GAAG,IAAI,GAAG,KAAK;EAEvE,IAAIlC,SAAS,EAAE,IAAI,IAAAlZ,KAAA,CAAA+K,OAAO,EAACuI,GAAG,EAAE,+BAA+B,CAAC,KAAKjB,IAAI,IAAIuK,QAAQ,CAAC,EAAE;IACpF;IACA;IACA;IACA,IAAIvK,IAAI,EAAE;MACN4H,MAAM,CAACxO,MAAM,CAAC,MAAM,CAAC;MACrBjL,KAAK,CAAC,sCAAsC,CAAC;;IAGjD;IACA;IACA;IACA;IACA;IACA;IACA,IAAIoc,QAAQ,IAAIzM,yBAAyB,EAAE;MACvC8J,MAAM,CAACxO,MAAM,CAAC,OAAO,CAAC;MACtBjL,KAAK,CAAC,uCAAuC,CAAC;;IAGlD;IACA;IACA;IACA;IACA;IACA;IACA,IAAID,MAAM,CAACuc,OAAO,CAACC,YAAY,EAAE;MAC7Bxc,MAAM,CAACuc,OAAO,CAACC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE3a,GAAG,CAACF,IAAI,CAAC;;;EAIrD;EACA,IAAAlC,KAAA,CAAAuB,MAAM,EAACR,KAAK,EAAE,4CAA4C,CAAC;EAE3D;EACA;EACA,MAAMic,UAAU,GAAG,CAAC3K,IAAI,KAAI,CAAA5L,EAAA,GAAA1F,KAAK,CAAC2D,aAAa,cAAA+B,EAAA,uBAAAA,EAAA,CAAE+D,YAAY;EAE7D;EACA;EACA,IAAI,CAACwS,UAAU,IAAIjc,KAAK,CAAC2I,QAAQ,EAAE;IAE/B,IAAA1J,KAAA,CAAAuB,MAAM,EAAC8Q,IAAI,EAAE,kCAAkC,CAAC;IAEhD7R,KAAK,CAAC,oDAAoD,CAAC;IAC3D,MAAMG,cAAc,GAAG,MAAMsc,iBAAiB,CAAC3J,GAAG,EAAE;MAChDjB,IAAI;MACJtR,KAAK;MACL2Z,qBAAqB,EAAEtX,OAAO,CAACsX,qBAAqB;MACpDlC,UAAU,EAAEpV,OAAO,CAACoV,UAAU,IAAIzX,KAAK,CAACoa;KAC3C,CAAC;IACF3a,KAAK,CAAC,2BAA2B,EAAEG,cAAc,CAAC;IAElD;IACA;IACA;IACA,MAAM+D,aAAa,GAAG,MAAM,IAAA1E,KAAA,CAAA+C,OAAO,EAA2BhC,KAAK,CAAC2I,QAAQ,EAAE/I,cAAc,CAAC;IAC7FH,KAAK,CAAC,oBAAoB,EAAEkE,aAAa,CAAC;IAC1C,IAAA1E,KAAA,CAAAuB,MAAM,EAACmD,aAAa,CAAC8F,YAAY,EAAE,gCAAgC,CAAC;IAEpE;IACAzJ,KAAK,CAACoI,SAAS,GAAG,IAAAnJ,KAAA,CAAAyK,wBAAwB,EAAC/F,aAAa,EAAE4O,GAAG,CAAC;IAE9D;IACA;IACAvS,KAAK,GAAG;MAAE,GAAGA,KAAK;MAAE2D;IAAa,CAAE;IACnC,MAAM6X,OAAO,CAACxa,GAAG,CAACgE,GAAG,EAAEhF,KAAK,CAAC;IAC7BP,KAAK,CAAC,2BAA2B,CAAC;GACrC,MACI;IACDA,KAAK,CAAC,EAAAgJ,EAAA,GAAAzI,KAAK,CAAC2D,aAAa,cAAA8E,EAAA,uBAAAA,EAAA,CAAEgB,YAAY,IACnC,oBAAoB,GACpB,yBAAyB,CAC5B;;EAGL,IAAI2F,yBAAyB,EAAE;IAC3B,MAAMoM,OAAO,CAACxa,GAAG,CAAC5B,UAAA,CAAA8F,SAAS,EAAEF,GAAG,CAAC;;EAGrC,MAAMnF,MAAM,GAAG,IAAIgP,QAAA,CAAAtN,OAAM,CAACgR,GAAG,EAAEvS,KAAK,CAAC;EACrCP,KAAK,CAAC,6BAA6B,EAAEI,MAAM,CAAC;EAC5C,OAAOA,MAAM;AACjB;AA/JAsK,aAAA,GAAAkG,KAAA;AAiKA;;;;AAIO,eAAe6L,iBAAiBA,CACnC3J,GAAuB,EACvB;EACIjB,IAAI;EACJtR,KAAK;EACL2Z,qBAAqB;EACrBlC;AAAU,CA2Bb;EAGD,MAAM;IAAEwC,WAAW;IAAEzQ,YAAY;IAAEb,QAAQ;IAAEO,QAAQ;IAAE8N;EAAY,CAAE,GAAGhX,KAAK;EAE7E,IAAAf,KAAA,CAAAuB,MAAM,EAACyZ,WAAW,EAAE,2BAA2B,CAAC;EAChD,IAAAhb,KAAA,CAAAuB,MAAM,EAACmI,QAAQ,EAAE,wBAAwB,CAAC;EAC1C,IAAA1J,KAAA,CAAAuB,MAAM,EAAC0I,QAAQ,EAAE,wBAAwB,CAAC;EAE1C,MAAMtJ,cAAc,GAAwB;IACxC0J,MAAM,EAAE,MAAM;IACdnD,OAAO,EAAE;MAAE,cAAc,EAAE;IAAmC,CAAE;IAChEO,IAAI,EAAE,QAAQ4K,IAAI,+CACdtI,kBAAkB,CAACiR,WAAW,CAAC;GACtC;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIzQ,YAAY,EAAE;IACd5J,cAAc,CAACuG,OAAO,CAACC,aAAa,GAAG,QAAQ,GAAGmM,GAAG,CAAC3N,IAAI,CACtDsE,QAAQ,GAAG,GAAG,GAAGM,YAAY,CAChC;IACD/J,KAAK,CACD,oEAAoE,EACpEG,cAAc,CAACuG,OAAO,CAACC,aAAa,CACvC;;EAGL;EAAA,KACK,IAAIqR,UAAU,EAAE;IAEjB,MAAM0E,EAAE,GAAG,KAAK,IAAI1E,UAAU,GAC1BA,UAAU,CAACzS,GAAgB,GAC3B,MAAMuN,GAAG,CAACxD,QAAQ,CAACmI,SAAS,CAACO,UAA4B,CAAC;IAE9D,MAAM2E,UAAU,GAAG;MACfC,GAAG,EAAE,KAAK;MACVC,GAAG,EAAE7E,UAAU,CAAC6E,GAAG;MACnBC,GAAG,EAAE5C,qBAAqB,IAAI3Z,KAAK,CAAC2Z;KACvC;IAED,MAAM6C,SAAS,GAAG;MACd1C,GAAG,EAAE5Q,QAAQ;MACbwC,GAAG,EAAExC,QAAQ;MACbuT,GAAG,EAAE9T,QAAQ;MACb+T,GAAG,EAAEnK,GAAG,CAACzC,eAAe,CAACyC,GAAG,CAACxD,QAAQ,CAACuH,WAAW,CAAC,EAAE,CAAC,CAAC;MACtDzC,GAAG,EAAE,IAAA5U,KAAA,CAAAuU,eAAe,EAAC,GAAG,CAAC,CAAC;KAC7B;IAED,MAAMmJ,eAAe,GAAG,MAAMpK,GAAG,CAACxD,QAAQ,CAACyI,cAAc,CAACC,UAAU,CAACL,GAAG,EAAE+E,EAAE,EAAEC,UAAU,EAAEI,SAAS,CAAC;IACpG5c,cAAc,CAAC8G,IAAI,IAAI,0BAA0BsC,kBAAkB,CAAC,wDAAwD,CAAC,EAAE;IAC/HpJ,cAAc,CAAC8G,IAAI,IAAI,qBAAqBsC,kBAAkB,CAAC2T,eAAe,CAAC,EAAE;IACjFld,KAAK,CAAC,yEAAyE,CAAC;;EAGpF;EAAA,KACK;IACDA,KAAK,CAAC,gEAAgE,CAAC;IACvEG,cAAc,CAAC8G,IAAI,IAAI,cAAcsC,kBAAkB,CAACE,QAAQ,CAAC,EAAE;;EAGvE,IAAI8N,YAAY,EAAE;IAChBvX,KAAK,CAAC,mDAAmD,CAAC;IAC1D;IACAG,cAAc,CAAC8G,IAAI,IAAI,iBAAiB,GAAGsQ,YAAY;;EAGzD,OAAOpX,cAA6B;AACxC;AA1GAuK,yBAAA,GAAA+R,iBAAA;AA4GA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BO,eAAe1L,IAAIA,CACtB+B,GAAuB,EACvBqK,gBAA4C,EAC5CC,YAAsC;EAGtC,MAAMxb,GAAG,GAAKkR,GAAG,CAACjD,MAAM,EAAE;EAC1B,MAAMgC,IAAI,GAAIjQ,GAAG,CAACN,YAAY,CAACkE,GAAG,CAAC,MAAM,CAAC;EAC1C,MAAMjF,KAAK,GAAGqB,GAAG,CAACN,YAAY,CAACkE,GAAG,CAAC,OAAO,CAAC;EAE3C;EACA,IAAIqM,IAAI,IAAItR,KAAK,EAAE;IACf,OAAOqQ,KAAK,CAACkC,GAAG,EAAEsK,YAAY,CAAC;;EAGnC;EACA;EACA;EACA,MAAM/X,OAAO,GAAGyN,GAAG,CAACxN,UAAU,EAAE;EAChC,MAAMC,GAAG,GAAOhF,KAAK,KAAI,MAAM8E,OAAO,CAACG,GAAG,CAAC7F,UAAA,CAAA8F,SAAS,CAAC;EACrD,MAAM4X,MAAM,GAAI,MAAMhY,OAAO,CAACG,GAAG,CAACD,GAAG,CAAC;EACtC,IAAI8X,MAAM,EAAE;IACR,OAAO,IAAIjO,QAAA,CAAAtN,OAAM,CAACgR,GAAG,EAAEuK,MAAM,CAAC;;EAGlC;EACA,OAAOvM,SAAS,CAACgC,GAAG,EAAEqK,gBAAgB,CAAC,CAACrW,IAAI,CAAC,MAAK;IAC9C;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,IAAItE,OAAO,CAAC,MAAK,CAA6B,CAAC,CAAC;EAC3D,CAAC,CAAC;AACN;AApCAkI,YAAA,GAAAqG,IAAA;;;;;;;;;;;;;;;;AC/uBA,MAAqBgL,OAAO;EAExB;;;;EAIA,MAAMvW,GAAGA,CAACD,GAAW;IAEjB,MAAMuM,KAAK,GAAGwJ,cAAc,CAAC/V,GAAG,CAAC;IACjC,IAAIuM,KAAK,EAAE;MACP,OAAOhH,IAAI,CAAC8D,KAAK,CAACkD,KAAK,CAAC;;IAE5B,OAAO,IAAI;EACf;EAEA;;;;EAIA,MAAMvQ,GAAGA,CAACgE,GAAW,EAAEuM,KAAU;IAE7BwJ,cAAc,CAAC/V,GAAG,CAAC,GAAGuF,IAAI,CAACC,SAAS,CAAC+G,KAAK,CAAC;IAC3C,OAAOA,KAAK;EAChB;EAEA;;;;;EAKA,MAAMpM,KAAKA,CAACH,GAAW;IAEnB,IAAIA,GAAG,IAAI+V,cAAc,EAAE;MACvB,OAAOA,cAAc,CAAC/V,GAAG,CAAC;MAC1B,OAAO,IAAI;;IAEf,OAAO,KAAK;EAChB;;AArCJmF,kBAAA,GAAAqR,OAAA;;;;;;;;;;;;;;;;ACAA;AACArR,kBAAA,GAAe;EACXpD,OAAO,EAAQ,2CAA2C;EAC1DlD,YAAY,EAAG,oHAAoH;EACnIE,UAAU,EAAK,6DAA6D;EAC5EC,aAAa,EAAE;CAClB;;;;;;;;;;ACND;;AAEA;AACA;AACA;;AAEA,kBAAkB;AAClB,YAAY;AACZ,YAAY;AACZ,iBAAiB;AACjB,eAAe;AACf,eAAe;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;;AAEA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;AAEF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,4CAA4C;;AAEvD;AACA;AACA;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY,QAAQ;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA,iBAAiB,mBAAO,CAAC,oDAAU;;AAEnC,OAAO,YAAY;;AAEnB;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;;;;;;;;;;AC7QA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAO,CAAC,sCAAI;AACpC;;AAEA;AACA;AACA,EAAE;;AAEF;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,eAAe;AAC3B;AACA;AACA;AACA;;AAEA,kBAAkB,sBAAsB;AACxC;AACA,cAAc;AACd;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,uCAAuC;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,cAAc,SAAS;AACvB;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY,QAAQ;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;;AAEA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;;;;;;;;;;ACjRA;AACA;AACA;AACA;AACA;AACA,IAAI,KAA4D;AAChE;AACA,UAAU,CAeM;AAChB,CAAC;AACD;AACA,iBAAiB,qBAAM,mBAAmB,qBAAM;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,oBAAoB;AACxD;AACA,KAAK;AACL,mCAAmC,EAAE,oBAAoB,EAAE,sBAAsB,EAAE;AACnF;AACA;AACA;AACA,0BAA0B;AAC1B,sCAAsC;AACtC,6DAA6D,+BAA+B;AAC5F,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,eAAe;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,QAAQ;AACzB;AACA,8DAA8D;AAC9D,wCAAwC;AACxC;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA,4CAA4C,OAAO;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAAS;AACxB,iBAAiB,QAAQ;AACzB;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,QAAQ;AACzB;AACA,8BAA8B;AAC9B;AACA;AACA,yBAAyB;AACzB;AACA,6BAA6B;AAC7B,6BAA6B;AAC7B;AACA;AACA,eAAe,SAAS;AACxB,iBAAiB,QAAQ;AACzB;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA,qCAAqC;AACrC;AACA;AACA;AACA,iEAAiE,EAAE,wBAAwB,EAAE;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,QAAQ;AACzB;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,eAAe;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,QAAQ;AACzB;AACA,8DAA8D;AAC9D,wCAAwC;AACxC;AACA;AACA;AACA,yBAAyB;AACzB,yBAAyB,sDAAsD,yBAAyB;AACxG;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA,yBAAyB;AACzB;AACA,6BAA6B;AAC7B,6BAA6B;AAC7B,gCAAgC,mDAAmD,+BAA+B;AAClH;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,QAAQ;AACzB;AACA,kCAAkC;AAClC;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA,mDAAmD,IAAI;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C,yCAAyC,sBAAsB;AAC/D,8CAA8C,+BAA+B;AAC7E,0CAA0C,4BAA4B;AACtE,0CAA0C,4BAA4B;AACtE,2CAA2C,4BAA4B;AACvE;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C,8CAA8C,uCAAuC;AACrF,0CAA0C,oCAAoC;AAC9E,0CAA0C,oCAAoC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA,gDAAgD,wCAAwC;AACxF;AACA,CAAC;;;;;;;;;;;ACzTD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,eAAe;AAC1B,WAAW,QAAQ;AACnB,YAAY,OAAO;AACnB,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACjK6B;AAC7B,iEAAe,aAAa;;;;;;;;;;;;ACD5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA,CAAC,sCAAsC,SAAI;AAC3C;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,yBAAyB,cAAc;AACvC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS,yBAAyB;AAClC;;AAEA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA,+FAA+F,8BAA8B;AAC7H;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,yDAAyD;AACzD,oBAAoB;AACpB;AACA;AACA;AACA;AACA,2CAA2C;AAC3C,iCAAiC,mBAAmB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,oBAAoB;AACrD;AACA;AACA;AACA;AACA,4FAA4F;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC,kDAAkD;AAClD;AACA;;AAEA,oBAAoB,wCAAwC;AAC5D,oBAAoB;;AAEpB;AACA;AACA,4BAA4B;;AAE5B;;AAEA;AACA;;AAEA;AACA;AACA;AACA,gCAAgC,OAAO;AACvC;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,eAAe;AAC9D;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,0BAA0B;;AAE1B,4CAA4C;AAC5C;AACA,6BAA6B,SAAS;AACtC;AACA,iDAAiD;AACjD;AACA,6BAA6B,SAAS;AACtC;AACA,mCAAmC;AACnC;AACA;AACA,gEAAgE;AAChE;AACA;AACA,6BAA6B,SAAS;AACtC;AACA,2CAA2C;AAC3C,6BAA6B,gBAAgB;AAC7C;AACA;AACA,sGAAsG;AACtG;AACA,yBAAyB,iBAAiB,SAAS;AACnD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,+CAA+C,gEAAgE;AAC/G;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;;AAEA,6DAA6D;AAC7D;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,yDAAyD;AACzD,yDAAyD;AACzD,qBAAqB;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;;AAEjB;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sEAAsE;AACtE;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,yDAAyD;AACzD,yDAAyD;AACzD,qBAAqB;AACrB;;AAEA;AACA;AACA,8EAA8E;AAC9E;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,qBAAqB;AACrB;;AAEA;AACA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iDAAiD;AACjD,iDAAiD;AACjD,aAAa;;AAEb;AACA;;AAEA;AACA,+BAA+B,wBAAwB,qCAAqC;AAC5F,+BAA+B,gBAAgB;AAC/C,SAAS;;AAET;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED,CAAC,sEAAe,EAAE,GAAC;;;;;;;UCnmBnB;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC;;;;;WCPD;;;;;UEAA;UACA;UACA;UACA", "sources": ["webpack://FHIR/./src/Client.ts", "webpack://FHIR/./src/FhirClient.ts", "webpack://FHIR/./src/HttpError.ts", "webpack://FHIR/./src/adapters/BrowserAdapter.ts", "webpack://FHIR/./src/entry/browser.ts", "webpack://FHIR/./src/lib.ts", "webpack://FHIR/./src/security/browser.ts", "webpack://FHIR/./src/settings.ts", "webpack://FHIR/./src/smart.ts", "webpack://FHIR/./src/storage/BrowserStorage.ts", "webpack://FHIR/./src/strings.ts", "webpack://FHIR/./node_modules/debug/src/browser.js", "webpack://FHIR/./node_modules/debug/src/common.js", "webpack://FHIR/./node_modules/js-base64/base64.js", "webpack://FHIR/./node_modules/ms/index.js", "webpack://FHIR/./node_modules/isomorphic-webcrypto/src/browser.mjs", "webpack://FHIR/./node_modules/isomorphic-webcrypto/src/webcrypto-shim.mjs", "webpack://FHIR/webpack/bootstrap", "webpack://FHIR/webpack/runtime/define property getters", "webpack://FHIR/webpack/runtime/global", "webpack://FHIR/webpack/runtime/hasOwnProperty shorthand", "webpack://FHIR/webpack/before-startup", "webpack://FHIR/webpack/startup", "webpack://FHIR/webpack/after-startup"], "sourcesContent": ["import {\n    absolute,\n    debug as _debug,\n    getPath,\n    jwtDecode,\n    makeArray,\n    request,\n    byCode,\n    byCodes,\n    units,\n    getPatientParam,\n    fetchConformanceStatement,\n    getAccessTokenExpiration,\n    assert\n} from \"./lib\";\n\nimport str from \"./strings\";\nimport { SMART_KEY, patientCompartment } from \"./settings\";\nimport HttpError from \"./HttpError\";\nimport BrowserAdapter from \"./adapters/BrowserAdapter\";\nimport { fhirclient } from \"./types\";\nimport FhirClient from \"./FhirClient\";\n\n// $lab:coverage:off$\n// @ts-ignore\nconst { Response } = typeof FHIRCLIENT_PURE !== \"undefined\" ? window : require(\"cross-fetch\");\n// $lab:coverage:on$\n\nconst debug = _debug.extend(\"client\");\n\n/**\n * Adds patient context to requestOptions object to be used with [[Client.request]]\n * @param requestOptions Can be a string URL (relative to the serviceUrl), or an\n * object which will be passed to fetch()\n * @param client Current FHIR client object containing patient context\n * @return requestOptions object contextualized to current patient\n */\nasync function contextualize(\n    requestOptions: string | URL | fhirclient.RequestOptions,\n    client: Client\n): Promise<fhirclient.RequestOptions>\n{\n    const base = absolute(\"/\", client.state.serverUrl);\n\n    async function contextualURL(_url: URL) {\n        const resourceType = _url.pathname.split(\"/\").pop();\n        assert(resourceType, `Invalid url \"${_url}\"`);\n        assert(patientCompartment.indexOf(resourceType) > -1, `Cannot filter \"${resourceType}\" resources by patient`);\n        const conformance = await fetchConformanceStatement(client.state.serverUrl);\n        const searchParam = getPatientParam(conformance, resourceType);\n        _url.searchParams.set(searchParam, client.patient.id as string);\n        return _url.href;\n    }\n\n    if (typeof requestOptions == \"string\" || requestOptions instanceof URL) {\n        return { url: await contextualURL(new URL(requestOptions + \"\", base)) };\n    }\n\n    requestOptions.url = await contextualURL(new URL(requestOptions.url + \"\", base));\n    return requestOptions;\n}\n\n/**\n * This is a FHIR client that is returned to you from the `ready()` call of the\n * **SMART API**. You can also create it yourself if needed:\n *\n * ```js\n * // BROWSER\n * const client = FHIR.client(\"https://r4.smarthealthit.org\");\n *\n * // SERVER\n * const client = smart(req, res).client(\"https://r4.smarthealthit.org\");\n * ```\n */\nexport default class Client extends FhirClient\n{\n    /**\n     * The state of the client instance is an object with various properties.\n     * It contains some details about how the client has been authorized and\n     * determines the behavior of the client instance. This state is persisted\n     * in `SessionStorage` in browsers or in request session on the servers.\n     */\n    readonly state: fhirclient.ClientState;\n\n    /**\n     * The adapter to use to connect to the current environment. Currently we have:\n     * - BrowserAdapter - for browsers\n     * - NodeAdapter - for Express or vanilla NodeJS servers\n     * - HapiAdapter - for HAPI NodeJS servers\n     */\n    readonly environment: fhirclient.Adapter;\n\n    /**\n     * A SMART app is typically associated with a patient. This is a namespace\n     * for the patient-related functionality of the client.\n     */\n    readonly patient: {\n\n        /**\n         * The ID of the current patient or `null` if there is no current patient\n         */\n        id: string | null\n\n        /**\n         * A method to fetch the current patient resource from the FHIR server.\n         * If there is no patient context, it will reject with an error.\n         * @param {fhirclient.FetchOptions} [requestOptions] Any options to pass to the `fetch` call.\n         * @category Request\n         */\n        read: fhirclient.RequestFunction<fhirclient.FHIR.Patient>\n        \n        /**\n         * This is similar to [[request]] but it makes requests in the\n         * context of the current patient. For example, instead of doing\n         * ```js\n         * client.request(\"Observation?patient=\" + client.patient.id)\n         * ```\n         * you can do\n         * ```js\n         * client.patient.request(\"Observation\")\n         * ```\n         * The return type depends on the arguments. Typically it will be the\n         * response payload JSON object. Can also be a string or the `Response`\n         * object itself if we have received a non-json result, which allows us\n         * to handle even binary responses. Can also be a [[CombinedFetchResult]]\n         * object if the `requestOptions.includeResponse`s has been set to true.\n         * @category Request\n         */\n        request: <R = fhirclient.FetchResult>(\n            requestOptions: string|URL|fhirclient.RequestOptions,\n            fhirOptions?: fhirclient.FhirOptions\n        ) => Promise<R>\n\n        /**\n         * This is the FhirJS Patient API. It will ONLY exist if the `Client`\n         * instance is \"connected\" to FhirJS.\n         */\n        api?: Record<string, any>\n    };\n\n    /**\n     * The client may be associated with a specific encounter, if the scopes\n     * permit that and if the back-end server supports that. This is a namespace\n     * for encounter-related functionality.\n     */\n    readonly encounter: {\n\n        /**\n         * The ID of the current encounter or `null` if there is no current\n         * encounter\n         */\n        id: string | null\n\n        /**\n         * A method to fetch the current encounter resource from the FHIR server.\n         * If there is no encounter context, it will reject with an error.\n         * @param [requestOptions] Any options to pass to the `fetch` call.\n         * @category Request\n         */\n        read: fhirclient.RequestFunction<fhirclient.FHIR.Encounter>\n    };\n\n    /**\n     * The client may be associated with a specific user, if the scopes\n     * permit that. This is a namespace for user-related functionality.\n     */\n    readonly user: {\n\n        /**\n         * The ID of the current user or `null` if there is no current user\n         */\n        id: string | null\n\n        /**\n         * A method to fetch the current user resource from the FHIR server.\n         * If there is no user context, it will reject with an error.\n         * @param [requestOptions] Any options to pass to the `fetch` call.\n         * @category Request\n         */\n        read: fhirclient.RequestFunction<\n            fhirclient.FHIR.Patient |\n            fhirclient.FHIR.Practitioner |\n            fhirclient.FHIR.RelatedPerson\n        >\n\n        /**\n         * Returns the profile of the logged_in user (if any), or null if the\n         * user is not available. This is a string having the shape\n         * `{user type}/{user id}`. For example `Practitioner/abc` or\n         * `Patient/xyz`.\n         * @alias client.getFhirUser()\n         */\n        fhirUser: string | null\n\n        /**\n         * Returns the type of the logged-in user or null. The result can be\n         * `Practitioner`, `Patient` or `RelatedPerson`.\n         * @alias client.getUserType()\n         */\n        resourceType: string | null\n    };\n\n    /**\n     * The [FhirJS](https://github.com/FHIR/fhir.js/blob/master/README.md) API.\n     * **NOTE:** This will only be available if `fhir.js` is used. Otherwise it\n     * will be `undefined`.\n     */\n    api: Record<string, any> | undefined;\n\n    /**\n     * Refers to the refresh task while it is being performed.\n     * @see [[refresh]]\n     */\n    private _refreshTask: Promise<any> | null;\n\n    /**\n     * Validates the parameters, creates an instance and tries to connect it to\n     * FhirJS, if one is available globally.\n     */\n    constructor(environment: fhirclient.Adapter, state: fhirclient.ClientState | string)\n    {\n        const _state = typeof state == \"string\" ? { serverUrl: state } : state;\n        \n        // Valid serverUrl is required!\n        assert(\n            _state.serverUrl && _state.serverUrl.match(/https?:\\/\\/.+/),\n            \"A \\\"serverUrl\\\" option is required and must begin with \\\"http(s)\\\"\"\n        );\n        \n        super(_state.serverUrl)\n\n        this.state = _state;\n        this.environment = environment;\n        this._refreshTask = null;\n\n        const client = this;\n\n        // patient api ---------------------------------------------------------\n        this.patient = {\n            get id() { return client.getPatientId(); },\n            read: (requestOptions) => {\n                const id = this.patient.id;\n                return id ?\n                    this.request({ ...requestOptions, url: `Patient/${id}` }) :\n                    Promise.reject(new Error(\"Patient is not available\"));\n            },\n            request: (requestOptions, fhirOptions = {}) => {\n                if (this.patient.id) {\n                    return (async () => {\n                        const options = await contextualize(requestOptions, this);\n                        return this.request(options, fhirOptions);\n                    })();\n                } else {\n                    return Promise.reject(new Error(\"Patient is not available\"));\n                }\n            }\n        };\n\n        // encounter api -------------------------------------------------------\n        this.encounter = {\n            get id() { return client.getEncounterId(); },\n            read: requestOptions => {\n                const id = this.encounter.id;\n                return id ?\n                    this.request({ ...requestOptions, url: `Encounter/${id}` }) :\n                    Promise.reject(new Error(\"Encounter is not available\"));\n            }\n        };\n\n        // user api ------------------------------------------------------------\n        this.user = {\n            get fhirUser() { return client.getFhirUser(); },\n            get id() { return client.getUserId(); },\n            get resourceType() { return client.getUserType(); },\n            read: requestOptions => {\n                const fhirUser = this.user.fhirUser;\n                return fhirUser ?\n                    this.request({ ...requestOptions, url: fhirUser }) :\n                    Promise.reject(new Error(\"User is not available\"));\n            }\n        };\n\n        // fhir.js api (attached automatically in browser)\n        // ---------------------------------------------------------------------\n        this.connect((environment as BrowserAdapter).fhir);\n    }\n\n    /**\n     * This method is used to make the \"link\" between the `fhirclient` and the\n     * `fhir.js`, if one is available.\n     * **Note:** This is called by the constructor. If fhir.js is available in\n     * the global scope as `fhir`, it will automatically be linked to any [[Client]]\n     * instance. You should only use this method to connect to `fhir.js` which\n     * is not global.\n     */\n    connect(fhirJs?: (options: Record<string, any>) => Record<string, any>): Client\n    {\n        if (typeof fhirJs == \"function\") {\n            const options: Record<string, any> = {\n                baseUrl: this.state.serverUrl.replace(/\\/$/, \"\")\n            };\n\n            const accessToken = this.getState(\"tokenResponse.access_token\");\n            if (accessToken) {\n                options.auth = { token: accessToken };\n            }\n            else {\n                const { username, password } = this.state;\n                if (username && password) {\n                    options.auth = {\n                        user: username,\n                        pass: password\n                    };\n                }\n            }\n            this.api = fhirJs(options);\n\n            const patientId = this.getState(\"tokenResponse.patient\");\n            if (patientId) {\n                this.patient.api = fhirJs({\n                    ...options,\n                    patient: patientId\n                });\n            }\n        }\n        return this;\n    }\n\n    /**\n     * Returns the ID of the selected patient or null. You should have requested\n     * \"launch/patient\" scope. Otherwise this will return null.\n     */\n    getPatientId(): string | null\n    {\n        const tokenResponse = this.state.tokenResponse;\n        if (tokenResponse) {\n            // We have been authorized against this server but we don't know\n            // the patient. This should be a scope issue.\n            if (!tokenResponse.patient) {\n                if (!(this.state.scope || \"\").match(/\\blaunch(\\/patient)?\\b/)) {\n                    debug(str.noScopeForId, \"patient\", \"patient\");\n                }\n                else {\n                    // The server should have returned the patient!\n                    debug(\"The ID of the selected patient is not available. Please check if your server supports that.\");\n                }\n                return null;\n            }\n            return tokenResponse.patient;\n        }\n\n        if (this.state.authorizeUri) {\n            debug(str.noIfNoAuth, \"the ID of the selected patient\");\n        }\n        else {\n            debug(str.noFreeContext, \"selected patient\");\n        }\n        return null;\n    }\n\n    /**\n     * Returns the ID of the selected encounter or null. You should have\n     * requested \"launch/encounter\" scope. Otherwise this will return null.\n     * Note that not all servers support the \"launch/encounter\" scope so this\n     * will be null if they don't.\n     */\n    getEncounterId(): string | null\n    {\n        const tokenResponse = this.state.tokenResponse;\n        if (tokenResponse) {\n            // We have been authorized against this server but we don't know\n            // the encounter. This should be a scope issue.\n            if (!tokenResponse.encounter) {\n                if (!(this.state.scope || \"\").match(/\\blaunch(\\/encounter)?\\b/)) {\n                    debug(str.noScopeForId, \"encounter\", \"encounter\");\n                }\n                else {\n                    // The server should have returned the encounter!\n                    debug(\"The ID of the selected encounter is not available. Please check if your server supports that, and that the selected patient has any recorded encounters.\");\n                }\n                return null;\n            }\n            return tokenResponse.encounter;\n        }\n\n        if (this.state.authorizeUri) {\n            debug(str.noIfNoAuth, \"the ID of the selected encounter\");\n        }\n        else {\n            debug(str.noFreeContext, \"selected encounter\");\n        }\n        return null;\n    }\n\n    /**\n     * Returns the (decoded) id_token if any. You need to request \"openid\" and\n     * \"profile\" scopes if you need to receive an id_token (if you need to know\n     * who the logged-in user is).\n     */\n    getIdToken(): fhirclient.IDToken | null\n    {\n        const tokenResponse = this.state.tokenResponse;\n        if (tokenResponse) {\n            const idToken = tokenResponse.id_token;\n            const scope = this.state.scope || \"\";\n\n            // We have been authorized against this server but we don't have\n            // the id_token. This should be a scope issue.\n            if (!idToken) {\n                const hasOpenid   = scope.match(/\\bopenid\\b/);\n                const hasProfile  = scope.match(/\\bprofile\\b/);\n                const hasFhirUser = scope.match(/\\bfhirUser\\b/);\n                if (!hasOpenid || !(hasFhirUser || hasProfile)) {\n                    debug(\n                        \"You are trying to get the id_token but you are not \" +\n                        \"using the right scopes. Please add 'openid' and \" +\n                        \"'fhirUser' or 'profile' to the scopes you are \" +\n                        \"requesting.\"\n                    );\n                }\n                else {\n                    // The server should have returned the id_token!\n                    debug(\"The id_token is not available. Please check if your server supports that.\");\n                }\n                return null;\n            }\n            return jwtDecode(idToken, this.environment) as fhirclient.IDToken;\n        }\n        if (this.state.authorizeUri) {\n            debug(str.noIfNoAuth, \"the id_token\");\n        }\n        else {\n            debug(str.noFreeContext, \"id_token\");\n        }\n        return null;\n    }\n\n    /**\n     * Returns the profile of the logged_in user (if any). This is a string\n     * having the following shape `\"{user type}/{user id}\"`. For example:\n     * `\"Practitioner/abc\"` or `\"Patient/xyz\"`.\n     */\n    getFhirUser(): string | null\n    {\n        const idToken = this.getIdToken();\n        if (idToken) {\n            // Epic may return a full url\n            // @see https://github.com/smart-on-fhir/client-js/issues/105\n            if (idToken.fhirUser) {\n                return idToken.fhirUser.split(\"/\").slice(-2).join(\"/\");\n            }\n            return idToken.profile\n        }\n        return null;\n    }\n\n    /**\n     * Returns the user ID or null.\n     */\n    getUserId(): string | null\n    {\n        const profile = this.getFhirUser();\n        if (profile) {\n            return profile.split(\"/\")[1];\n        }\n        return null;\n    }\n\n    /**\n     * Returns the type of the logged-in user or null. The result can be\n     * \"Practitioner\", \"Patient\" or \"RelatedPerson\".\n     */\n    getUserType(): string | null\n    {\n        const profile = this.getFhirUser();\n        if (profile) {\n            return profile.split(\"/\")[0];\n        }\n        return null;\n    }\n\n    /**\n     * Builds and returns the value of the `Authorization` header that can be\n     * sent to the FHIR server\n     */\n    getAuthorizationHeader(): string | null\n    {\n        const accessToken = this.getState(\"tokenResponse.access_token\");\n        if (accessToken) {\n            return \"Bearer \" + accessToken;\n        }\n        const { username, password } = this.state;\n        if (username && password) {\n            return \"Basic \" + this.environment.btoa(username + \":\" + password);\n        }\n        return null;\n    }\n\n    /**\n     * Used internally to clear the state of the instance and the state in the\n     * associated storage.\n     */\n    private async _clearState() {\n        const storage = this.environment.getStorage();\n        const key = await storage.get(SMART_KEY);\n        if (key) {\n            await storage.unset(key);\n        }\n        await storage.unset(SMART_KEY);\n        this.state.tokenResponse = {};\n    }\n\n    /**\n     * @param requestOptions Can be a string URL (relative to the serviceUrl),\n     * or an object which will be passed to fetch()\n     * @param fhirOptions Additional options to control the behavior\n     * @param _resolvedRefs DO NOT USE! Used internally.\n     * @category Request\n     */\n    async request<T = any>(\n        requestOptions: string|URL|fhirclient.RequestOptions,\n        fhirOptions: fhirclient.FhirOptions = {},\n        _resolvedRefs: fhirclient.JsonObject = {}\n    ): Promise<T>\n    {\n        const debugRequest = _debug.extend(\"client:request\");\n        assert(requestOptions, \"request requires an url or request options as argument\");\n\n        // url -----------------------------------------------------------------\n        let url: string;\n        if (typeof requestOptions == \"string\" || requestOptions instanceof URL) {\n            url = String(requestOptions);\n            requestOptions = {} as fhirclient.RequestOptions;\n        }\n        else {\n            url = String(requestOptions.url);\n        }\n\n        url = absolute(url, this.state.serverUrl);\n\n        const options = {\n            graph: fhirOptions.graph !== false,\n            flat : !!fhirOptions.flat,\n            pageLimit: fhirOptions.pageLimit ?? 1,\n            resolveReferences: makeArray(fhirOptions.resolveReferences || []) as string[],\n            useRefreshToken: fhirOptions.useRefreshToken !== false,\n            onPage: typeof fhirOptions.onPage == \"function\" ?\n                fhirOptions.onPage as (\n                    data: fhirclient.JsonObject | fhirclient.JsonObject[],\n                    references?: fhirclient.JsonObject | undefined) => any :\n                undefined\n        };\n\n        const signal = (requestOptions as RequestInit).signal || undefined;\n\n        // Refresh the access token if needed\n        if (options.useRefreshToken) {\n            await this.refreshIfNeeded({ signal })\n        }\n\n        // Add the Authorization header now, after the access token might\n        // have been updated\n        const authHeader = this.getAuthorizationHeader();\n        if (authHeader) {\n            requestOptions.headers = {\n                ...requestOptions.headers,\n                authorization: authHeader\n            };\n        }\n\n        debugRequest(\"%s, options: %O, fhirOptions: %O\", url, requestOptions, options);\n\n        let response: Response | undefined;\n\n        return super.fhirRequest<fhirclient.FetchResult>(url, requestOptions).then(result => {\n            if ((requestOptions as fhirclient.RequestOptions).includeResponse) {\n                response = (result as fhirclient.CombinedFetchResult).response;\n                return (result as fhirclient.CombinedFetchResult).body;\n            }\n            return result;\n        })\n\n        // Handle 401 ----------------------------------------------------------\n        .catch(async (error: HttpError) => {\n            if (error.status == 401) {\n\n                // !accessToken -> not authorized -> No session. Need to launch.\n                if (!this.getState(\"tokenResponse.access_token\")) {\n                    error.message += \"\\nThis app cannot be accessed directly. Please launch it as SMART app!\";\n                    throw error;\n                }\n\n                // auto-refresh not enabled and Session expired.\n                // Need to re-launch. Clear state to start over!\n                if (!options.useRefreshToken) {\n                    debugRequest(\"Your session has expired and the useRefreshToken option is set to false. Please re-launch the app.\");\n                    await this._clearState();\n                    error.message += \"\\n\" + str.expired;\n                    throw error;\n                }\n\n                // In rare cases we may have a valid access token and a refresh\n                // token and the request might still fail with 401 just because\n                // the access token has just been revoked.\n\n                // otherwise -> auto-refresh failed. Session expired.\n                // Need to re-launch. Clear state to start over!\n                debugRequest(\"Auto-refresh failed! Please re-launch the app.\");\n                await this._clearState();\n                error.message += \"\\n\" + str.expired;\n                throw error;\n            }\n            throw error;\n        })\n\n        // Handle 403 ----------------------------------------------------------\n        .catch((error: HttpError) => {\n            if (error.status == 403) {\n                debugRequest(\"Permission denied! Please make sure that you have requested the proper scopes.\");\n            }\n            throw error;\n        })\n\n        .then(async (data: any) => {\n\n            // At this point we don't know what `data` actually is!\n            // We might get an empty or falsy result. If so return it as is\n            // Also handle raw responses\n            if (!data || typeof data == \"string\" || data instanceof Response) {\n                if ((requestOptions as fhirclient.FetchOptions).includeResponse) {\n                    return {\n                        body: data,\n                        response\n                    }\n                }\n                return data;\n            }\n            \n            // Resolve References ----------------------------------------------\n            await this.fetchReferences(\n                data as any,\n                options.resolveReferences,\n                options.graph,\n                _resolvedRefs,\n                requestOptions as fhirclient.RequestOptions\n            );\n\n            return Promise.resolve(data)\n\n            // Pagination ------------------------------------------------------\n            .then(async _data => {\n                if (_data && _data.resourceType == \"Bundle\") {\n                    const links = (_data.link || []) as fhirclient.FHIR.BundleLink[];\n\n                    if (options.flat) {\n                        _data = (_data.entry || []).map(\n                            (entry: fhirclient.FHIR.BundleEntry) => entry.resource\n                        );\n                    }\n\n                    if (options.onPage) {\n                        await options.onPage(_data, { ..._resolvedRefs });\n                    }\n\n                    if (--options.pageLimit) {\n                        const next = links.find(l => l.relation == \"next\");\n                        _data = makeArray(_data);\n                        if (next && next.url) {\n                            const nextPage = await this.request(\n                                {\n                                    url: next.url,\n\n                                    // Aborting the main request (even after it is complete)\n                                    // must propagate to any child requests and abort them!\n                                    // To do so, just pass the same AbortSignal if one is\n                                    // provided.\n                                    signal\n                                },\n                                options,\n                                _resolvedRefs\n                            );\n\n                            if (options.onPage) {\n                                return null;\n                            }\n\n                            if (options.resolveReferences.length) {\n                                Object.assign(_resolvedRefs, nextPage.references);\n                                return _data.concat(makeArray(nextPage.data || nextPage));\n                            }\n                            return _data.concat(makeArray(nextPage));\n                        }\n                    }\n                }\n                return _data;\n            })\n\n            // Finalize --------------------------------------------------------\n            .then(_data => {\n                if (options.graph) {\n                    _resolvedRefs = {};\n                }\n                else if (!options.onPage && options.resolveReferences.length) {\n                    return {\n                        data: _data,\n                        references: _resolvedRefs\n                    };\n                }\n                return _data;\n            })\n            .then(_data => {\n                if ((requestOptions as fhirclient.FetchOptions).includeResponse) {\n                    return {\n                        body: _data,\n                        response\n                    }\n                }\n                return _data;\n            });\n        });\n    }\n\n    /**\n     * Checks if access token and refresh token are present. If they are, and if\n     * the access token is expired or is about to expire in the next 10 seconds,\n     * calls `this.refresh()` to obtain new access token.\n     * @param requestOptions Any options to pass to the fetch call. Most of them\n     * will be overridden, bit it might still be useful for passing additional\n     * request options or an abort signal.\n     * @category Request\n     */\n    refreshIfNeeded(requestOptions: RequestInit = {}): Promise<fhirclient.ClientState>\n    {\n        const accessToken  = this.getState(\"tokenResponse.access_token\");\n        const refreshToken = this.getState(\"tokenResponse.refresh_token\");\n        const expiresAt    = this.state.expiresAt || 0;\n\n        if (accessToken && refreshToken && expiresAt - 10 < Date.now() / 1000) {\n            return this.refresh(requestOptions);\n        }\n\n        return Promise.resolve(this.state);\n    }\n\n    /**\n     * Use the refresh token to obtain new access token. If the refresh token is\n     * expired (or this fails for any other reason) it will be deleted from the\n     * state, so that we don't enter into loops trying to re-authorize.\n     *\n     * This method is typically called internally from [[request]] if\n     * certain request fails with 401.\n     *\n     * @param requestOptions Any options to pass to the fetch call. Most of them\n     * will be overridden, bit it might still be useful for passing additional\n     * request options or an abort signal.\n     * @category Request\n     */\n    refresh(requestOptions: RequestInit = {}): Promise<fhirclient.ClientState>\n    {\n        const debugRefresh = _debug.extend(\"client:refresh\");\n        debugRefresh(\"Attempting to refresh with refresh_token...\");\n\n        const refreshToken = this.state?.tokenResponse?.refresh_token;\n        assert(refreshToken, \"Unable to refresh. No refresh_token found.\");\n\n        const tokenUri = this.state.tokenUri;\n        assert(tokenUri, \"Unable to refresh. No tokenUri found.\");\n\n        const scopes = this.getState(\"tokenResponse.scope\") || \"\";\n        const hasOfflineAccess = scopes.search(/\\boffline_access\\b/) > -1;\n        const hasOnlineAccess = scopes.search(/\\bonline_access\\b/) > -1;\n        assert(hasOfflineAccess || hasOnlineAccess, \"Unable to refresh. No offline_access or online_access scope found.\");\n\n        // This method is typically called internally from `request` if certain\n        // request fails with 401. However, clients will often run multiple\n        // requests in parallel which may result in multiple refresh calls.\n        // To avoid that, we keep a reference to the current refresh task (if any).\n        if (!this._refreshTask) {\n            let body = `grant_type=refresh_token&refresh_token=${encodeURIComponent(refreshToken)}`;\n            if (this.environment.options.refreshTokenWithClientId) {\n                body += `&client_id=${this.state.clientId}`;\n            }\n            const refreshRequestOptions = {\n                credentials: this.environment.options.refreshTokenWithCredentials || \"same-origin\",\n                ...requestOptions,\n                method : \"POST\",\n                mode   : \"cors\" as RequestMode,\n                headers: {\n                    ...(requestOptions.headers || {}),\n                    \"content-type\": \"application/x-www-form-urlencoded\"\n                },\n                body: body\n            };\n\n            // custom authorization header can be passed on manual calls\n            if (!(\"authorization\" in refreshRequestOptions.headers)) {\n                const { clientSecret, clientId } = this.state;\n                if (clientSecret) {\n                    // @ts-ignore\n                    refreshRequestOptions.headers.authorization = \"Basic \" + this.environment.btoa(\n                        clientId + \":\" + clientSecret\n                    );\n                }\n            }\n\n            this._refreshTask = request<fhirclient.TokenResponse>(tokenUri, refreshRequestOptions)\n            .then(data => {\n                assert(data.access_token, \"No access token received\");\n                debugRefresh(\"Received new access token response %O\", data);\n                this.state.tokenResponse = { ...this.state.tokenResponse, ...data };\n                this.state.expiresAt = getAccessTokenExpiration(data, this.environment);\n                return this.state;\n            })\n            .catch((error: Error) => {\n                if (this.state?.tokenResponse?.refresh_token) {\n                    debugRefresh(\"Deleting the expired or invalid refresh token.\");\n                    delete this.state.tokenResponse.refresh_token;\n                }\n                throw error;\n            })\n            .finally(() => {\n                this._refreshTask = null;\n                const key = this.state.key;\n                if (key) {\n                    this.environment.getStorage().set(key, this.state);\n                } else {\n                    debugRefresh(\"No 'key' found in Clint.state. Cannot persist the instance.\");\n                }\n            });\n        }\n\n        return this._refreshTask;\n    }\n\n    // utils -------------------------------------------------------------------\n\n    /**\n     * Groups the observations by code. Returns a map that will look like:\n     * ```js\n     * const map = client.byCodes(observations, \"code\");\n     * // map = {\n     * //     \"55284-4\": [ observation1, observation2 ],\n     * //     \"6082-2\": [ observation3 ]\n     * // }\n     * ```\n     * @param observations Array of observations\n     * @param property The name of a CodeableConcept property to group by\n     * @todo This should be deprecated and moved elsewhere. One should not have\n     * to obtain an instance of [[Client]] just to use utility functions like this.\n     * @deprecated\n     * @category Utility\n     */\n    byCode(\n        observations: fhirclient.FHIR.Observation | fhirclient.FHIR.Observation[],\n        property: string\n    ): fhirclient.ObservationMap\n    {\n        return byCode(observations, property);\n    }\n\n    /**\n     * First groups the observations by code using `byCode`. Then returns a function\n     * that accepts codes as arguments and will return a flat array of observations\n     * having that codes. Example:\n     * ```js\n     * const filter = client.byCodes(observations, \"category\");\n     * filter(\"laboratory\") // => [ observation1, observation2 ]\n     * filter(\"vital-signs\") // => [ observation3 ]\n     * filter(\"laboratory\", \"vital-signs\") // => [ observation1, observation2, observation3 ]\n     * ```\n     * @param observations Array of observations\n     * @param property The name of a CodeableConcept property to group by\n     * @todo This should be deprecated and moved elsewhere. One should not have\n     * to obtain an instance of [[Client]] just to use utility functions like this.\n     * @deprecated\n     * @category Utility\n     */\n    byCodes(\n        observations: fhirclient.FHIR.Observation | fhirclient.FHIR.Observation[],\n        property: string\n    ): (...codes: string[]) => any[]\n    {\n        return byCodes(observations, property);\n    }\n\n    /**\n     * @category Utility\n     */\n    units = units;\n\n    /**\n     * Walks through an object (or array) and returns the value found at the\n     * provided path. This function is very simple so it intentionally does not\n     * support any argument polymorphism, meaning that the path can only be a\n     * dot-separated string. If the path is invalid returns undefined.\n     * @param obj The object (or Array) to walk through\n     * @param path The path (eg. \"a.b.4.c\")\n     * @returns {*} Whatever is found in the path or undefined\n     * @todo This should be deprecated and moved elsewhere. One should not have\n     * to obtain an instance of [[Client]] just to use utility functions like this.\n     * @deprecated\n     * @category Utility\n     */\n    getPath(obj: Record<string, any>, path = \"\"): any {\n        return getPath(obj, path);\n    }\n\n    /**\n     * Returns a copy of the client state. Accepts a dot-separated path argument\n     * (same as for `getPath`) to allow for selecting specific properties.\n     * Examples:\n     * ```js\n     * client.getState(); // -> the entire state object\n     * client.getState(\"serverUrl\"); // -> the URL we are connected to\n     * client.getState(\"tokenResponse.patient\"); // -> The selected patient ID (if any)\n     * ```\n     * @param path The path (eg. \"a.b.4.c\")\n     * @returns {*} Whatever is found in the path or undefined\n     */\n    getState(path = \"\") {\n        return getPath({ ...this.state }, path);\n    }\n\n}\n", "import type { B<PERSON>le, BundleLink, Resource } from 'fhir/r4'\nimport { fhirVersions }                      from './settings'\nimport { fhirclient }                        from './types'\nimport {\n    absolute,\n    debug as _debug,\n    getPath,\n    setPath,\n    makeArray,\n    request,\n    fetchConformanceStatement,\n    assertJsonPatch,\n    assert\n} from \"./lib\";\n\n\nconst debug = _debug.extend(\"FhirClient\");\n\ninterface RequestOptions extends RequestInit {\n    /**\n     * If the `includeResponse` option is `true` we can expect a\n     * `CombinedFetchResult` where the `response` property is the `Response`\n     * object and the `body` property is the parsed body.\n     */\n    includeResponse?: boolean;\n\n    /**\n     * Sets a limit if applicable. For example, we can control how many pages to\n     * or resources to fetch\n     */\n    limit?: number\n\n    /**\n     * An object where keys are URLs (or other unique strings) and values are\n     * the request results. If provided, it will be used as in-memory cache.\n     * Otherwise no cache will be used, but you can still use the cache option\n     * for fetch requests if using this in browsers.\n     */\n    cacheMap?: Record<string, any>\n}\n\n/**\n * This is a basic FHIR client for making basic FHIR API calls\n */\nexport default class FhirClient\n{\n    /**\n     * The state of the client instance is an object with various properties.\n     * It contains some details about how the client has been authorized and\n     * determines the behavior of the client instance. This state is persisted\n     * in `SessionStorage` in browsers or in request session on the servers.\n     */\n    readonly fhirBaseUrl: string;\n\n    /**\n     * Validates the parameters, creates an instance and tries to connect it to\n     * FhirJS, if one is available globally.\n     */\n    constructor(fhirBaseUrl: string)\n    {\n        assert(\n            fhirBaseUrl && typeof fhirBaseUrl === \"string\" && fhirBaseUrl.match(/https?:\\/\\/.+/),\n            \"A \\\"fhirBaseUrl\\\" string parameter is required and must begin with \\\"http(s)\\\"\"\n        );\n        this.fhirBaseUrl = fhirBaseUrl;\n    }\n\n    /**\n     * Creates a new resource in a server-assigned location\n     * @see http://hl7.org/fhir/http.html#create\n     * @param resource A FHIR resource to be created\n     * @param [requestOptions] Any options to be passed to the fetch call.\n     * Note that `method` and `body` will be ignored.\n     * @category Request\n     */\n    async create<R = fhirclient.FHIR.Resource, O extends fhirclient.FetchOptions = {}>(\n        resource: fhirclient.FHIR.Resource,\n        requestOptions?: O\n    ): Promise<O[\"includeResponse\"] extends true ? fhirclient.CombinedFetchResult<R> : R>\n    {\n        return this.fhirRequest(resource.resourceType!, {\n            ...requestOptions,\n            method: \"POST\",\n            body: JSON.stringify(resource),\n            headers: {\n                \"content-type\": \"application/json\",\n                ...(requestOptions || {}).headers\n            }\n        });\n    }\n\n    /**\n     * Creates a new current version for an existing resource or creates an\n     * initial version if no resource already exists for the given id.\n     * @see http://hl7.org/fhir/http.html#update\n     * @param resource A FHIR resource to be updated\n     * @param requestOptions Any options to be passed to the fetch call.\n     * Note that `method` and `body` will be ignored.\n     * @category Request\n     */\n    async update<R = fhirclient.FHIR.Resource, O extends fhirclient.FetchOptions = {}>(\n        resource: fhirclient.FHIR.Resource,\n        requestOptions?: O\n    ): Promise<O[\"includeResponse\"] extends true ? fhirclient.CombinedFetchResult<R> : R>\n    {\n        return this.fhirRequest(`${resource.resourceType}/${resource.id}`, {\n            ...requestOptions,\n            method: \"PUT\",\n            body: JSON.stringify(resource),\n            headers: {\n                \"content-type\": \"application/json\",\n                ...(requestOptions || {}).headers\n            }\n        });\n    }\n\n    /**\n     * Removes an existing resource.\n     * @see http://hl7.org/fhir/http.html#delete\n     * @param url Relative URI of the FHIR resource to be deleted\n     * (format: `resourceType/id`)\n     * @param requestOptions Any options (except `method` which will be fixed\n     * to `DELETE`) to be passed to the fetch call.\n     * @category Request\n     */\n    async delete<R = unknown>(url: string, requestOptions: fhirclient.FetchOptions = {}): Promise<R>\n    {\n        return this.fhirRequest<R>(url, { ...requestOptions, method: \"DELETE\" });\n    }\n\n    /**\n     * Makes a JSON Patch to the given resource\n     * @see http://hl7.org/fhir/http.html#patch\n     * @param url Relative URI of the FHIR resource to be patched\n     * (format: `resourceType/id`)\n     * @param patch A JSON Patch array to send to the server, For details\n     * see https://datatracker.ietf.org/doc/html/rfc6902\n     * @param requestOptions Any options to be passed to the fetch call,\n     * except for `method`, `url` and `body` which cannot be overridden.\n     * @since 2.4.0\n     * @category Request\n     * @typeParam ResolveType This method would typically resolve with the\n     * patched resource or reject with an OperationOutcome. However, this may\n     * depend on the server implementation or even on the request headers.\n     * For that reason, if the default resolve type (which is\n     * [[fhirclient.FHIR.Resource]]) does not work for you, you can pass\n     * in your own resolve type parameter.\n     */\n    async patch<ResolveType=fhirclient.FHIR.Resource>(\n        url: string,\n        patch: fhirclient.JsonPatch,\n        requestOptions: fhirclient.FetchOptions = {}\n    ): Promise<ResolveType>\n    {\n        assertJsonPatch(patch);\n        return this.fhirRequest<ResolveType>(url, {\n            ...requestOptions,\n            method: \"PATCH\",\n            body: JSON.stringify(patch),\n            headers: {\n                \"prefer\": \"return=presentation\",\n                \"content-type\": \"application/json-patch+json; charset=UTF-8\",\n                ...requestOptions.headers,\n            }\n        });\n    }\n\n    private async resolveRef(\n        obj  : Resource,\n        path : string,\n        graph: boolean,\n        cache: Record<string, any>,\n        requestOptions: Omit<fhirclient.RequestOptions, \"url\"> = {}\n    ) {\n        const node = getPath(obj, path);\n        if (node) {\n            const isArray = Array.isArray(node);\n            return Promise.all(makeArray(node).filter(Boolean).map((item, i) => {\n                const ref = item.reference;\n                if (ref) {\n                    return this.fhirRequest(ref, { ...requestOptions, includeResponse: false, cacheMap: cache }).then(sub => {\n                        if (graph) {\n                            if (isArray) {\n                                if (path.indexOf(\"..\") > -1) {\n                                    setPath(obj, `${path.replace(\"..\", `.${i}.`)}`, sub);    \n                                } else {\n                                    setPath(obj, `${path}.${i}`, sub);\n                                }\n                            } else {\n                                setPath(obj, path, sub);\n                            }\n                        }\n                    }).catch((ex) => {\n                        if (ex?.status === 404) {\n                            console.warn(`Missing reference ${ref}. ${ex}`)\n                        } else {\n                            throw ex;\n                        }\n                    });\n                }\n            }));\n        }\n    }\n\n    /**\n     * Fetches all references in the given resource, ignoring duplicates, and\n     * then modifies the resource by \"mounting\" the resolved references in place\n     */\n    async resolveReferences(\n        resource: Resource,\n        references: string[],\n        requestOptions: Omit<fhirclient.RequestOptions, \"url\"> = {}\n    ): Promise<void> {\n        await this.fetchReferences(resource, references, true, {}, requestOptions)\n    }\n\n    protected async fetchReferences(\n        resource: Resource,\n        references: string[],\n        graph: boolean,\n        cache: Record<string, any> = {},\n        requestOptions: Omit<fhirclient.RequestOptions, \"url\"> = {}\n    ): Promise<Record<string, any>> {\n\n        if (resource.resourceType == \"Bundle\") {\n            for (const item of ((resource as Bundle).entry || [])) {\n                if (item.resource) {\n                    await this.fetchReferences(item.resource, references, graph, cache, requestOptions)\n                }\n            }\n            return cache\n        }\n        \n        // 1. Sanitize paths, remove any invalid ones\n        let paths = references.map(path => String(path).trim()).filter(Boolean);\n\n        // 2. Remove duplicates\n        paths = paths.reduce((prev, cur) => {\n            if (prev.includes(cur)) {\n                debug(\"Duplicated reference path \\\"%s\\\"\", cur);\n            } else {\n                prev.push(cur)\n            }\n            return prev\n        }, [] as string[]);\n\n        // 3. Early exit if no valid paths are found\n        if (!paths.length) {\n            return Promise.resolve(cache);\n        }\n\n        // 4. Group the paths by depth so that child refs are looked up\n        // after their parents!\n        const groups: Record<string, any> = {};\n        paths.forEach(path => {\n            const len = path.split(\".\").length;\n            if (!groups[len]) {\n                groups[len] = [];\n            }\n            groups[len].push(path);\n        });\n\n        // 5. Execute groups sequentially! Paths within same group are\n        // fetched in parallel!\n        let task: Promise<any> = Promise.resolve();\n        Object.keys(groups).sort().forEach(len => {\n            const group = groups[len];\n            task = task.then(() => Promise.all(group.map((path: string) => {\n                return this.resolveRef(resource, path, graph, cache, requestOptions);\n            })));\n        });\n        await task;\n        return cache\n    }\n\n    /**\n     * Fetches all references in the given resource, ignoring duplicates\n     */\n    async getReferences(\n        resource: Resource,\n        references: string[],\n        requestOptions: Omit<fhirclient.RequestOptions, \"url\"> = {}\n    ): Promise<Record<string, Resource>> {\n        const refs = await this.fetchReferences(resource, references, false, {}, requestOptions)\n        const out: any = {}\n        for (const key in refs) {\n            out[key] = await refs[key]\n        }\n        return out\n    }\n\n    /**\n     * Given a FHIR Bundle or a URL pointing to a bundle, iterates over all\n     * entry resources. Note that this will also automatically crawl through\n     * further pages (if any)\n     */\n    async *resources(bundleOrUrl: Bundle | string | URL, options?: RequestOptions) {\n        let count = 0\n        for await(const page of this.pages(bundleOrUrl, options)) {\n            for (const entry of (page.entry || [])) {\n                if (options?.limit && ++count > options.limit) {\n                    return\n                }\n                yield entry.resource\n            }\n        }\n    }\n\n    /**\n     * Given a FHIR Bundle or a URL pointing to a bundle, iterates over all\n     * pages. Note that this will automatically crawl through\n     * further pages (if any) but it will not detect previous pages. It is\n     * designed to be called on the first page and fetch any followup pages.\n     */\n    async *pages(\n        bundleOrUrl    : Bundle | string | URL,\n        requestOptions?: RequestOptions\n    ) {\n        const { limit, ...options } = requestOptions || {}\n        \n        const fetchPage = (url: string | URL) => this.fhirRequest(url, options)\n\n        let page: Bundle = typeof bundleOrUrl === \"string\" || bundleOrUrl instanceof URL ?\n            await fetchPage(bundleOrUrl) :\n            bundleOrUrl;\n\n        let count = 0\n  \n        while (page && page.resourceType === \"Bundle\" && (!limit || ++count <= limit)) {\n            \n            // Yield the current page\n            yield page;\n        \n            // If caller aborted, stop crawling\n            if (options?.signal?.aborted) {\n                break;\n            }\n        \n            // Find the \"next\" link\n            const nextLink = (page.link ?? []).find(\n                (l: BundleLink) => l.relation === 'next' && typeof l.url === 'string'\n            );\n        \n            if (!nextLink) {\n                break; // no more pages\n            }\n    \n            // Fetch the next page\n            page = await fetchPage(nextLink.url!);\n        }\n    }\n\n    /**\n     * The method responsible for making all http requests\n     */\n    async fhirRequest<T = any>(uri: string | URL, options: RequestOptions = {}): Promise<T>\n    {\n        assert(options, \"fhirRequest requires a uri as first argument\");\n\n        const path = uri + \"\"\n        const url  = absolute(path, this.fhirBaseUrl);\n        const { cacheMap } = options\n\n        if (cacheMap) {\n            if (!(path in cacheMap)) {\n                cacheMap[path] = request<T>(url, options)\n                .then(res => {\n                    cacheMap[path] = res;\n                    return res;\n                })\n                .catch(error => {\n                    delete cacheMap[path];\n                    throw error;\n                });\n            }\n            return cacheMap[path];\n        }\n        return request<T>(url, options)\n    }\n\n    /**\n     * Returns a promise that will be resolved with the fhir version as defined\n     * in the CapabilityStatement.\n     */\n    async getFhirVersion(): Promise<string>\n    {\n        return fetchConformanceStatement(this.fhirBaseUrl)\n            .then((metadata) => metadata.fhirVersion);\n    }\n\n    /**\n     * Returns a promise that will be resolved with the numeric fhir version\n     * - 2 for DSTU2\n     * - 3 for STU3\n     * - 4 for R4\n     * - 0 if the version is not known\n     */\n    async getFhirRelease(): Promise<number>\n    {\n        return this.getFhirVersion().then(v => (fhirVersions as any)[v] ?? 0);\n    }\n}\n", "import { fhirclient } from \"./types\";\n\n\nexport default class HttpError extends <PERSON>rror\n{\n    /**\n     * The HTTP status code for this error\n     */\n    statusCode: number;\n\n    /**\n     * The HTTP status code for this error.\n     * Note that this is the same as `status`, i.e. the code is available\n     * through any of these.\n     */\n    status: number;\n\n    /**\n     * The HTTP status text corresponding to this error\n     */\n    statusText: string;\n\n    /**\n     * Reference to the HTTP Response object\n     */\n    response: Response;\n\n    constructor(response: Response) {\n        super(`${response.status} ${response.statusText}\\nURL: ${response.url}`);\n        this.name       = \"HttpError\";\n        this.response   = response;\n        this.statusCode = response.status;\n        this.status     = response.status;\n        this.statusText = response.statusText;\n    }\n\n    async parse()\n    {\n        if (!this.response.bodyUsed) {\n            try {\n                const type = this.response.headers.get(\"content-type\") || \"text/plain\";\n                if (type.match(/\\bjson\\b/i)) {\n                    let body = await this.response.json();\n                    if (body.error) {\n                        this.message += \"\\n\" + body.error;\n                        if (body.error_description) {\n                            this.message += \": \" + body.error_description;\n                        }\n                    }\n                    else {\n                        this.message += \"\\n\\n\" + JSON.stringify(body, null, 4);\n                    }\n                }\n                else if (type.match(/^text\\//i)) {\n                    let body = await this.response.text();\n                    if (body) {\n                        this.message += \"\\n\\n\" + body;\n                    }\n                }\n            } catch {\n                // ignore\n            }\n        }\n\n        return this;\n    }\n\n    toJSON() {\n        return {\n            name      : this.name,\n            statusCode: this.statusCode,\n            status    : this.status,\n            statusText: this.statusText,\n            message   : this.message\n        };\n    }\n}\n", "import { ready, authorize, init } from \"../smart\";\nimport Client from \"../Client\";\nimport BrowserStorage from \"../storage/BrowserStorage\";\nimport { fhirclient } from \"../types\";\nimport * as security from \"../security/browser\"\nimport { encodeURL, decode, fromUint8Array } from \"js-base64\"\n\n/**\n * Browser Adapter\n */\nexport default class BrowserAdapter implements fhirclient.Adapter\n{\n    /**\n     * Stores the URL instance associated with this adapter\n     */\n    private _url: URL | null = null;\n\n    /**\n     * Holds the Storage instance associated with this instance\n     */\n    private _storage: fhirclient.Storage | null = null;\n\n    /**\n     * Environment-specific options\n     */\n    options: fhirclient.BrowserFHIRSettings;\n\n    security = security;\n\n    /**\n     * @param options Environment-specific options\n     */\n    constructor(options: fhirclient.BrowserFHIRSettings = {})\n    {\n        this.options = {\n            // Replaces the browser's current URL\n            // using window.history.replaceState API or by reloading.\n            replaceBrowserHistory: true,\n\n            // When set to true, this variable will fully utilize\n            // HTML5 sessionStorage API.\n            // This variable can be overridden to false by setting\n            // FHIR.oauth2.settings.fullSessionStorageSupport = false.\n            // When set to false, the sessionStorage will be keyed\n            // by a state variable. This is to allow the embedded IE browser\n            // instances instantiated on a single thread to continue to\n            // function without having sessionStorage data shared\n            // across the embedded IE instances.\n            fullSessionStorageSupport: true,\n\n            // Do we want to send cookies while making a request to the token\n            // endpoint in order to obtain new access token using existing\n            // refresh token. In rare cases the auth server might require the\n            // client to send cookies along with those requests. In this case\n            // developers will have to change this before initializing the app\n            // like so:\n            // `FHIR.oauth2.settings.refreshTokenWithCredentials = \"include\";`\n            // or\n            // `FHIR.oauth2.settings.refreshTokenWithCredentials = \"same-origin\";`\n            // Can be one of:\n            // \"include\"     - always send cookies\n            // \"same-origin\" - only send cookies if we are on the same domain (default)\n            // \"omit\"        - do not send cookies\n            refreshTokenWithCredentials: \"same-origin\",\n\n            ...options\n        };\n    }\n\n    /**\n     * Given a relative path, returns an absolute url using the instance base URL\n     */\n    relative(path: string): string\n    {\n        return new URL(path, this.getUrl().href).href;\n    }\n\n    /**\n     * In browsers we need to be able to (dynamically) check if fhir.js is\n     * included in the page. If it is, it should have created a \"fhir\" variable\n     * in the global scope.\n     */\n    get fhir()\n    {\n        // @ts-ignore\n        return typeof fhir === \"function\" ? fhir : null;\n    }\n\n    /**\n     * Given the current environment, this method must return the current url\n     * as URL instance\n     */\n    getUrl(): URL\n    {\n        if (!this._url) {\n            this._url = new URL(location + \"\");\n        }\n        return this._url;\n    }\n\n    /**\n     * Given the current environment, this method must redirect to the given\n     * path\n     */\n    redirect(to: string): void\n    {\n        location.href = to;\n    }\n\n    /**\n     * Returns a BrowserStorage object which is just a wrapper around\n     * sessionStorage\n     */\n    getStorage(): BrowserStorage\n    {\n        if (!this._storage) {\n            this._storage = new BrowserStorage();\n        }\n        return this._storage;\n    }\n\n    /**\n     * Returns a reference to the AbortController constructor. In browsers,\n     * AbortController will always be available as global (native or polyfilled)\n     */\n    getAbortController()\n    {\n        return AbortController;\n    }\n\n    /**\n     * ASCII string to Base64\n     */\n    atob(str: string): string\n    {\n        return window.atob(str);\n    }\n\n    /**\n     * Base64 to ASCII string\n     */\n    btoa(str: string): string\n    {\n        return window.btoa(str);\n    }\n\n    base64urlencode(input: string | Uint8Array)\n    {\n        if (typeof input == \"string\") {\n            return encodeURL(input)\n        }\n        return fromUint8Array(input, true)\n    }\n\n    base64urldecode(input: string)\n    {\n        return decode(input)\n    }\n\n    /**\n     * Creates and returns adapter-aware SMART api. Not that while the shape of\n     * the returned object is well known, the arguments to this function are not.\n     * Those who override this method are free to require any environment-specific\n     * arguments. For example in node we will need a request, a response and\n     * optionally a storage or storage factory function.\n     */\n    getSmartApi(): fhirclient.SMART\n    {\n        return {\n            ready    : (...args: any[]) => ready(this, ...args),\n            authorize: options => authorize(this, options),\n            init     : options => init(this, options),\n            client   : (state: string | fhirclient.ClientState) => new Client(this, state),\n            options  : this.options,\n            utils: {\n                security\n            }\n        };\n    }\n}\n", "\n// Note: the following 2 imports appear as unused but they affect how tsc is\n// generating type definitions!\nimport { fhirclient } from \"../types\";\nimport Client from \"../Client\";\n\n// In Browsers we create an adapter, get the SMART api from it and build the\n// global FHIR object\nimport BrowserAdapter from \"../adapters/BrowserAdapter\";\nimport FhirClient from \"../FhirClient\";\n\nconst adapter = new BrowserAdapter();\nconst { ready, authorize, init, client, options, utils } = adapter.getSmartApi();\n\n// We have two kinds of browser builds - \"pure\" for new browsers and \"legacy\"\n// for old ones. In pure builds we assume that the browser supports everything\n// we need. In legacy mode, the library also acts as a polyfill. Babel will\n// automatically polyfill everything except \"fetch\", which we have to handle\n// manually.\n// @ts-ignore\nif (typeof FHIRCLIENT_PURE == \"undefined\") {\n    const fetch = require(\"cross-fetch\");\n    require(\"abortcontroller-polyfill/dist/abortcontroller-polyfill-only\");\n    if (!window.fetch) {\n        window.fetch    = fetch.default;\n        window.Headers  = fetch.Headers;\n        window.Request  = fetch.Request;\n        window.Response = fetch.Response;\n    }\n}\n\n// $lab:coverage:off$\nconst FHIR = {\n    AbortController: window.AbortController,\n\n    client,\n\n    /**\n     * Using this class if you are connecting to open server that does not\n     * require authorization.\n     */\n    FhirClient,\n\n    utils,\n    oauth2: {\n        settings: options,\n        ready,\n        authorize,\n        init\n    }\n};\n\nexport = FHIR;\n// $lab:coverage:on$\n", "/*\n * This file contains some shared functions. They are used by other modules, but\n * are defined here so that tests can import this library and test them.\n */\n\nimport HttpError from \"./HttpError\";\nimport { patientParams } from \"./settings\";\nimport { fhirclient } from \"./types\";\nconst debug = require(\"debug\");\n\n// $lab:coverage:off$\n// @ts-ignore\nconst { fetch } = typeof FHIRCLIENT_PURE !== \"undefined\" ? window : require(\"cross-fetch\");\n// $lab:coverage:on$\n\nconst _debug     = debug(\"FHIR\");\nexport { _debug as debug };\n\n/**\n * The cache for the `getAndCache` function\n */\nconst cache: Record<string, any> = {};\n\n/**\n * A namespace with functions for converting between different measurement units\n */\nexport const units = {\n    cm({ code, value }: fhirclient.CodeValue) {\n        ensureNumerical({ code, value });\n        if (code == \"cm\"     ) return value;\n        if (code == \"m\"      ) return value *   100;\n        if (code == \"in\"     ) return value *  2.54;\n        if (code == \"[in_us]\") return value *  2.54;\n        if (code == \"[in_i]\" ) return value *  2.54;\n        if (code == \"ft\"     ) return value * 30.48;\n        if (code == \"[ft_us]\") return value * 30.48;\n        throw new Error(\"Unrecognized length unit: \" + code);\n    },\n    kg({ code, value }: fhirclient.CodeValue){\n        ensureNumerical({ code, value });\n        if (code == \"kg\"    ) return value;\n        if (code == \"g\"     ) return value / 1000;\n        if (code.match(/lb/)) return value / 2.20462;\n        if (code.match(/oz/)) return value / 35.274;\n        throw new Error(\"Unrecognized weight unit: \" + code);\n    },\n    any(pq: fhirclient.CodeValue){\n        ensureNumerical(pq);\n        return pq.value;\n    }\n};\n\n/**\n * Assertion function to guard arguments for `units` functions\n */\nfunction ensureNumerical({ value, code }: fhirclient.CodeValue) {\n    if (typeof value !== \"number\") {\n        throw new Error(\"Found a non-numerical unit: \" + value + \" \" + code);\n    }\n}\n\n/**\n * Used in fetch Promise chains to reject if the \"ok\" property is not true\n */\nexport async function checkResponse(resp: Response): Promise<Response> {\n    if (!resp.ok) {\n        const error = new HttpError(resp);\n        await error.parse();\n        throw error;\n    }\n    return resp;\n}\n\n/**\n * Used in fetch Promise chains to return the JSON version of the response.\n * Note that `resp.json()` will throw on empty body so we use resp.text()\n * instead.\n */\nexport function responseToJSON(resp: Response): Promise<object|string> {\n    return resp.text().then(text => text.length ? JSON.parse(text) : \"\");\n}\n\nexport function loweCaseKeys<T=Record<string, any> | any[] | undefined>(obj: T): T {\n    \n    // Can be undefined to signal that this key should be removed\n    if (!obj) {\n        return obj as T\n    }\n\n    // Arrays are valid values in case of recursive calls\n    if (Array.isArray(obj)) {\n        return obj.map(v => v && typeof v === \"object\" ? loweCaseKeys(v) : v) as unknown as T;\n    }\n\n    // Plain object\n    let out: Record<string, any> = {};\n    Object.keys(obj).forEach(key => {\n        const lowerKey = key.toLowerCase()\n        const v = (obj as Record<string, any>)[key]\n        out[lowerKey] = v && typeof v == \"object\" ? loweCaseKeys(v) : v;\n    });\n    return out as T;\n}\n\n/**\n * This is our built-in request function. It does a few things by default\n * (unless told otherwise):\n * - Makes CORS requests\n * - Sets accept header to \"application/json\"\n * - Handles errors\n * - If the response is json return the json object\n * - If the response is text return the result text\n * - Otherwise return the response object on which we call stuff like `.blob()`\n */\nexport function request<T = fhirclient.FetchResult>(\n    url: string | Request,\n    requestOptions: fhirclient.FetchOptions = {}\n): Promise<T>\n{\n    const { includeResponse, ...options } = requestOptions;\n    return fetch(url, {\n        mode: \"cors\",\n        ...options,\n        headers: {\n            accept: \"application/json\",\n            ...loweCaseKeys(options.headers)\n        }\n    })\n    .then(checkResponse)\n    .then((res: Response) => {\n        const type = res.headers.get(\"content-type\") + \"\";\n        if (type.match(/\\bjson\\b/i)) {\n            return responseToJSON(res).then(body => ({ res, body }));\n        }\n        if (type.match(/^text\\//i)) {\n            return res.text().then(body => ({ res, body }));\n        }\n        return { res };\n    })\n    .then(({res, body}: {res:Response, body?:fhirclient.JsonObject|string}) => {\n\n        // Some servers will reply after CREATE with json content type but with\n        // empty body. In this case check if a location header is received and\n        // fetch that to use it as the final result.\n        if (!body && res.status == 201) {\n            const location = res.headers.get(\"location\");\n            if (location) {\n                return request(location, { ...options, method: \"GET\", body: null, includeResponse });\n            }\n        }\n\n        if (includeResponse) {\n            return { body, response: res };\n        }\n\n        // For any non-text and non-json response return the Response object.\n        // This to let users decide if they want to call text(), blob() or\n        // something else on it\n        if (body === undefined) {\n            return res;\n        }\n\n        // Otherwise just return the parsed body (can also be \"\" or null)\n        return body;\n    });\n}\n\n/**\n * Makes a request using `fetch` and stores the result in internal memory cache.\n * The cache is cleared when the page is unloaded.\n * @param url The URL to request\n * @param requestOptions Request options\n * @param force If true, reload from source and update the cache, even if it has\n * already been cached.\n */\nexport function getAndCache(url: string, requestOptions?: RequestInit, force: boolean = process.env.NODE_ENV === \"test\"): Promise<any> {\n    if (force || !cache[url]) {\n        cache[url] = request(url, requestOptions);\n        return cache[url];\n    }\n    return Promise.resolve(cache[url]);\n}\n\n/**\n * Fetches the conformance statement from the given base URL.\n * Note that the result is cached in memory (until the page is reloaded in the\n * browser) because it might have to be re-used by the client\n * @param baseUrl The base URL of the FHIR server\n * @param [requestOptions] Any options passed to the fetch call\n */\nexport function fetchConformanceStatement(baseUrl = \"/\", requestOptions?: RequestInit): Promise<fhirclient.FHIR.CapabilityStatement>\n{\n    const url = String(baseUrl).replace(/\\/*$/, \"/\") + \"metadata\";\n    return getAndCache(url, requestOptions).catch((ex: Error) => {\n        throw new Error(\n            `Failed to fetch the conformance statement from \"${url}\". ${ex}`\n        );\n    });\n}\n\n\n/**\n * Walks through an object (or array) and returns the value found at the\n * provided path. This function is very simple so it intentionally does not\n * support any argument polymorphism, meaning that the path can only be a\n * dot-separated string. If the path is invalid returns undefined.\n * @param obj The object (or Array) to walk through\n * @param path The path (eg. \"a.b.4.c\")\n * @returns {*} Whatever is found in the path or undefined\n */\nexport function getPath(obj: Record<string, any>, path = \"\"): any {\n    path = path.trim();\n    if (!path) {\n        return obj;\n    }\n\n    let segments = path.split(\".\");\n    let result = obj;\n\n    while (result && segments.length) {\n        const key = segments.shift();\n        if (!key && Array.isArray(result)) {\n            return result.map(o => getPath(o, segments.join(\".\")));\n        } else {\n            result = result[key as string];\n        }\n    }\n\n    return result;\n}\n\n/**\n * Like getPath, but if the node is found, its value is set to @value\n * @param obj The object (or Array) to walk through\n * @param path The path (eg. \"a.b.4.c\")\n * @param value The value to set\n * @param createEmpty If true, create missing intermediate objects or arrays\n * @returns The modified object\n */\nexport function setPath(obj: Record<string, any>, path: string, value: any, createEmpty = false): Record<string, any> {\n    path.trim().split(\".\").reduce(\n        (out, key, idx, arr) => {\n            if (out && idx === arr.length - 1) {\n                out[key] = value;\n            }\n            else {\n                if (out && out[key] === undefined && createEmpty) {\n                    out[key] = arr[idx + 1].match(/^[0-9]+$/) ? [] : {};\n                }\n                return out ? out[key] : undefined;\n            }\n        },\n        obj\n    );\n    return obj;\n}\n\n/**\n * If the argument is an array returns it as is. Otherwise puts it in an array\n * (`[arg]`) and returns the result\n * @param arg The element to test and possibly convert to array\n * @category Utility\n */\nexport function makeArray<T = any>(arg: any): T[] {\n    if (Array.isArray(arg)) {\n        return arg;\n    }\n    return [arg];\n}\n\n/**\n * Given a path, converts it to absolute url based on the `baseUrl`. If baseUrl\n * is not provided, the result would be a rooted path (one that starts with `/`).\n * @param path The path to convert\n * @param baseUrl The base URL\n */\nexport function absolute(path: string, baseUrl?: string): string\n{\n    if (path.match(/^http/)) return path;\n    if (path.match(/^urn/)) return path;\n    return String(baseUrl || \"\").replace(/\\/+$/, \"\") + \"/\" + path.replace(/^\\/+/, \"\");\n}\n\n/**\n * Generates random strings. By default this returns random 8 characters long\n * alphanumeric strings.\n * @param strLength The length of the output string. Defaults to 8.\n * @param charSet A string containing all the possible characters.\n *     Defaults to all the upper and lower-case letters plus digits.\n * @category Utility\n */\nexport function randomString(\n    strLength = 8,\n    charSet = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\"\n): string\n{\n    const result = [];\n    const len = charSet.length;\n    while (strLength--) {\n        result.push(charSet.charAt(Math.floor(Math.random() * len)));\n    }\n    return result.join(\"\");\n}\n\n/**\n * Decodes a JWT token and returns it's body.\n * @param token The token to read\n * @param env An `Adapter` or any other object that has an `atob` method\n * @category Utility\n */\nexport function jwtDecode(token: string, env: fhirclient.Adapter): Record<string, any> | null\n{\n    const payload = token.split(\".\")[1];\n    return payload ? JSON.parse(env.atob(payload)) : null;\n}\n\n/**\n * Add a supplied number of seconds to the supplied Date, returning\n * an integer number of seconds since the epoch\n * @param secondsAhead How far ahead, in seconds (defaults to 120 seconds)\n * @param from Initial time (defaults to current time)\n */\nexport function getTimeInFuture(secondsAhead: number = 120, from?: Date | number): number {\n    return Math.floor(+(from || new Date()) / 1000 + secondsAhead) \n}\n\n/**\n * Given a token response, computes and returns the expiresAt timestamp.\n * Note that this should only be used immediately after an access token is\n * received, otherwise the computed timestamp will be incorrect.\n * @param tokenResponse \n * @param env \n */\nexport function getAccessTokenExpiration(tokenResponse: fhirclient.TokenResponse, env: fhirclient.Adapter): number\n{\n    const now = Math.floor(Date.now() / 1000);\n\n    // Option 1 - using the expires_in property of the token response\n    if (tokenResponse.expires_in) {\n        return now + tokenResponse.expires_in;\n    }\n\n    // Option 2 - using the exp property of JWT tokens (must not assume JWT!)\n    if (tokenResponse.access_token) {\n        let tokenBody = jwtDecode(tokenResponse.access_token, env);\n        if (tokenBody && tokenBody.exp) {\n            return tokenBody.exp;\n        }\n    }\n\n    // Option 3 - if none of the above worked set this to 5 minutes after now\n    return now + 300;\n}\n\n/**\n * Groups the observations by code. Returns a map that will look like:\n * ```js\n * const map = client.byCodes(observations, \"code\");\n * // map = {\n * //     \"55284-4\": [ observation1, observation2 ],\n * //     \"6082-2\": [ observation3 ]\n * // }\n * ```\n * @param observations Array of observations\n * @param property The name of a CodeableConcept property to group by\n */\nexport function byCode(\n    observations: fhirclient.FHIR.Observation | fhirclient.FHIR.Observation[],\n    property: string\n): fhirclient.ObservationMap\n{\n    const ret: fhirclient.ObservationMap = {};\n\n    function handleCodeableConcept(concept: fhirclient.FHIR.CodeableConcept, observation: fhirclient.FHIR.Observation) {\n        if (concept && Array.isArray(concept.coding)) {\n            concept.coding.forEach(({ code }) => {\n                if (code) {\n                    ret[code] = ret[code] || [] as fhirclient.FHIR.Observation[];\n                    ret[code].push(observation);\n                }\n            });\n        }\n    }\n\n    makeArray(observations).forEach(o => {\n        if (o.resourceType === \"Observation\" && o[property]) {\n            if (Array.isArray(o[property])) {\n                o[property].forEach((concept: fhirclient.FHIR.CodeableConcept) => handleCodeableConcept(concept, o));\n            } else {\n                handleCodeableConcept(o[property], o);\n            }\n        }\n    });\n\n    return ret;\n}\n\n/**\n * First groups the observations by code using `byCode`. Then returns a function\n * that accepts codes as arguments and will return a flat array of observations\n * having that codes. Example:\n * ```js\n * const filter = client.byCodes(observations, \"category\");\n * filter(\"laboratory\") // => [ observation1, observation2 ]\n * filter(\"vital-signs\") // => [ observation3 ]\n * filter(\"laboratory\", \"vital-signs\") // => [ observation1, observation2, observation3 ]\n * ```\n * @param observations Array of observations\n * @param property The name of a CodeableConcept property to group by\n */\nexport function byCodes(\n    observations: fhirclient.FHIR.Observation | fhirclient.FHIR.Observation[],\n    property: string\n): (...codes: string[]) => any[]\n{\n    const bank = byCode(observations, property);\n    return (...codes) => codes\n        .filter(code => (code + \"\") in bank)\n        .reduce(\n            (prev, code) => prev.concat(bank[code + \"\"]),\n            [] as fhirclient.FHIR.Observation[]\n        );\n}\n\n/**\n * Given a conformance statement and a resource type, returns the name of the\n * URL parameter that can be used to scope the resource type by patient ID.\n */\nexport function getPatientParam(conformance: fhirclient.FHIR.CapabilityStatement, resourceType: string): string\n{\n    // Find what resources are supported by this server\n    const resources = getPath(conformance, \"rest.0.resource\") || [];\n\n    // Check if this resource is supported\n    const meta = resources.find((r: any) => r.type === resourceType);\n    if (!meta) {\n        throw new Error(`Resource \"${resourceType}\" is not supported by this FHIR server`);\n    }\n\n    // Check if any search parameters are available for this resource\n    if (!Array.isArray(meta.searchParam)) {\n        throw new Error(`No search parameters supported for \"${resourceType}\" on this FHIR server`);\n    }\n\n    // This is a rare case but could happen in generic workflows\n    if (resourceType == \"Patient\" && meta.searchParam.find((x: any) => x.name == \"_id\")) {\n        return \"_id\";\n    }\n\n    // Now find the first possible parameter name\n    const out = patientParams.find(p => meta.searchParam.find((x: any) => x.name == p));\n\n    // If there is no match\n    if (!out) {\n        throw new Error(\"I don't know what param to use for \" + resourceType);\n    }\n\n    return out;\n}\n\n/**\n * Resolves a reference to target window. It may also open new window or tab if\n * the `target = \"popup\"` or `target = \"_blank\"`.\n * @param target\n * @param width Only used when `target = \"popup\"`\n * @param height Only used when `target = \"popup\"`\n */\nexport async function getTargetWindow(target: fhirclient.WindowTarget, width: number = 800, height: number = 720): Promise<Window>\n{\n    // The target can be a function that returns the target. This can be\n    // used to open a layer pop-up with an iframe and then return a reference\n    // to that iframe (or its name)\n    if (typeof target == \"function\") {\n        target = await target();\n    }\n\n    // The target can be a window reference\n    if (target && typeof target == \"object\") {\n        return target;\n    }\n\n    // At this point target must be a string\n    if (typeof target != \"string\") {\n        _debug(\"Invalid target type '%s'. Failing back to '_self'.\", typeof target);\n        return self;\n    }\n\n    // Current window\n    if (target == \"_self\") {\n        return self;\n    }\n\n    // The parent frame\n    if (target == \"_parent\") {\n        return parent;\n    }\n\n    // The top window\n    if (target == \"_top\") {\n        return top || self;\n    }\n\n    // New tab or window\n    if (target == \"_blank\") {\n        let error, targetWindow: Window | null = null;\n        try {\n            targetWindow = window.open(\"\", \"SMARTAuthPopup\");\n            if (!targetWindow) {\n                throw new Error(\"Perhaps window.open was blocked\");\n            }\n        } catch (e) {\n            error = e;\n        }\n\n        if (!targetWindow) {\n            _debug(\"Cannot open window. Failing back to '_self'. %s\", error);\n            return self;\n        } else {\n            return targetWindow;\n        }\n    }\n\n    // Popup window\n    if (target == \"popup\") {\n        let error, targetWindow: Window | null = null;\n        // if (!targetWindow || targetWindow.closed) {\n        try {\n            targetWindow = window.open(\"\", \"SMARTAuthPopup\", [\n                \"height=\" + height,\n                \"width=\" + width,\n                \"menubar=0\",\n                \"resizable=1\",\n                \"status=0\",\n                \"top=\" + (screen.height - height) / 2,\n                \"left=\" + (screen.width - width) / 2\n            ].join(\",\"));\n            if (!targetWindow) {\n                throw new Error(\"Perhaps the popup window was blocked\");\n            }\n        } catch (e) {\n            error = e;\n        }\n\n        if (!targetWindow) {\n            _debug(\"Cannot open window. Failing back to '_self'. %s\", error);\n            return self;\n        } else {\n            return targetWindow;\n        }\n    }\n\n    // Frame or window by name\n    const winOrFrame: Window = frames[target as any];\n    if (winOrFrame) {\n        return winOrFrame;\n    }\n\n    _debug(\"Unknown target '%s'. Failing back to '_self'.\", target);\n    return self;\n}\n\nexport function assert(condition: any, message: string): asserts condition {\n    if (!(condition)) {\n        throw new Error(message)\n    }\n}\n\nexport function assertJsonPatch(patch: fhirclient.JsonPatch): asserts patch {\n    assert(Array.isArray(patch), \"The JSON patch must be an array\")\n    assert(patch.length > 0, \"The JSON patch array should not be empty\")\n    patch.forEach((operation: fhirclient.JsonPatchOperation) => {\n        assert(\n            [\"add\", \"replace\", \"test\", \"move\", \"copy\", \"remove\"].indexOf(operation.op) > -1,\n            'Each patch operation must have an \"op\" property which must be one of: \"add\", \"replace\", \"test\", \"move\", \"copy\", \"remove\"'\n        )\n        assert(operation.path && typeof operation.path, `Invalid \"${operation.op}\" operation. Missing \"path\" property`)\n        \n        if (operation.op == \"add\" || operation.op == \"replace\" || operation.op == \"test\") {\n            assert(\"value\" in operation, `Invalid \"${operation.op}\" operation. Missing \"value\" property`)\n            assert(Object.keys(operation).length == 3, `Invalid \"${operation.op}\" operation. Contains unknown properties`)\n        }\n\n        else if (operation.op == \"move\" || operation.op == \"copy\") {\n            assert(typeof operation.from == \"string\", `Invalid \"${operation.op}\" operation. Requires a string \"from\" property`)\n            assert(Object.keys(operation).length == 3, `Invalid \"${operation.op}\" operation. Contains unknown properties`)\n        }\n\n        else {\n            assert(Object.keys(operation).length == 2, `Invalid \"${operation.op}\" operation. Contains unknown properties`)\n        }\n    })\n}\n", "import { encodeURL, fromUint8Array } from \"js-base64\"\nimport { fhirclient }                from \"../types\"\n\n\nconst crypto: Crypto = typeof globalThis === \"object\" && globalThis.crypto ?\n    globalThis.crypto :\n    require(\"isomorphic-webcrypto\").default;\n\nconst subtle = () => {\n    if (!crypto.subtle) {\n        if (!globalThis.isSecureContext) {\n            throw new Error(\n                \"Some of the required subtle crypto functionality is not \" +\n                \"available unless you run this app in secure context (using \" +\n                \"HTTPS or running locally). See \" +\n                \"https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts\"\n            )\n        }\n        throw new Error(\n            \"Some of the required subtle crypto functionality is not \" +\n            \"available in the current environment (no crypto.subtle)\"\n        )\n    }\n    return crypto.subtle\n}\n\n\ninterface PkcePair {\n    codeChallenge: string\n    codeVerifier: string\n}\n\nconst ALGS = {\n    ES384: {\n        name: \"ECDSA\",\n        namedCurve: \"P-384\"\n    } as EcKeyGenParams,\n    RS384: {\n        name: \"RSASSA-PKCS1-v1_5\",\n        modulusLength: 4096,\n        publicExponent: new Uint8Array([1, 0, 1]),\n        hash: {\n            name: 'SHA-384'\n        }\n    } as RsaHashedKeyGenParams\n};\n\nexport function randomBytes(count: number): Uint8Array {\n    return crypto.getRandomValues(new Uint8Array(count));\n}\n\nexport async function digestSha256(payload: string): Promise<Uint8Array> {\n    const prepared = new TextEncoder().encode(payload);\n    const hash = await subtle().digest('SHA-256', prepared);\n    return new Uint8Array(hash);\n}\n\nexport const generatePKCEChallenge = async (entropy = 96): Promise<PkcePair> => {\n    const inputBytes    = randomBytes(entropy)\n    const codeVerifier  = fromUint8Array(inputBytes, true)\n    const codeChallenge = fromUint8Array(await digestSha256(codeVerifier), true)\n    return { codeChallenge, codeVerifier }\n}\n\nexport async function importJWK(jwk: fhirclient.JWK): Promise<CryptoKey> {\n    // alg is optional in JWK but we need it here!\n    if (!jwk.alg) {\n        throw new Error('The \"alg\" property of the JWK must be set to \"ES384\" or \"RS384\"')\n    }\n\n    // Use of the \"key_ops\" member is OPTIONAL, unless the application requires its presence.\n    // https://www.rfc-editor.org/rfc/rfc7517.html#section-4.3\n    // \n    // In our case the app will only import private keys so we can assume \"sign\"\n    if (!Array.isArray(jwk.key_ops)) {\n        jwk.key_ops = [\"sign\"]\n    }\n\n    // In this case the JWK has a \"key_ops\" array and \"sign\" is not listed\n    if (!jwk.key_ops.includes(\"sign\")) {\n        throw new Error('The \"key_ops\" property of the JWK does not contain \"sign\"')\n    }\n\n    try {\n        return await subtle().importKey(\n            \"jwk\",\n            jwk,\n            ALGS[jwk.alg],\n            jwk.ext === true,\n            jwk.key_ops// || ['sign']\n        )\n    } catch (e) {\n        throw new Error(`The ${jwk.alg} is not supported by this browser: ${e}`)\n    }\n}\n\nexport async function signCompactJws(alg: keyof typeof ALGS, privateKey: CryptoKey, header: any, payload: any): Promise<string> {\n\n    const jwtHeader  = JSON.stringify({ ...header, alg });\n    const jwtPayload = JSON.stringify(payload);\n    const jwtAuthenticatedContent = `${encodeURL(jwtHeader)}.${encodeURL(jwtPayload)}`;\n\n    const signature = await subtle().sign(\n        { ...privateKey.algorithm, hash: 'SHA-384' },\n        privateKey,\n        new TextEncoder().encode(jwtAuthenticatedContent)\n    );\n\n    return `${jwtAuthenticatedContent}.${fromUint8Array(new Uint8Array(signature), true)}`\n}\n", "/**\n * Combined list of FHIR resource types accepting patient parameter in FHIR R2-R4\n */\nexport const patientCompartment = [\n    \"Account\",\n    \"AdverseEvent\",\n    \"AllergyIntolerance\",\n    \"Appointment\",\n    \"AppointmentResponse\",\n    \"AuditEvent\",\n    \"Basic\",\n    \"BodySite\",\n    \"BodyStructure\",\n    \"CarePlan\",\n    \"CareTeam\",\n    \"ChargeItem\",\n    \"Claim\",\n    \"ClaimResponse\",\n    \"ClinicalImpression\",\n    \"Communication\",\n    \"CommunicationRequest\",\n    \"Composition\",\n    \"Condition\",\n    \"Consent\",\n    \"Coverage\",\n    \"CoverageEligibilityRequest\",\n    \"CoverageEligibilityResponse\",\n    \"DetectedIssue\",\n    \"DeviceRequest\",\n    \"DeviceUseRequest\",\n    \"DeviceUseStatement\",\n    \"DiagnosticOrder\",\n    \"DiagnosticReport\",\n    \"DocumentManifest\",\n    \"DocumentReference\",\n    \"EligibilityRequest\",\n    \"Encounter\",\n    \"EnrollmentRequest\",\n    \"EpisodeOfCare\",\n    \"ExplanationOfBenefit\",\n    \"FamilyMemberHistory\",\n    \"Flag\",\n    \"Goal\",\n    \"Group\",\n    \"ImagingManifest\",\n    \"ImagingObjectSelection\",\n    \"ImagingStudy\",\n    \"Immunization\",\n    \"ImmunizationEvaluation\",\n    \"ImmunizationRecommendation\",\n    \"Invoice\",\n    \"List\",\n    \"MeasureReport\",\n    \"Media\",\n    \"MedicationAdministration\",\n    \"MedicationDispense\",\n    \"MedicationOrder\",\n    \"MedicationRequest\",\n    \"MedicationStatement\",\n    \"MolecularSequence\",\n    \"NutritionOrder\",\n    \"Observation\",\n    \"Order\",\n    \"Patient\",\n    \"Person\",\n    \"Procedure\",\n    \"ProcedureRequest\",\n    \"Provenance\",\n    \"QuestionnaireResponse\",\n    \"ReferralRequest\",\n    \"RelatedPerson\",\n    \"RequestGroup\",\n    \"ResearchSubject\",\n    \"RiskAssessment\",\n    \"Schedule\",\n    \"ServiceRequest\",\n    \"Specimen\",\n    \"SupplyDelivery\",\n    \"SupplyRequest\",\n    \"VisionPrescription\"\n];\n\n/**\n * Map of FHIR releases and their abstract version as number\n */\nexport const fhirVersions = {\n    \"0.4.0\": 2,\n    \"0.5.0\": 2,\n    \"1.0.0\": 2,\n    \"1.0.1\": 2,\n    \"1.0.2\": 2,\n    \"1.1.0\": 3,\n    \"1.4.0\": 3,\n    \"1.6.0\": 3,\n    \"1.8.0\": 3,\n    \"3.0.0\": 3,\n    \"3.0.1\": 3,\n    \"3.3.0\": 4,\n    \"3.5.0\": 4,\n    \"4.0.0\": 4,\n    \"4.0.1\": 4\n};\n\n/**\n * Combined (FHIR R2-R4) list of search parameters that can be used to scope\n * a request by patient ID.\n */\nexport const patientParams = [\n    \"patient\",\n    \"subject\",\n    \"requester\",\n    \"member\",\n    \"actor\",\n    \"beneficiary\"\n];\n\n/**\n * The name of the sessionStorage entry that contains the current key\n */\nexport const SMART_KEY = \"SMART_KEY\";\n", "/* global window */\nimport {\n    debug as _debug,\n    request,\n    getPath,\n    getTimeInFuture,\n    randomString,\n    getAndCache,\n    fetchConformanceStatement,\n    getAccessTokenExpiration,\n    getTargetWindow,\n    assert\n} from \"./lib\";\nimport Client from \"./Client\";\nimport { SMART_KEY } from \"./settings\";\nimport { fhirclient } from \"./types\";\n\nconst debug = _debug.extend(\"oauth2\");\n\nexport { SMART_KEY as KEY };\n\nfunction isBrowser() {\n    return typeof window === \"object\";\n}\n\n/**\n * Fetches the well-known json file from the given base URL.\n * Note that the result is cached in memory (until the page is reloaded in the\n * browser) because it might have to be re-used by the client\n * @param baseUrl The base URL of the FHIR server\n */\nexport function fetchWellKnownJson(baseUrl = \"/\", requestOptions?: RequestInit): Promise<fhirclient.WellKnownSmartConfiguration>\n{\n    const url = String(baseUrl).replace(/\\/*$/, \"/\") + \".well-known/smart-configuration\";\n    return getAndCache(url, requestOptions).catch((ex: Error) => {\n        throw new Error(`Failed to fetch the well-known json \"${url}\". ${ex.message}`);\n    });\n}\n\n/**\n * Fetch a \"WellKnownJson\" and extract the SMART endpoints from it\n */\nfunction getSecurityExtensionsFromWellKnownJson(baseUrl = \"/\", requestOptions?: RequestInit): Promise<fhirclient.OAuthSecurityExtensions>\n{\n    return fetchWellKnownJson(baseUrl, requestOptions).then(meta => {\n        if (!meta.authorization_endpoint || !meta.token_endpoint) {\n            throw new Error(\"Invalid wellKnownJson\");\n        }\n        return {\n            registrationUri     : meta.registration_endpoint  || \"\",\n            authorizeUri        : meta.authorization_endpoint,\n            tokenUri            : meta.token_endpoint,\n            codeChallengeMethods: meta.code_challenge_methods_supported || []\n        };\n    });\n}\n\n/**\n * Fetch a `CapabilityStatement` and extract the SMART endpoints from it\n */\nfunction getSecurityExtensionsFromConformanceStatement(baseUrl = \"/\", requestOptions?: RequestInit): Promise<fhirclient.OAuthSecurityExtensions>\n{\n    return fetchConformanceStatement(baseUrl, requestOptions).then(meta => {\n        const nsUri = \"http://fhir-registry.smarthealthit.org/StructureDefinition/oauth-uris\";\n        const extensions = ((getPath(meta || {}, \"rest.0.security.extension\") || []) as Array<fhirclient.FHIR.Extension<\"valueUri\">>)\n            .filter(e => e.url === nsUri)\n            .map(o => o.extension)[0];\n\n        const out:fhirclient.OAuthSecurityExtensions = {\n            registrationUri     : \"\",\n            authorizeUri        : \"\",\n            tokenUri            : \"\",\n            codeChallengeMethods: [],\n        };\n\n        if (extensions) {\n            extensions.forEach(ext => {\n                if (ext.url === \"register\") {\n                    out.registrationUri = ext.valueUri;\n                }\n                if (ext.url === \"authorize\") {\n                    out.authorizeUri = ext.valueUri;\n                }\n                if (ext.url === \"token\") {\n                    out.tokenUri = ext.valueUri;\n                }\n            });\n        }\n\n        return out;\n    });\n}\n\n\n/**\n * Given a FHIR server, returns an object with it's Oauth security endpoints\n * that we are interested in. This will try to find the info in both the\n * `CapabilityStatement` and the `.well-known/smart-configuration`. Whatever\n * Arrives first will be used and the other request will be aborted.\n * @param [baseUrl = \"/\"] Fhir server base URL\n */\nexport function getSecurityExtensions(baseUrl = \"/\"): Promise<fhirclient.OAuthSecurityExtensions>\n{\n    return getSecurityExtensionsFromWellKnownJson(baseUrl)\n        .catch(() => getSecurityExtensionsFromConformanceStatement(baseUrl));\n}\n\n/**\n * Starts the SMART Launch Sequence.\n * > **IMPORTANT**:\n *   `authorize()` will end up redirecting you to the authorization server.\n *    This means that you should not add anything to the returned promise chain.\n *    Any code written directly after the authorize() call might not be executed\n *    due to that redirect!\n * @param env\n * @param [params]\n */\nexport async function authorize(\n    env: fhirclient.Adapter,\n    params: fhirclient.AuthorizeParams | fhirclient.AuthorizeParams[] = {}\n): Promise<string|void>\n{\n    const url = env.getUrl();\n\n    // Multiple config for EHR launches ---------------------------------------\n    if (Array.isArray(params)) {\n        const urlISS = url.searchParams.get(\"iss\") || url.searchParams.get(\"fhirServiceUrl\");\n        if (!urlISS) {\n            throw new Error(\n                'Passing in an \"iss\" url parameter is required if authorize ' +\n                'uses multiple configurations'\n            );\n        }\n        // pick the right config\n        const cfg = params.find(x => {\n            if (x.issMatch) {\n                if (typeof x.issMatch === \"function\") {\n                    return !!x.issMatch(urlISS);\n                }\n                if (typeof x.issMatch === \"string\") {\n                    return x.issMatch === urlISS;\n                }\n                if (x.issMatch instanceof RegExp) {\n                    return x.issMatch.test(urlISS);\n                }\n            }\n            return false;\n        });\n        assert(cfg, `No configuration found matching the current \"iss\" parameter \"${urlISS}\"`);\n        return await authorize(env, cfg);\n    }\n    // ------------------------------------------------------------------------\n\n    // Obtain input\n    const {\n        clientSecret,\n        fakeTokenResponse,\n        encounterId,\n        target,\n        width,\n        height,\n        pkceMode,\n        clientPublicKeySetUrl,\n        // Two deprecated values to use as fall-back values later\n        redirect_uri,\n        client_id,\n    } = params;\n    \n    let {\n        iss,\n        launch,\n        patientId,\n        fhirServiceUrl,\n        redirectUri,\n        noRedirect,\n        scope = \"\",\n        clientId,\n        completeInTarget,\n        clientPrivateJwk,\n        stateKey\n    } = params;\n\n    const storage = env.getStorage();\n\n    // For these, a url param takes precedence over inline option\n    iss            = url.searchParams.get(\"iss\")            || iss;\n    fhirServiceUrl = url.searchParams.get(\"fhirServiceUrl\") || fhirServiceUrl;\n    launch         = url.searchParams.get(\"launch\")         || launch;\n    patientId      = url.searchParams.get(\"patientId\")      || patientId;\n    clientId       = url.searchParams.get(\"clientId\")       || clientId;\n\n    // If there's still no clientId or redirectUri, check deprecated params \n    if (!clientId) {\n        clientId = client_id;\n    }\n    if (!redirectUri) {\n        redirectUri = redirect_uri;\n    }\n\n    if (!redirectUri) {\n        redirectUri = env.relative(\".\");\n    } else if (!redirectUri.match(/^https?\\:\\/\\//)) {\n        redirectUri = env.relative(redirectUri);\n    }\n\n    const serverUrl = String(iss || fhirServiceUrl || \"\");\n\n    // Validate input\n    if (!serverUrl) {\n        throw new Error(\n            \"No server url found. It must be specified as `iss` or as \" +\n            \"`fhirServiceUrl` parameter\"\n        );\n    }\n\n    if (iss) {\n        debug(\"Making %s launch...\", launch ? \"EHR\" : \"standalone\");\n    }\n\n    // append launch scope if needed\n    if (launch && !scope.match(/launch/)) {\n        scope += \" launch\";\n    }\n\n    if (isBrowser()) {\n        const inFrame = isInFrame();\n        const inPopUp = isInPopUp();\n\n        if ((inFrame || inPopUp) && completeInTarget !== true && completeInTarget !== false) {\n            \n            // completeInTarget will default to true if authorize is called from\n            // within an iframe. This is to avoid issues when the entire app\n            // happens to be rendered in an iframe (including in some EHRs),\n            // even though that was not how the app developer's intention.\n            completeInTarget = inFrame;\n\n            // In this case we can't always make the best decision so ask devs\n            // to be explicit in their configuration.\n            console.warn(\n                'Your app is being authorized from within an iframe or popup ' +\n                'window. Please be explicit and provide a \"completeInTarget\" ' +\n                'option. Use \"true\" to complete the authorization in the '     +\n                'same window, or \"false\" to try to complete it in the parent ' +\n                'or the opener window. See http://docs.smarthealthit.org/client-js/api.html'\n            );\n        }\n    }\n\n    // If `authorize` is called, make sure we clear any previous state (in case\n    // this is a re-authorize)\n    const oldKey = await storage.get(SMART_KEY);\n    await storage.unset(oldKey);\n\n    stateKey = stateKey ?? randomString(16);\n\n    // Create initial state\n    const state: fhirclient.ClientState = {\n        clientId,\n        scope,\n        redirectUri,\n        serverUrl,\n        clientSecret,\n        clientPrivateJwk,\n        tokenResponse: {},\n        key: stateKey,\n        completeInTarget,\n        clientPublicKeySetUrl\n    };\n\n    const fullSessionStorageSupport = isBrowser() ?\n        getPath(env, \"options.fullSessionStorageSupport\") :\n        true;\n\n    if (fullSessionStorageSupport) {\n        await storage.set(SMART_KEY, stateKey);\n    }\n\n    // fakeTokenResponse to override stuff (useful in development)\n    if (fakeTokenResponse) {\n        Object.assign(state.tokenResponse!, fakeTokenResponse);\n    }\n\n    // Fixed patientId (useful in development)\n    if (patientId) {\n        Object.assign(state.tokenResponse!, { patient: patientId });\n    }\n\n    // Fixed encounterId (useful in development)\n    if (encounterId) {\n        Object.assign(state.tokenResponse!, { encounter: encounterId });\n    }\n\n    let redirectUrl = redirectUri + \"?state=\" + encodeURIComponent(stateKey);\n\n    // bypass oauth if fhirServiceUrl is used (but iss takes precedence)\n    if (fhirServiceUrl && !iss) {\n        debug(\"Making fake launch...\");\n        await storage.set(stateKey, state);\n        if (noRedirect) {\n            return redirectUrl;\n        }\n        return await env.redirect(redirectUrl);\n    }\n\n    // Get oauth endpoints and add them to the state\n    const extensions = await getSecurityExtensions(serverUrl);\n    Object.assign(state, extensions);\n    await storage.set(stateKey, state);\n\n    // If this happens to be an open server and there is no authorizeUri\n    if (!state.authorizeUri) {\n        if (noRedirect) {\n            return redirectUrl;\n        }\n        return await env.redirect(redirectUrl);\n    }\n\n    // build the redirect uri\n    const redirectParams = [\n        \"response_type=code\",\n        \"client_id=\"    + encodeURIComponent(clientId || \"\"),\n        \"scope=\"        + encodeURIComponent(scope),\n        \"redirect_uri=\" + encodeURIComponent(redirectUri),\n        \"aud=\"          + encodeURIComponent(serverUrl),\n        \"state=\"        + encodeURIComponent(stateKey)\n    ];\n\n    // also pass this in case of EHR launch\n    if (launch) {\n        redirectParams.push(\"launch=\" + encodeURIComponent(launch));\n    }\n\n    if (shouldIncludeChallenge(extensions.codeChallengeMethods.includes('S256'), pkceMode)) {\n        let codes = await env.security.generatePKCEChallenge()\n        Object.assign(state, codes);\n        await storage.set(stateKey, state);\n        redirectParams.push(\"code_challenge=\" + state.codeChallenge);// note that the challenge is ALREADY encoded properly\n        redirectParams.push(\"code_challenge_method=S256\");\n    }\n  \n    redirectUrl = state.authorizeUri + \"?\" + redirectParams.join(\"&\");\n\n    if (noRedirect) {\n        return redirectUrl;\n    }\n\n    if (target && isBrowser()) {\n        let win: Window;\n\n        win = await getTargetWindow(target, width, height);\n\n        if (win !== self) {\n            try {\n                // Also remove any old state from the target window and then\n                // transfer the current state there\n                win.sessionStorage.removeItem(oldKey);\n                win.sessionStorage.setItem(stateKey, JSON.stringify(state));\n            } catch (ex) {\n                _debug(`Failed to modify window.sessionStorage. Perhaps it is from different origin?. Failing back to \"_self\". %s`, ex);\n                win = self;\n            }\n        }\n\n        if (win !== self) {\n            try {\n                win.location.href = redirectUrl;\n                self.addEventListener(\"message\", onMessage);\n            } catch (ex) {\n                _debug(`Failed to modify window.location. Perhaps it is from different origin?. Failing back to \"_self\". %s`, ex);\n                self.location.href = redirectUrl;\n            }\n        } else {\n            self.location.href = redirectUrl;\n        }\n\n        return;\n    }\n    else {\n        return await env.redirect(redirectUrl);\n    }\n}\n\nfunction shouldIncludeChallenge(S256supported: boolean, pkceMode?: string) {\n    if (pkceMode === \"disabled\") {\n        return false;\n    }\n    if (pkceMode === \"unsafeV1\") {\n        return true;\n    }\n    if (pkceMode === \"required\") {\n        if (!S256supported) {\n            throw new Error(\"Required PKCE code challenge method (`S256`) was not found in the server's codeChallengeMethods declaration.\");\n        }\n        return true;\n    }\n    return S256supported;\n}\n\n/**\n * Checks if called within a frame. Only works in browsers!\n * If the current window has a `parent` or `top` properties that refer to\n * another window, returns true. If trying to access `top` or `parent` throws an\n * error, returns true. Otherwise returns `false`.\n */\nexport function isInFrame() {\n    try {\n        return self !== top && parent !== self;\n    } catch (e) {\n        return true;\n    }\n}\n\n/**\n * Checks if called within another window (popup or tab). Only works in browsers!\n * To consider itself called in a new window, this function verifies that:\n * 1. `self === top` (not in frame)\n * 2. `!!opener && opener !== self` The window has an opener\n * 3. `!!window.name` The window has a `name` set\n */\nexport function isInPopUp() {\n    try {\n        return self === top &&\n               !!opener &&\n               opener !== self &&\n               !!window.name;\n    } catch (e) {\n        return false;\n    }\n}\n\n/**\n * Another window can send a \"completeAuth\" message to this one, making it to\n * navigate to e.data.url\n * @param e The message event\n */\nexport function onMessage(e: MessageEvent) {\n    if (e.data.type == \"completeAuth\" && e.origin === new URL(self.location.href).origin) {\n        window.removeEventListener(\"message\", onMessage);\n        window.location.href = e.data.url;\n    }\n}\n\n/**\n * The ready function should only be called on the page that represents\n * the redirectUri. We typically land there after a redirect from the\n * authorization server, but this code will also be executed upon subsequent\n * navigation or page refresh.\n */\nexport async function ready(env: fhirclient.Adapter, options: fhirclient.ReadyOptions = {}): Promise<Client>\n{\n    const url = env.getUrl();\n    const Storage = env.getStorage();\n    const params = url.searchParams;\n\n    let key                    = params.get(\"state\") || options.stateKey;\n    const code                 = params.get(\"code\")  || options.code;\n    const authError            = params.get(\"error\");\n    const authErrorDescription = params.get(\"error_description\");\n\n    if (!key) {\n        key = await Storage.get(SMART_KEY);\n    }\n\n    // Start by checking the url for `error` and `error_description` parameters.\n    // This happens when the auth server rejects our authorization attempt. In\n    // this case it has no other way to tell us what the error was, other than\n    // appending these parameters to the redirect url.\n    // From client's point of view, this is not very reliable (because we can't\n    // know how we have landed on this page - was it a redirect or was it loaded\n    // manually). However, if `ready()` is being called, we can assume\n    // that the url comes from the auth server (otherwise the app won't work\n    // anyway).\n    if (authError || authErrorDescription) {\n        throw new Error([\n            authError,\n            authErrorDescription\n        ].filter(Boolean).join(\": \"));\n    }\n\n    debug(\"key: %s, code: %s\", key, code);\n\n    // key might be coming from the page url so it might be empty or missing\n    assert(key, \"No 'state' parameter found. Please (re)launch the app.\");\n\n    // Check if we have a previous state\n    let state = (await Storage.get(key)) as fhirclient.ClientState;\n\n    const fullSessionStorageSupport = isBrowser() ?\n        getPath(env, \"options.fullSessionStorageSupport\") :\n        true;\n\n    // If we are in a popup window or an iframe and the authorization is\n    // complete, send the location back to our opener and exit.\n    if (isBrowser() && state && !state.completeInTarget) {\n\n        const inFrame = isInFrame();\n        const inPopUp = isInPopUp();\n\n        // we are about to return to the opener/parent where completeAuth will\n        // be called again. In rare cases the opener or parent might also be\n        // a frame or popup. Then inFrame or inPopUp will be true but we still\n        // have to stop going up the chain. To guard against that weird form of\n        // recursion we pass one additional parameter to the url which we later\n        // remove.\n        if ((inFrame || inPopUp) && !url.searchParams.get(\"complete\")) {\n            url.searchParams.set(\"complete\", \"1\");\n            const { href, origin } = url;\n            if (inFrame) {\n                parent.postMessage({ type: \"completeAuth\", url: href }, origin);\n            }\n            if (inPopUp) {\n                opener.postMessage({ type: \"completeAuth\", url: href }, origin);\n                window.close();\n            }\n\n            return new Promise(() => { /* leave it pending!!! */ });\n        }\n    }\n\n    url.searchParams.delete(\"complete\");\n\n    // Do we have to remove the `code` and `state` params from the URL?\n    const hasState = params.has(\"state\") || options.stateKey ? true : false;\n\n    if (isBrowser() && getPath(env, \"options.replaceBrowserHistory\") && (code || hasState)) {\n        // `code` is the flag that tell us to request an access token.\n        // We have to remove it, otherwise the page will authorize on\n        // every load!\n        if (code) {\n            params.delete(\"code\");\n            debug(\"Removed code parameter from the url.\");\n        }\n\n        // If we have `fullSessionStorageSupport` it means we no longer\n        // need the `state` key. It will be stored to a well know\n        // location - sessionStorage[SMART_KEY]. However, no\n        // fullSessionStorageSupport means that this \"well know location\"\n        // might be shared between windows and tabs. In this case we\n        // MUST keep the `state` url parameter.\n        if (hasState && fullSessionStorageSupport) {\n            params.delete(\"state\");\n            debug(\"Removed state parameter from the url.\");\n        }\n\n        // If the browser does not support the replaceState method for the\n        // History Web API, the \"code\" parameter cannot be removed. As a\n        // consequence, the page will (re)authorize on every load. The\n        // workaround is to reload the page to new location without those\n        // parameters. If that is not acceptable replaceBrowserHistory\n        // should be set to false.\n        if (window.history.replaceState) {\n            window.history.replaceState({}, \"\", url.href);\n        }\n    }\n\n    // If the state does not exist, it means the page has been loaded directly.\n    assert(state, \"No state found! Please (re)launch the app.\");\n\n    // Assume the client has already completed a token exchange when\n    // there is no code (but we have a state) or access token is found in state\n    const authorized = !code || state.tokenResponse?.access_token;\n\n    // If we are authorized already, then this is just a reload.\n    // Otherwise, we have to complete the code flow\n    if (!authorized && state.tokenUri) {\n\n        assert(code, \"'code' url parameter is required\");\n\n        debug(\"Preparing to exchange the code for access token...\");\n        const requestOptions = await buildTokenRequest(env, {\n            code,\n            state,\n            clientPublicKeySetUrl: options.clientPublicKeySetUrl,\n            privateKey: options.privateKey || state.clientPrivateJwk\n        });\n        debug(\"Token request options: %O\", requestOptions);\n\n        // The EHR authorization server SHALL return a JSON structure that\n        // includes an access token or a message indicating that the\n        // authorization request has been denied.\n        const tokenResponse = await request<fhirclient.TokenResponse>(state.tokenUri, requestOptions);\n        debug(\"Token response: %O\", tokenResponse);\n        assert(tokenResponse.access_token, \"Failed to obtain access token.\");\n\n        // Now we need to determine when is this authorization going to expire\n        state.expiresAt = getAccessTokenExpiration(tokenResponse, env);\n\n        // save the tokenResponse so that we don't have to re-authorize on\n        // every page reload\n        state = { ...state, tokenResponse };\n        await Storage.set(key, state);\n        debug(\"Authorization successful!\");\n    }\n    else {\n        debug(state.tokenResponse?.access_token ?\n            \"Already authorized\" :\n            \"No authorization needed\"\n        );\n    }\n\n    if (fullSessionStorageSupport) {\n        await Storage.set(SMART_KEY, key);\n    }\n\n    const client = new Client(env, state);\n    debug(\"Created client instance: %O\", client);\n    return client;\n}\n\n/**\n * Builds the token request options. Does not make the request, just\n * creates it's configuration and returns it in a Promise.\n */\nexport async function buildTokenRequest(\n    env: fhirclient.Adapter,\n    {\n        code,\n        state,\n        clientPublicKeySetUrl,\n        privateKey\n    }: {\n        /**\n         * The `code` URL parameter received from the auth redirect\n         */\n        code: string,\n        \n        /**\n         * The app state\n         */\n        state: fhirclient.ClientState\n\n        /**\n         * If provided overrides the `clientPublicKeySetUrl` from the authorize\n         * options (if any). Used for `jku` token header in case of asymmetric auth.\n         */\n        clientPublicKeySetUrl?: string\n\n        /**\n         * Can be a private JWK, or an object with alg, kid and key properties,\n         * where `key` is an un-extractable private CryptoKey object.\n         */\n        privateKey?: fhirclient.JWK | {\n            key: CryptoKey\n            alg: \"RS384\" | \"ES384\"\n            kid: string\n        }\n    }\n): Promise<RequestInit>\n{\n    const { redirectUri, clientSecret, tokenUri, clientId, codeVerifier } = state;\n\n    assert(redirectUri, \"Missing state.redirectUri\");\n    assert(tokenUri, \"Missing state.tokenUri\");\n    assert(clientId, \"Missing state.clientId\");\n\n    const requestOptions: Record<string, any> = {\n        method: \"POST\",\n        headers: { \"content-type\": \"application/x-www-form-urlencoded\" },\n        body: `code=${code}&grant_type=authorization_code&redirect_uri=${\n            encodeURIComponent(redirectUri)}`\n    };\n\n    // For public apps, authentication is not possible (and thus not required),\n    // since a client with no secret cannot prove its identity when it issues a\n    // call. (The end-to-end system can still be secure because the client comes\n    // from a known, https protected endpoint specified and enforced by the\n    // redirect uri.) For confidential apps, an Authorization header using HTTP\n    // Basic authentication is required, where the username is the app’s\n    // client_id and the password is the app’s client_secret (see example).\n    if (clientSecret) {\n        requestOptions.headers.authorization = \"Basic \" + env.btoa(\n            clientId + \":\" + clientSecret\n        );\n        debug(\n            \"Using state.clientSecret to construct the authorization header: %s\",\n            requestOptions.headers.authorization\n        );\n    }\n    \n    // Asymmetric auth\n    else if (privateKey) {\n\n        const pk = \"key\" in privateKey ?\n            privateKey.key as CryptoKey:\n            await env.security.importJWK(privateKey as fhirclient.JWK)\n\n        const jwtHeaders = {\n            typ: \"JWT\",\n            kid: privateKey.kid,\n            jku: clientPublicKeySetUrl || state.clientPublicKeySetUrl\n        };\n\n        const jwtClaims = {\n            iss: clientId,\n            sub: clientId,\n            aud: tokenUri,\n            jti: env.base64urlencode(env.security.randomBytes(32)),\n            exp: getTimeInFuture(120) // two minutes in the future\n        };\n        \n        const clientAssertion = await env.security.signCompactJws(privateKey.alg, pk, jwtHeaders, jwtClaims);\n        requestOptions.body += `&client_assertion_type=${encodeURIComponent(\"urn:ietf:params:oauth:client-assertion-type:jwt-bearer\")}`;\n        requestOptions.body += `&client_assertion=${encodeURIComponent(clientAssertion)}`;\n        debug(\"Using state.clientPrivateJwk to add a client_assertion to the POST body\")\n    }\n    \n    // Public client\n    else {\n        debug(\"Public client detected; adding state.clientId to the POST body\");\n        requestOptions.body += `&client_id=${encodeURIComponent(clientId)}`;\n    }\n\n    if (codeVerifier) {\n      debug(\"Found state.codeVerifier, adding to the POST body\")\n      // Note that the codeVerifier is ALREADY encoded properly  \n      requestOptions.body += \"&code_verifier=\" + codeVerifier;\n    }\n  \n    return requestOptions as RequestInit;\n}\n\n/**\n * This function can be used when you want to handle everything in one page\n * (no launch endpoint needed). You can think of it as if it does:\n * ```js\n * authorize(options).then(ready)\n * ```\n *\n * **Be careful with init()!** There are some details you need to be aware of:\n *\n * 1. It will only work if your launch_uri is the same as your redirect_uri.\n *    While this should be valid, we can’t promise that every EHR will allow you\n *    to register client with such settings.\n * 2. Internally, `init()` will be called twice. First it will redirect to the\n *    EHR, then the EHR will redirect back to the page where init() will be\n *    called again to complete the authorization. This is generally fine,\n *    because the returned promise will only be resolved once, after the second\n *    execution, but please also consider the following:\n *    - You should wrap all your app’s code in a function that is only executed\n *      after `init()` resolves!\n *    - Since the page will be loaded twice, you must be careful if your code\n *      has global side effects that can persist between page reloads\n *      (for example writing to localStorage).\n * 3. For standalone launch, only use init in combination with offline_access\n *    scope. Once the access_token expires, if you don’t have a refresh_token\n *    there is no way to re-authorize properly. We detect that and delete the\n *    expired access token, but it still means that the user will have to\n *    refresh the page twice to re-authorize.\n * @param env The adapter\n * @param authorizeOptions The authorize options\n */\nexport async function init(\n    env: fhirclient.Adapter,\n    authorizeOptions: fhirclient.AuthorizeParams,\n    readyOptions?: fhirclient.ReadyOptions\n): Promise<Client|never>\n{\n    const url   = env.getUrl();\n    const code  = url.searchParams.get(\"code\");\n    const state = url.searchParams.get(\"state\");\n\n    // if `code` and `state` params are present we need to complete the auth flow\n    if (code && state) {\n        return ready(env, readyOptions);\n    }\n\n    // Check for existing client state. If state is found, it means a client\n    // instance have already been created in this session and we should try to\n    // \"revive\" it.\n    const storage = env.getStorage();\n    const key     = state || await storage.get(SMART_KEY);\n    const cached  = await storage.get(key);\n    if (cached) {\n        return new Client(env, cached);\n    }\n\n    // Otherwise try to launch\n    return authorize(env, authorizeOptions).then(() => {\n        // `init` promises a Client but that cannot happen in this case. The\n        // browser will be redirected (unload the page and be redirected back\n        // to it later and the same init function will be called again). On\n        // success, authorize will resolve with the redirect url but we don't\n        // want to return that from this promise chain because it is not a\n        // Client instance. At the same time, if authorize fails, we do want to\n        // pass the error to those waiting for a client instance.\n        return new Promise(() => { /* leave it pending!!! */ });\n    });\n}\n", "export default class Storage\n{\n    /**\n     * Gets the value at `key`. Returns a promise that will be resolved\n     * with that value (or undefined for missing keys).\n     */\n    async get(key: string): Promise<any>\n    {\n        const value = sessionStorage[key];\n        if (value) {\n            return JSON.parse(value);\n        }\n        return null;\n    }\n\n    /**\n     * Sets the `value` on `key` and returns a promise that will be resolved\n     * with the value that was set.\n     */\n    async set(key: string, value: any): Promise<any>\n    {\n        sessionStorage[key] = JSON.stringify(value);\n        return value;\n    }\n\n    /**\n     * Deletes the value at `key`. Returns a promise that will be resolved\n     * with true if the key was deleted or with false if it was not (eg. if\n     * did not exist).\n     */\n    async unset(key: string): Promise<boolean>\n    {\n        if (key in sessionStorage) {\n            delete sessionStorage[key];\n            return true;\n        }\n        return false;\n    }\n\n}\n", "// This map contains reusable debug messages (only those used in multiple places)\nexport default {\n    expired      : \"Session expired! Please re-launch the app\",\n    noScopeForId : \"Trying to get the ID of the selected %s. Please add 'launch' or 'launch/%s' to the requested scopes and try again.\",\n    noIfNoAuth   : \"You are trying to get %s but the app is not authorized yet.\",\n    noFreeContext: \"Please don't use open fhir servers if you need to access launch context items like the %S.\"\n};\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tlet i;\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t\tconst len = split.length;\n\n\t\tfor (i = 0; i < len; i++) {\n\t\t\tif (!split[i]) {\n\t\t\t\t// ignore empty strings\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tnamespaces = split[i].replace(/\\*/g, '.*?');\n\n\t\t\tif (namespaces[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(new RegExp('^' + namespaces.slice(1) + '$'));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(new RegExp('^' + namespaces + '$'));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names.map(toNamespace),\n\t\t\t...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tif (name[name.length - 1] === '*') {\n\t\t\treturn true;\n\t\t}\n\n\t\tlet i;\n\t\tlet len;\n\n\t\tfor (i = 0, len = createDebug.skips.length; i < len; i++) {\n\t\t\tif (createDebug.skips[i].test(name)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0, len = createDebug.names.length; i < len; i++) {\n\t\t\tif (createDebug.names[i].test(name)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/\n\tfunction toNamespace(regexp) {\n\t\treturn regexp.toString()\n\t\t\t.substring(2, regexp.toString().length - 2)\n\t\t\t.replace(/\\.\\*\\?$/, '*');\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "//\n// THIS FILE IS AUTOMATICALLY GENERATED! DO NOT EDIT BY HAND!\n//\n;\n(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined'\n        ? module.exports = factory()\n        : typeof define === 'function' && define.amd\n            ? define(factory) :\n            // cf. https://github.com/dankogai/js-base64/issues/119\n            (function () {\n                // existing version for noConflict()\n                var _Base64 = global.Base64;\n                var gBase64 = factory();\n                gBase64.noConflict = function () {\n                    global.Base64 = _Base64;\n                    return gBase64;\n                };\n                if (global.Meteor) { // Meteor.js\n                    Base64 = gBase64;\n                }\n                global.Base64 = gBase64;\n            })();\n}((typeof self !== 'undefined' ? self\n    : typeof window !== 'undefined' ? window\n        : typeof global !== 'undefined' ? global\n            : this), function () {\n    'use strict';\n    /**\n     *  base64.ts\n     *\n     *  Licensed under the BSD 3-Clause License.\n     *    http://opensource.org/licenses/BSD-3-Clause\n     *\n     *  References:\n     *    http://en.wikipedia.org/wiki/Base64\n     *\n     * <AUTHOR> (https://github.com/dankogai)\n     */\n    var version = '3.7.7';\n    /**\n     * @deprecated use lowercase `version`.\n     */\n    var VERSION = version;\n    var _hasBuffer = typeof Buffer === 'function';\n    var _TD = typeof TextDecoder === 'function' ? new TextDecoder() : undefined;\n    var _TE = typeof TextEncoder === 'function' ? new TextEncoder() : undefined;\n    var b64ch = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n    var b64chs = Array.prototype.slice.call(b64ch);\n    var b64tab = (function (a) {\n        var tab = {};\n        a.forEach(function (c, i) { return tab[c] = i; });\n        return tab;\n    })(b64chs);\n    var b64re = /^(?:[A-Za-z\\d+\\/]{4})*?(?:[A-Za-z\\d+\\/]{2}(?:==)?|[A-Za-z\\d+\\/]{3}=?)?$/;\n    var _fromCC = String.fromCharCode.bind(String);\n    var _U8Afrom = typeof Uint8Array.from === 'function'\n        ? Uint8Array.from.bind(Uint8Array)\n        : function (it) { return new Uint8Array(Array.prototype.slice.call(it, 0)); };\n    var _mkUriSafe = function (src) { return src\n        .replace(/=/g, '').replace(/[+\\/]/g, function (m0) { return m0 == '+' ? '-' : '_'; }); };\n    var _tidyB64 = function (s) { return s.replace(/[^A-Za-z0-9\\+\\/]/g, ''); };\n    /**\n     * polyfill version of `btoa`\n     */\n    var btoaPolyfill = function (bin) {\n        // console.log('polyfilled');\n        var u32, c0, c1, c2, asc = '';\n        var pad = bin.length % 3;\n        for (var i = 0; i < bin.length;) {\n            if ((c0 = bin.charCodeAt(i++)) > 255 ||\n                (c1 = bin.charCodeAt(i++)) > 255 ||\n                (c2 = bin.charCodeAt(i++)) > 255)\n                throw new TypeError('invalid character found');\n            u32 = (c0 << 16) | (c1 << 8) | c2;\n            asc += b64chs[u32 >> 18 & 63]\n                + b64chs[u32 >> 12 & 63]\n                + b64chs[u32 >> 6 & 63]\n                + b64chs[u32 & 63];\n        }\n        return pad ? asc.slice(0, pad - 3) + \"===\".substring(pad) : asc;\n    };\n    /**\n     * does what `window.btoa` of web browsers do.\n     * @param {String} bin binary string\n     * @returns {string} Base64-encoded string\n     */\n    var _btoa = typeof btoa === 'function' ? function (bin) { return btoa(bin); }\n        : _hasBuffer ? function (bin) { return Buffer.from(bin, 'binary').toString('base64'); }\n            : btoaPolyfill;\n    var _fromUint8Array = _hasBuffer\n        ? function (u8a) { return Buffer.from(u8a).toString('base64'); }\n        : function (u8a) {\n            // cf. https://stackoverflow.com/questions/12710001/how-to-convert-uint8-array-to-base64-encoded-string/12713326#12713326\n            var maxargs = 0x1000;\n            var strs = [];\n            for (var i = 0, l = u8a.length; i < l; i += maxargs) {\n                strs.push(_fromCC.apply(null, u8a.subarray(i, i + maxargs)));\n            }\n            return _btoa(strs.join(''));\n        };\n    /**\n     * converts a Uint8Array to a Base64 string.\n     * @param {boolean} [urlsafe] URL-and-filename-safe a la RFC4648 §5\n     * @returns {string} Base64 string\n     */\n    var fromUint8Array = function (u8a, urlsafe) {\n        if (urlsafe === void 0) { urlsafe = false; }\n        return urlsafe ? _mkUriSafe(_fromUint8Array(u8a)) : _fromUint8Array(u8a);\n    };\n    // This trick is found broken https://github.com/dankogai/js-base64/issues/130\n    // const utob = (src: string) => unescape(encodeURIComponent(src));\n    // reverting good old fationed regexp\n    var cb_utob = function (c) {\n        if (c.length < 2) {\n            var cc = c.charCodeAt(0);\n            return cc < 0x80 ? c\n                : cc < 0x800 ? (_fromCC(0xc0 | (cc >>> 6))\n                    + _fromCC(0x80 | (cc & 0x3f)))\n                    : (_fromCC(0xe0 | ((cc >>> 12) & 0x0f))\n                        + _fromCC(0x80 | ((cc >>> 6) & 0x3f))\n                        + _fromCC(0x80 | (cc & 0x3f)));\n        }\n        else {\n            var cc = 0x10000\n                + (c.charCodeAt(0) - 0xD800) * 0x400\n                + (c.charCodeAt(1) - 0xDC00);\n            return (_fromCC(0xf0 | ((cc >>> 18) & 0x07))\n                + _fromCC(0x80 | ((cc >>> 12) & 0x3f))\n                + _fromCC(0x80 | ((cc >>> 6) & 0x3f))\n                + _fromCC(0x80 | (cc & 0x3f)));\n        }\n    };\n    var re_utob = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFFF]|[^\\x00-\\x7F]/g;\n    /**\n     * @deprecated should have been internal use only.\n     * @param {string} src UTF-8 string\n     * @returns {string} UTF-16 string\n     */\n    var utob = function (u) { return u.replace(re_utob, cb_utob); };\n    //\n    var _encode = _hasBuffer\n        ? function (s) { return Buffer.from(s, 'utf8').toString('base64'); }\n        : _TE\n            ? function (s) { return _fromUint8Array(_TE.encode(s)); }\n            : function (s) { return _btoa(utob(s)); };\n    /**\n     * converts a UTF-8-encoded string to a Base64 string.\n     * @param {boolean} [urlsafe] if `true` make the result URL-safe\n     * @returns {string} Base64 string\n     */\n    var encode = function (src, urlsafe) {\n        if (urlsafe === void 0) { urlsafe = false; }\n        return urlsafe\n            ? _mkUriSafe(_encode(src))\n            : _encode(src);\n    };\n    /**\n     * converts a UTF-8-encoded string to URL-safe Base64 RFC4648 §5.\n     * @returns {string} Base64 string\n     */\n    var encodeURI = function (src) { return encode(src, true); };\n    // This trick is found broken https://github.com/dankogai/js-base64/issues/130\n    // const btou = (src: string) => decodeURIComponent(escape(src));\n    // reverting good old fationed regexp\n    var re_btou = /[\\xC0-\\xDF][\\x80-\\xBF]|[\\xE0-\\xEF][\\x80-\\xBF]{2}|[\\xF0-\\xF7][\\x80-\\xBF]{3}/g;\n    var cb_btou = function (cccc) {\n        switch (cccc.length) {\n            case 4:\n                var cp = ((0x07 & cccc.charCodeAt(0)) << 18)\n                    | ((0x3f & cccc.charCodeAt(1)) << 12)\n                    | ((0x3f & cccc.charCodeAt(2)) << 6)\n                    | (0x3f & cccc.charCodeAt(3)), offset = cp - 0x10000;\n                return (_fromCC((offset >>> 10) + 0xD800)\n                    + _fromCC((offset & 0x3FF) + 0xDC00));\n            case 3:\n                return _fromCC(((0x0f & cccc.charCodeAt(0)) << 12)\n                    | ((0x3f & cccc.charCodeAt(1)) << 6)\n                    | (0x3f & cccc.charCodeAt(2)));\n            default:\n                return _fromCC(((0x1f & cccc.charCodeAt(0)) << 6)\n                    | (0x3f & cccc.charCodeAt(1)));\n        }\n    };\n    /**\n     * @deprecated should have been internal use only.\n     * @param {string} src UTF-16 string\n     * @returns {string} UTF-8 string\n     */\n    var btou = function (b) { return b.replace(re_btou, cb_btou); };\n    /**\n     * polyfill version of `atob`\n     */\n    var atobPolyfill = function (asc) {\n        // console.log('polyfilled');\n        asc = asc.replace(/\\s+/g, '');\n        if (!b64re.test(asc))\n            throw new TypeError('malformed base64.');\n        asc += '=='.slice(2 - (asc.length & 3));\n        var u24, bin = '', r1, r2;\n        for (var i = 0; i < asc.length;) {\n            u24 = b64tab[asc.charAt(i++)] << 18\n                | b64tab[asc.charAt(i++)] << 12\n                | (r1 = b64tab[asc.charAt(i++)]) << 6\n                | (r2 = b64tab[asc.charAt(i++)]);\n            bin += r1 === 64 ? _fromCC(u24 >> 16 & 255)\n                : r2 === 64 ? _fromCC(u24 >> 16 & 255, u24 >> 8 & 255)\n                    : _fromCC(u24 >> 16 & 255, u24 >> 8 & 255, u24 & 255);\n        }\n        return bin;\n    };\n    /**\n     * does what `window.atob` of web browsers do.\n     * @param {String} asc Base64-encoded string\n     * @returns {string} binary string\n     */\n    var _atob = typeof atob === 'function' ? function (asc) { return atob(_tidyB64(asc)); }\n        : _hasBuffer ? function (asc) { return Buffer.from(asc, 'base64').toString('binary'); }\n            : atobPolyfill;\n    //\n    var _toUint8Array = _hasBuffer\n        ? function (a) { return _U8Afrom(Buffer.from(a, 'base64')); }\n        : function (a) { return _U8Afrom(_atob(a).split('').map(function (c) { return c.charCodeAt(0); })); };\n    /**\n     * converts a Base64 string to a Uint8Array.\n     */\n    var toUint8Array = function (a) { return _toUint8Array(_unURI(a)); };\n    //\n    var _decode = _hasBuffer\n        ? function (a) { return Buffer.from(a, 'base64').toString('utf8'); }\n        : _TD\n            ? function (a) { return _TD.decode(_toUint8Array(a)); }\n            : function (a) { return btou(_atob(a)); };\n    var _unURI = function (a) { return _tidyB64(a.replace(/[-_]/g, function (m0) { return m0 == '-' ? '+' : '/'; })); };\n    /**\n     * converts a Base64 string to a UTF-8 string.\n     * @param {String} src Base64 string.  Both normal and URL-safe are supported\n     * @returns {string} UTF-8 string\n     */\n    var decode = function (src) { return _decode(_unURI(src)); };\n    /**\n     * check if a value is a valid Base64 string\n     * @param {String} src a value to check\n      */\n    var isValid = function (src) {\n        if (typeof src !== 'string')\n            return false;\n        var s = src.replace(/\\s+/g, '').replace(/={0,2}$/, '');\n        return !/[^\\s0-9a-zA-Z\\+/]/.test(s) || !/[^\\s0-9a-zA-Z\\-_]/.test(s);\n    };\n    //\n    var _noEnum = function (v) {\n        return {\n            value: v, enumerable: false, writable: true, configurable: true\n        };\n    };\n    /**\n     * extend String.prototype with relevant methods\n     */\n    var extendString = function () {\n        var _add = function (name, body) { return Object.defineProperty(String.prototype, name, _noEnum(body)); };\n        _add('fromBase64', function () { return decode(this); });\n        _add('toBase64', function (urlsafe) { return encode(this, urlsafe); });\n        _add('toBase64URI', function () { return encode(this, true); });\n        _add('toBase64URL', function () { return encode(this, true); });\n        _add('toUint8Array', function () { return toUint8Array(this); });\n    };\n    /**\n     * extend Uint8Array.prototype with relevant methods\n     */\n    var extendUint8Array = function () {\n        var _add = function (name, body) { return Object.defineProperty(Uint8Array.prototype, name, _noEnum(body)); };\n        _add('toBase64', function (urlsafe) { return fromUint8Array(this, urlsafe); });\n        _add('toBase64URI', function () { return fromUint8Array(this, true); });\n        _add('toBase64URL', function () { return fromUint8Array(this, true); });\n    };\n    /**\n     * extend Builtin prototypes with relevant methods\n     */\n    var extendBuiltins = function () {\n        extendString();\n        extendUint8Array();\n    };\n    var gBase64 = {\n        version: version,\n        VERSION: VERSION,\n        atob: _atob,\n        atobPolyfill: atobPolyfill,\n        btoa: _btoa,\n        btoaPolyfill: btoaPolyfill,\n        fromBase64: decode,\n        toBase64: encode,\n        encode: encode,\n        encodeURI: encodeURI,\n        encodeURL: encodeURI,\n        utob: utob,\n        btou: btou,\n        decode: decode,\n        isValid: isValid,\n        fromUint8Array: fromUint8Array,\n        toUint8Array: toUint8Array,\n        extendString: extendString,\n        extendUint8Array: extendUint8Array,\n        extendBuiltins: extendBuiltins\n    };\n    //\n    // export Base64 to the namespace\n    //\n    // ES5 is yet to have Object.assign() that may make transpilers unhappy.\n    // gBase64.Base64 = Object.assign({}, gBase64);\n    gBase64.Base64 = {};\n    Object.keys(gBase64).forEach(function (k) { return gBase64.Base64[k] = gBase64[k]; });\n    return gBase64;\n}));\n", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "import './webcrypto-shim.mjs'\nexport default window.crypto\n", "/**\n * @file Web Cryptography API shim\n * <AUTHOR> <v<PERSON><PERSON><PERSON>@gmail.com>\n * @license MIT\n */\n(function (global, factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define([], function () {\n            return factory(global);\n        });\n    } else if (typeof module === 'object' && module.exports) {\n        // CommonJS-like environments that support module.exports\n        module.exports = factory(global);\n    } else {\n        factory(global);\n    }\n}(typeof self !== 'undefined' ? self : this, function (global) {\n    'use strict';\n\n    if ( typeof Promise !== 'function' )\n        throw \"Promise support required\";\n\n    var _crypto = global.crypto || global.msCrypto;\n    if ( !_crypto ) return;\n\n    var _subtle = _crypto.subtle || _crypto.webkitSubtle;\n    if ( !_subtle ) return;\n\n    var _Crypto     = global.Crypto || _crypto.constructor || Object,\n        _SubtleCrypto = global.SubtleCrypto || _subtle.constructor || Object,\n        _CryptoKey  = global.CryptoKey || global.Key || Object;\n\n    var isEdge = global.navigator.userAgent.indexOf('Edge/') > -1;\n    var isIE    = !!global.msCrypto && !isEdge;\n    var isWebkit = !_crypto.subtle && !!_crypto.webkitSubtle;\n    if ( !isIE && !isWebkit ) return;\n\n    function s2a ( s ) {\n        return btoa(s).replace(/\\=+$/, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n    }\n\n    function a2s ( s ) {\n        s += '===', s = s.slice( 0, -s.length % 4 );\n        return atob( s.replace(/-/g, '+').replace(/_/g, '/') );\n    }\n\n    function s2b ( s ) {\n        var b = new Uint8Array(s.length);\n        for ( var i = 0; i < s.length; i++ ) b[i] = s.charCodeAt(i);\n        return b;\n    }\n\n    function b2s ( b ) {\n        if ( b instanceof ArrayBuffer ) b = new Uint8Array(b);\n        return String.fromCharCode.apply( String, b );\n    }\n\n    function alg ( a ) {\n        var r = { 'name': (a.name || a || '').toUpperCase().replace('V','v') };\n        switch ( r.name ) {\n            case 'SHA-1':\n            case 'SHA-256':\n            case 'SHA-384':\n            case 'SHA-512':\n                break;\n            case 'AES-CBC':\n            case 'AES-GCM':\n            case 'AES-KW':\n                if ( a.length ) r['length'] = a.length;\n                break;\n            case 'HMAC':\n                if ( a.hash ) r['hash'] = alg(a.hash);\n                if ( a.length ) r['length'] = a.length;\n                break;\n            case 'RSAES-PKCS1-v1_5':\n                if ( a.publicExponent ) r['publicExponent'] = new Uint8Array(a.publicExponent);\n                if ( a.modulusLength ) r['modulusLength'] = a.modulusLength;\n                break;\n            case 'RSASSA-PKCS1-v1_5':\n            case 'RSA-OAEP':\n                if ( a.hash ) r['hash'] = alg(a.hash);\n                if ( a.publicExponent ) r['publicExponent'] = new Uint8Array(a.publicExponent);\n                if ( a.modulusLength ) r['modulusLength'] = a.modulusLength;\n                break;\n            default:\n                throw new SyntaxError(\"Bad algorithm name\");\n        }\n        return r;\n    };\n\n    function jwkAlg ( a ) {\n        return {\n            'HMAC': {\n                'SHA-1': 'HS1',\n                'SHA-256': 'HS256',\n                'SHA-384': 'HS384',\n                'SHA-512': 'HS512',\n            },\n            'RSASSA-PKCS1-v1_5': {\n                'SHA-1': 'RS1',\n                'SHA-256': 'RS256',\n                'SHA-384': 'RS384',\n                'SHA-512': 'RS512',\n            },\n            'RSAES-PKCS1-v1_5': {\n                '': 'RSA1_5',\n            },\n            'RSA-OAEP': {\n                'SHA-1': 'RSA-OAEP',\n                'SHA-256': 'RSA-OAEP-256',\n            },\n            'AES-KW': {\n                '128': 'A128KW',\n                '192': 'A192KW',\n                '256': 'A256KW',\n            },\n            'AES-GCM': {\n                '128': 'A128GCM',\n                '192': 'A192GCM',\n                '256': 'A256GCM',\n            },\n            'AES-CBC': {\n                '128': 'A128CBC',\n                '192': 'A192CBC',\n                '256': 'A256CBC',\n            },\n        }[a.name][ ( a.hash || {} ).name || a.length || '' ];\n    }\n\n    function b2jwk ( k ) {\n        if ( k instanceof ArrayBuffer || k instanceof Uint8Array ) k = JSON.parse( decodeURIComponent( escape( b2s(k) ) ) );\n        var jwk = { 'kty': k.kty, 'alg': k.alg, 'ext': k.ext || k.extractable };\n        switch ( jwk.kty ) {\n            case 'oct':\n                jwk.k = k.k;\n            case 'RSA':\n                [ 'n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qi', 'oth' ].forEach( function ( x ) { if ( x in k ) jwk[x] = k[x] } );\n                break;\n            default:\n                throw new TypeError(\"Unsupported key type\");\n        }\n        return jwk;\n    }\n\n    function jwk2b ( k ) {\n        var jwk = b2jwk(k);\n        if ( isIE ) jwk['extractable'] = jwk.ext, delete jwk.ext;\n        return s2b( unescape( encodeURIComponent( JSON.stringify(jwk) ) ) ).buffer;\n    }\n\n    function pkcs2jwk ( k ) {\n        var info = b2der(k), prv = false;\n        if ( info.length > 2 ) prv = true, info.shift(); // remove version from PKCS#8 PrivateKeyInfo structure\n        var jwk = { 'ext': true };\n        switch ( info[0][0] ) {\n            case '1.2.840.113549.1.1.1':\n                var rsaComp = [ 'n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qi' ],\n                    rsaKey  = b2der( info[1] );\n                if ( prv ) rsaKey.shift(); // remove version from PKCS#1 RSAPrivateKey structure\n                for ( var i = 0; i < rsaKey.length; i++ ) {\n                    if ( !rsaKey[i][0] ) rsaKey[i] = rsaKey[i].subarray(1);\n                    jwk[ rsaComp[i] ] = s2a( b2s( rsaKey[i] ) );\n                }\n                jwk['kty'] = 'RSA';\n                break;\n            default:\n                throw new TypeError(\"Unsupported key type\");\n        }\n        return jwk;\n    }\n\n    function jwk2pkcs ( k ) {\n        var key, info = [ [ '', null ] ], prv = false;\n        switch ( k.kty ) {\n            case 'RSA':\n                var rsaComp = [ 'n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qi' ],\n                    rsaKey = [];\n                for ( var i = 0; i < rsaComp.length; i++ ) {\n                    if ( !( rsaComp[i] in k ) ) break;\n                    var b = rsaKey[i] = s2b( a2s( k[ rsaComp[i] ] ) );\n                    if ( b[0] & 0x80 ) rsaKey[i] = new Uint8Array(b.length + 1), rsaKey[i].set( b, 1 );\n                }\n                if ( rsaKey.length > 2 ) prv = true, rsaKey.unshift( new Uint8Array([0]) ); // add version to PKCS#1 RSAPrivateKey structure\n                info[0][0] = '1.2.840.113549.1.1.1';\n                key = rsaKey;\n                break;\n            default:\n                throw new TypeError(\"Unsupported key type\");\n        }\n        info.push( new Uint8Array( der2b(key) ).buffer );\n        if ( !prv ) info[1] = { 'tag': 0x03, 'value': info[1] };\n        else info.unshift( new Uint8Array([0]) ); // add version to PKCS#8 PrivateKeyInfo structure\n        return new Uint8Array( der2b(info) ).buffer;\n    }\n\n    var oid2str = { 'KoZIhvcNAQEB': '1.2.840.113549.1.1.1' },\n        str2oid = { '1.2.840.113549.1.1.1': 'KoZIhvcNAQEB' };\n\n    function b2der ( buf, ctx ) {\n        if ( buf instanceof ArrayBuffer ) buf = new Uint8Array(buf);\n        if ( !ctx ) ctx = { pos: 0, end: buf.length };\n\n        if ( ctx.end - ctx.pos < 2 || ctx.end > buf.length ) throw new RangeError(\"Malformed DER\");\n\n        var tag = buf[ctx.pos++],\n            len = buf[ctx.pos++];\n\n        if ( len >= 0x80 ) {\n            len &= 0x7f;\n            if ( ctx.end - ctx.pos < len ) throw new RangeError(\"Malformed DER\");\n            for ( var xlen = 0; len--; ) xlen <<= 8, xlen |= buf[ctx.pos++];\n            len = xlen;\n        }\n\n        if ( ctx.end - ctx.pos < len ) throw new RangeError(\"Malformed DER\");\n\n        var rv;\n\n        switch ( tag ) {\n            case 0x02: // Universal Primitive INTEGER\n                rv = buf.subarray( ctx.pos, ctx.pos += len );\n                break;\n            case 0x03: // Universal Primitive BIT STRING\n                if ( buf[ctx.pos++] ) throw new Error( \"Unsupported bit string\" );\n                len--;\n            case 0x04: // Universal Primitive OCTET STRING\n                rv = new Uint8Array( buf.subarray( ctx.pos, ctx.pos += len ) ).buffer;\n                break;\n            case 0x05: // Universal Primitive NULL\n                rv = null;\n                break;\n            case 0x06: // Universal Primitive OBJECT IDENTIFIER\n                var oid = btoa( b2s( buf.subarray( ctx.pos, ctx.pos += len ) ) );\n                if ( !( oid in oid2str ) ) throw new Error( \"Unsupported OBJECT ID \" + oid );\n                rv = oid2str[oid];\n                break;\n            case 0x30: // Universal Constructed SEQUENCE\n                rv = [];\n                for ( var end = ctx.pos + len; ctx.pos < end; ) rv.push( b2der( buf, ctx ) );\n                break;\n            default:\n                throw new Error( \"Unsupported DER tag 0x\" + tag.toString(16) );\n        }\n\n        return rv;\n    }\n\n    function der2b ( val, buf ) {\n        if ( !buf ) buf = [];\n\n        var tag = 0, len = 0,\n            pos = buf.length + 2;\n\n        buf.push( 0, 0 ); // placeholder\n\n        if ( val instanceof Uint8Array ) {  // Universal Primitive INTEGER\n            tag = 0x02, len = val.length;\n            for ( var i = 0; i < len; i++ ) buf.push( val[i] );\n        }\n        else if ( val instanceof ArrayBuffer ) { // Universal Primitive OCTET STRING\n            tag = 0x04, len = val.byteLength, val = new Uint8Array(val);\n            for ( var i = 0; i < len; i++ ) buf.push( val[i] );\n        }\n        else if ( val === null ) { // Universal Primitive NULL\n            tag = 0x05, len = 0;\n        }\n        else if ( typeof val === 'string' && val in str2oid ) { // Universal Primitive OBJECT IDENTIFIER\n            var oid = s2b( atob( str2oid[val] ) );\n            tag = 0x06, len = oid.length;\n            for ( var i = 0; i < len; i++ ) buf.push( oid[i] );\n        }\n        else if ( val instanceof Array ) { // Universal Constructed SEQUENCE\n            for ( var i = 0; i < val.length; i++ ) der2b( val[i], buf );\n            tag = 0x30, len = buf.length - pos;\n        }\n        else if ( typeof val === 'object' && val.tag === 0x03 && val.value instanceof ArrayBuffer ) { // Tag hint\n            val = new Uint8Array(val.value), tag = 0x03, len = val.byteLength;\n            buf.push(0); for ( var i = 0; i < len; i++ ) buf.push( val[i] );\n            len++;\n        }\n        else {\n            throw new Error( \"Unsupported DER value \" + val );\n        }\n\n        if ( len >= 0x80 ) {\n            var xlen = len, len = 4;\n            buf.splice( pos, 0, (xlen >> 24) & 0xff, (xlen >> 16) & 0xff, (xlen >> 8) & 0xff, xlen & 0xff );\n            while ( len > 1 && !(xlen >> 24) ) xlen <<= 8, len--;\n            if ( len < 4 ) buf.splice( pos, 4 - len );\n            len |= 0x80;\n        }\n\n        buf.splice( pos - 2, 2, tag, len );\n\n        return buf;\n    }\n\n    function CryptoKey ( key, alg, ext, use ) {\n        Object.defineProperties( this, {\n            _key: {\n                value: key\n            },\n            type: {\n                value: key.type,\n                enumerable: true,\n            },\n            extractable: {\n                value: (ext === undefined) ? key.extractable : ext,\n                enumerable: true,\n            },\n            algorithm: {\n                value: (alg === undefined) ? key.algorithm : alg,\n                enumerable: true,\n            },\n            usages: {\n                value: (use === undefined) ? key.usages : use,\n                enumerable: true,\n            },\n        });\n    }\n\n    function isPubKeyUse ( u ) {\n        return u === 'verify' || u === 'encrypt' || u === 'wrapKey';\n    }\n\n    function isPrvKeyUse ( u ) {\n        return u === 'sign' || u === 'decrypt' || u === 'unwrapKey';\n    }\n\n    [ 'generateKey', 'importKey', 'unwrapKey' ]\n        .forEach( function ( m ) {\n            var _fn = _subtle[m];\n\n            _subtle[m] = function ( a, b, c ) {\n                var args = [].slice.call(arguments),\n                    ka, kx, ku;\n\n                switch ( m ) {\n                    case 'generateKey':\n                        ka = alg(a), kx = b, ku = c;\n                        break;\n                    case 'importKey':\n                        ka = alg(c), kx = args[3], ku = args[4];\n                        if ( a === 'jwk' ) {\n                            b = b2jwk(b);\n                            if ( !b.alg ) b.alg = jwkAlg(ka);\n                            if ( !b.key_ops ) b.key_ops = ( b.kty !== 'oct' ) ? ( 'd' in b ) ? ku.filter(isPrvKeyUse) : ku.filter(isPubKeyUse) : ku.slice();\n                            args[1] = jwk2b(b);\n                        }\n                        break;\n                    case 'unwrapKey':\n                        ka = args[4], kx = args[5], ku = args[6];\n                        args[2] = c._key;\n                        break;\n                }\n\n                if ( m === 'generateKey' && ka.name === 'HMAC' && ka.hash ) {\n                    ka.length = ka.length || { 'SHA-1': 512, 'SHA-256': 512, 'SHA-384': 1024, 'SHA-512': 1024 }[ka.hash.name];\n                    return _subtle.importKey( 'raw', _crypto.getRandomValues( new Uint8Array( (ka.length+7)>>3 ) ), ka, kx, ku );\n                }\n\n                if ( isWebkit && m === 'generateKey' && ka.name === 'RSASSA-PKCS1-v1_5' && ( !ka.modulusLength || ka.modulusLength >= 2048 ) ) {\n                    a = alg(a), a.name = 'RSAES-PKCS1-v1_5', delete a.hash;\n                    return _subtle.generateKey( a, true, [ 'encrypt', 'decrypt' ] )\n                        .then( function ( k ) {\n                            return Promise.all([\n                                _subtle.exportKey( 'jwk', k.publicKey ),\n                                _subtle.exportKey( 'jwk', k.privateKey ),\n                            ]);\n                        })\n                        .then( function ( keys ) {\n                            keys[0].alg = keys[1].alg = jwkAlg(ka);\n                            keys[0].key_ops = ku.filter(isPubKeyUse), keys[1].key_ops = ku.filter(isPrvKeyUse);\n                            return Promise.all([\n                                _subtle.importKey( 'jwk', keys[0], ka, true, keys[0].key_ops ),\n                                _subtle.importKey( 'jwk', keys[1], ka, kx, keys[1].key_ops ),\n                            ]);\n                        })\n                        .then( function ( keys ) {\n                            return {\n                                publicKey: keys[0],\n                                privateKey: keys[1],\n                            };\n                        });\n                }\n\n                if ( ( isWebkit || ( isIE && ( ka.hash || {} ).name === 'SHA-1' ) )\n                        && m === 'importKey' && a === 'jwk' && ka.name === 'HMAC' && b.kty === 'oct' ) {\n                    return _subtle.importKey( 'raw', s2b( a2s(b.k) ), c, args[3], args[4] );\n                }\n\n                if ( isWebkit && m === 'importKey' && ( a === 'spki' || a === 'pkcs8' ) ) {\n                    return _subtle.importKey( 'jwk', pkcs2jwk(b), c, args[3], args[4] );\n                }\n\n                if ( isIE && m === 'unwrapKey' ) {\n                    return _subtle.decrypt( args[3], c, b )\n                        .then( function ( k ) {\n                            return _subtle.importKey( a, k, args[4], args[5], args[6] );\n                        });\n                }\n\n                var op;\n                try {\n                    op = _fn.apply( _subtle, args );\n                }\n                catch ( e ) {\n                    return Promise.reject(e);\n                }\n\n                if ( isIE ) {\n                    op = new Promise( function ( res, rej ) {\n                        op.onabort =\n                        op.onerror =    function ( e ) { rej(e)               };\n                        op.oncomplete = function ( r ) { res(r.target.result) };\n                    });\n                }\n\n                op = op.then( function ( k ) {\n                    if ( ka.name === 'HMAC' ) {\n                        if ( !ka.length ) ka.length = 8 * k.algorithm.length;\n                    }\n                    if ( ka.name.search('RSA') == 0 ) {\n                        if ( !ka.modulusLength ) ka.modulusLength = (k.publicKey || k).algorithm.modulusLength;\n                        if ( !ka.publicExponent ) ka.publicExponent = (k.publicKey || k).algorithm.publicExponent;\n                    }\n                    if ( k.publicKey && k.privateKey ) {\n                        k = {\n                            publicKey: new CryptoKey( k.publicKey, ka, kx, ku.filter(isPubKeyUse) ),\n                            privateKey: new CryptoKey( k.privateKey, ka, kx, ku.filter(isPrvKeyUse) ),\n                        };\n                    }\n                    else {\n                        k = new CryptoKey( k, ka, kx, ku );\n                    }\n                    return k;\n                });\n\n                return op;\n            }\n        });\n\n    [ 'exportKey', 'wrapKey' ]\n        .forEach( function ( m ) {\n            var _fn = _subtle[m];\n\n            _subtle[m] = function ( a, b, c ) {\n                var args = [].slice.call(arguments);\n\n                switch ( m ) {\n                    case 'exportKey':\n                        args[1] = b._key;\n                        break;\n                    case 'wrapKey':\n                        args[1] = b._key, args[2] = c._key;\n                        break;\n                }\n\n                if ( ( isWebkit || ( isIE && ( b.algorithm.hash || {} ).name === 'SHA-1' ) )\n                        && m === 'exportKey' && a === 'jwk' && b.algorithm.name === 'HMAC' ) {\n                    args[0] = 'raw';\n                }\n\n                if ( isWebkit && m === 'exportKey' && ( a === 'spki' || a === 'pkcs8' ) ) {\n                    args[0] = 'jwk';\n                }\n\n                if ( isIE && m === 'wrapKey' ) {\n                    return _subtle.exportKey( a, b )\n                        .then( function ( k ) {\n                            if ( a === 'jwk' ) k = s2b( unescape( encodeURIComponent( JSON.stringify( b2jwk(k) ) ) ) );\n                            return  _subtle.encrypt( args[3], c, k );\n                        });\n                }\n\n                var op;\n                try {\n                    op = _fn.apply( _subtle, args );\n                }\n                catch ( e ) {\n                    return Promise.reject(e);\n                }\n\n                if ( isIE ) {\n                    op = new Promise( function ( res, rej ) {\n                        op.onabort =\n                        op.onerror =    function ( e ) { rej(e)               };\n                        op.oncomplete = function ( r ) { res(r.target.result) };\n                    });\n                }\n\n                if ( m === 'exportKey' && a === 'jwk' ) {\n                    op = op.then( function ( k ) {\n                        if ( ( isWebkit || ( isIE && ( b.algorithm.hash || {} ).name === 'SHA-1' ) )\n                                && b.algorithm.name === 'HMAC') {\n                            return { 'kty': 'oct', 'alg': jwkAlg(b.algorithm), 'key_ops': b.usages.slice(), 'ext': true, 'k': s2a( b2s(k) ) };\n                        }\n                        k = b2jwk(k);\n                        if ( !k.alg ) k['alg'] = jwkAlg(b.algorithm);\n                        if ( !k.key_ops ) k['key_ops'] = ( b.type === 'public' ) ? b.usages.filter(isPubKeyUse) : ( b.type === 'private' ) ? b.usages.filter(isPrvKeyUse) : b.usages.slice();\n                        return k;\n                    });\n                }\n\n                if ( isWebkit && m === 'exportKey' && ( a === 'spki' || a === 'pkcs8' ) ) {\n                    op = op.then( function ( k ) {\n                        k = jwk2pkcs( b2jwk(k) );\n                        return k;\n                    });\n                }\n\n                return op;\n            }\n        });\n\n    [ 'encrypt', 'decrypt', 'sign', 'verify' ]\n        .forEach( function ( m ) {\n            var _fn = _subtle[m];\n\n            _subtle[m] = function ( a, b, c, d ) {\n                if ( isIE && ( !c.byteLength || ( d && !d.byteLength ) ) )\n                    throw new Error(\"Empy input is not allowed\");\n\n                var args = [].slice.call(arguments),\n                    ka = alg(a);\n\n                if ( isIE && m === 'decrypt' && ka.name === 'AES-GCM' ) {\n                    var tl = a.tagLength >> 3;\n                    args[2] = (c.buffer || c).slice( 0, c.byteLength - tl ),\n                    a.tag = (c.buffer || c).slice( c.byteLength - tl );\n                }\n\n                args[1] = b._key;\n\n                var op;\n                try {\n                    op = _fn.apply( _subtle, args );\n                }\n                catch ( e ) {\n                    return Promise.reject(e);\n                }\n\n                if ( isIE ) {\n                    op = new Promise( function ( res, rej ) {\n                        op.onabort =\n                        op.onerror = function ( e ) {\n                            rej(e);\n                        };\n\n                        op.oncomplete = function ( r ) {\n                            var r = r.target.result;\n\n                            if ( m === 'encrypt' && r instanceof AesGcmEncryptResult ) {\n                                var c = r.ciphertext, t = r.tag;\n                                r = new Uint8Array( c.byteLength + t.byteLength );\n                                r.set( new Uint8Array(c), 0 );\n                                r.set( new Uint8Array(t), c.byteLength );\n                                r = r.buffer;\n                            }\n\n                            res(r);\n                        };\n                    });\n                }\n\n                return op;\n            }\n        });\n\n    if ( isIE ) {\n        var _digest = _subtle.digest;\n\n        _subtle['digest'] = function ( a, b ) {\n            if ( !b.byteLength )\n                throw new Error(\"Empy input is not allowed\");\n\n            var op;\n            try {\n                op = _digest.call( _subtle, a, b );\n            }\n            catch ( e ) {\n                return Promise.reject(e);\n            }\n\n            op = new Promise( function ( res, rej ) {\n                op.onabort =\n                op.onerror =    function ( e ) { rej(e)               };\n                op.oncomplete = function ( r ) { res(r.target.result) };\n            });\n\n            return op;\n        };\n\n        global.crypto = Object.create( _crypto, {\n            getRandomValues: { value: function ( a ) { return _crypto.getRandomValues(a) } },\n            subtle:          { value: _subtle },\n        });\n\n        global.CryptoKey = CryptoKey;\n    }\n\n    if ( isWebkit ) {\n        _crypto.subtle = _subtle;\n\n        global.Crypto = _Crypto;\n        global.SubtleCrypto = _SubtleCrypto;\n        global.CryptoKey = CryptoKey;\n    }\n}));\n\n export default {} // section modified by isomorphic-webcrypto build \n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(\"./src/entry/browser.ts\");\n", ""], "names": ["lib_1", "require", "strings_1", "settings_1", "FhirClient_1", "Response", "FHIRCLIENT_PURE", "window", "debug", "extend", "contextualize", "requestOptions", "client", "base", "absolute", "state", "serverUrl", "contextualURL", "_url", "resourceType", "pathname", "split", "pop", "assert", "patientCompartment", "indexOf", "conformance", "fetchConformanceStatement", "searchParam", "getPatientParam", "searchParams", "set", "patient", "id", "href", "URL", "url", "Client", "default", "constructor", "environment", "_state", "match", "units", "_refreshTask", "getPatientId", "read", "request", "Promise", "reject", "Error", "fhirOptions", "options", "encounter", "getEncounterId", "user", "fhirUser", "getFhirUser", "getUserId", "getUserType", "connect", "fhir", "fhirJs", "baseUrl", "replace", "accessToken", "getState", "auth", "token", "username", "password", "pass", "api", "patientId", "tokenResponse", "scope", "noScopeForId", "authorize<PERSON><PERSON>", "noIfNoAuth", "noFreeContext", "getIdToken", "idToken", "id_token", "hasOpenid", "hasProfile", "has<PERSON>hir<PERSON>ser", "jwtDecode", "slice", "join", "profile", "getAuthorizationHeader", "btoa", "_clearState", "storage", "getStorage", "key", "get", "SMART_KEY", "unset", "_resolvedRefs", "debugRequest", "String", "graph", "flat", "pageLimit", "_a", "resolveReferences", "makeArray", "useRefreshToken", "onPage", "undefined", "signal", "refreshIfNeeded", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "authorization", "response", "fhirRequest", "then", "result", "includeResponse", "body", "catch", "error", "status", "message", "expired", "data", "fetchReferences", "resolve", "_data", "links", "link", "entry", "map", "resource", "next", "find", "l", "relation", "nextPage", "length", "Object", "assign", "references", "concat", "refreshToken", "expiresAt", "Date", "now", "refresh", "debugRefresh", "_b", "refresh_token", "tokenUri", "scopes", "hasOfflineAccess", "search", "hasOnlineAccess", "encodeURIComponent", "refreshTokenWithClientId", "clientId", "refreshRequestOptions", "credentials", "refreshTokenWithCredentials", "method", "mode", "clientSecret", "access_token", "getAccessTokenExpiration", "finally", "byCode", "observations", "property", "byCodes", "<PERSON><PERSON><PERSON>", "obj", "path", "exports", "FhirClient", "fhirBaseUrl", "create", "JSON", "stringify", "update", "delete", "patch", "assertJsonPatch", "resolveRef", "cache", "node", "isArray", "Array", "all", "filter", "Boolean", "item", "i", "ref", "reference", "cacheMap", "sub", "set<PERSON>ath", "ex", "console", "warn", "paths", "trim", "reduce", "prev", "cur", "includes", "push", "groups", "for<PERSON>ach", "len", "task", "keys", "sort", "group", "getReferences", "refs", "out", "resources", "bundleOrUrl", "count", "page", "pages", "limit", "fetchPage", "aborted", "nextLink", "uri", "res", "getFhirVersion", "metadata", "fhirVersion", "getFhirRelease", "v", "fhirVersions", "HttpError", "statusText", "name", "statusCode", "parse", "bodyUsed", "type", "json", "error_description", "text", "toJSON", "smart_1", "Client_1", "BrowserStorage_1", "security", "js_base64_1", "BrowserAdapter", "_storage", "replaceBrowserHistory", "fullSessionStorageSupport", "relative", "getUrl", "location", "redirect", "to", "getAbortController", "AbortController", "atob", "str", "base64urlencode", "input", "encodeURL", "fromUint8Array", "base64urldecode", "decode", "getSmartApi", "ready", "args", "authorize", "init", "utils", "BrowserAdapter_1", "adapter", "fetch", "Headers", "Request", "FHIR", "oauth2", "settings", "module", "HttpError_1", "_debug", "cm", "code", "value", "ensureNumerical", "kg", "any", "pq", "checkResponse", "resp", "ok", "responseToJSON", "loweCaseKeys", "lowerKey", "toLowerCase", "accept", "getAndCache", "force", "process", "env", "NODE_ENV", "segments", "shift", "o", "createEmpty", "idx", "arr", "arg", "randomString", "str<PERSON><PERSON><PERSON>", "charSet", "char<PERSON>t", "Math", "floor", "random", "payload", "getTimeInFuture", "secondsAhead", "from", "expires_in", "tokenBody", "exp", "ret", "handleCodeableConcept", "concept", "observation", "coding", "bank", "codes", "meta", "r", "x", "patientParams", "p", "getTargetWindow", "target", "width", "height", "self", "parent", "top", "targetWindow", "open", "e", "screen", "winOr<PERSON>rame", "frames", "condition", "operation", "op", "crypto", "globalThis", "subtle", "isSecureContext", "ALGS", "ES384", "namedCurve", "RS384", "modulus<PERSON>ength", "publicExponent", "Uint8Array", "hash", "randomBytes", "getRandomValues", "digestSha256", "prepared", "TextEncoder", "encode", "digest", "generatePKCEChallenge", "entropy", "inputBytes", "codeVerifier", "codeChallenge", "importJWK", "jwk", "alg", "key_ops", "importKey", "ext", "signCompactJws", "privateKey", "header", "jwtHeader", "jwtPayload", "jwtAuthenticatedContent", "signature", "sign", "algorithm", "defineProperty", "enumerable", "<PERSON><PERSON><PERSON><PERSON>", "fetchWellKnownJson", "getSecurityExtensionsFromWellKnownJson", "authorization_endpoint", "token_endpoint", "registrationUri", "registration_endpoint", "codeChallengeMethods", "code_challenge_methods_supported", "getSecurityExtensionsFromConformanceStatement", "nsUri", "extensions", "extension", "valueUri", "getSecurityExtensions", "params", "urlISS", "cfg", "issMatch", "RegExp", "test", "fakeTokenResponse", "encounterId", "pkceMode", "clientPublicKeySetUrl", "redirect_uri", "client_id", "iss", "launch", "fhirServiceUrl", "redirectUri", "noRedirect", "completeInTarget", "clientPrivateJwk", "stateKey", "inFrame", "isInFrame", "inPopUp", "isInPopUp", "<PERSON><PERSON><PERSON>", "redirectUrl", "redirectParams", "shouldIncludeChallenge", "win", "sessionStorage", "removeItem", "setItem", "addEventListener", "onMessage", "S256supported", "opener", "origin", "removeEventListener", "Storage", "authError", "authErrorDescription", "postMessage", "close", "hasState", "has", "history", "replaceState", "authorized", "buildTokenRequest", "pk", "jwtHeaders", "typ", "kid", "jku", "jwtClaims", "aud", "jti", "clientAssertion", "authorizeOptions", "readyOptions", "cached"], "sourceRoot": ""}