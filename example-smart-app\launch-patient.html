<!DOCTYPE html>
<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <link rel='stylesheet' type='text/css' href='./src/css/example-smart-app.css'>
  <!--
      Temporarily disable cerner-smart-embeddable-lib
      <link rel='stylesheet' type='text/css' href='./lib/css/cerner-smart-embeddable-lib-1.0.0.min.css'>
    -->
  <title>seb Example-SMART-App v1B</title>
</head>

<body>
  <!-- Required JS files to enable this page to embed within an MPage -->
  <!--
      Temporarily disable cerner-smart-embeddable-lib
      <script src='https://cdnjs.cloudflare.com/ajax/libs/babel-polyfill/6.26.0/polyfill.min.js'></script>
      <script src='./lib/js/cerner-smart-embeddable-lib-1.0.0.min.js'></script>
    -->

  <!-- FHIR Client JS Library -->
  <script src='./lib/js/fhir-client-v0.1.12.js'></script>

  <!-- Prevent session bleed caused by single threaded embedded browser and sessionStorage API -->
  <!-- https://github.com/cerner/fhir-client-cerner-additions -->
  <script src='./lib/js/fhir-client-cerner-additions-1.0.0.js'></script>
  <script>
    const clientId = "678aa6b2-4053-4d4a-ace0-eb7c6339da4c";
    FHIR.oauth2.authorize({
      'client_id': clientId,
      'scope': 'patient/Patient.read patient/Observation.read launch/patient online_access openid profile'
    });
  </script>
  <div class="spinner">
    <div class="bounce1"></div>
    <div class="bounce2"></div>
    <div class="bounce3"></div>
  </div>
</body>

</html>