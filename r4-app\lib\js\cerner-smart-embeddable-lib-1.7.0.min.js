(function(modules){var installedModules={};function __webpack_require__(moduleId){if(installedModules[moduleId]){return installedModules[moduleId].exports}var module=installedModules[moduleId]={i:moduleId,l:false,exports:{}};modules[moduleId].call(module.exports,module,module.exports,__webpack_require__);module.l=true;return module.exports}__webpack_require__.m=modules;__webpack_require__.c=installedModules;__webpack_require__.d=function(exports,name,getter){if(!__webpack_require__.o(exports,name)){Object.defineProperty(exports,name,{configurable:false,enumerable:true,get:getter})}};__webpack_require__.n=function(module){var getter=module&&module.__esModule?function getDefault(){return module["default"]}:function getModuleExports(){return module};__webpack_require__.d(getter,"a",getter);return getter};__webpack_require__.o=function(object,property){return Object.prototype.hasOwnProperty.call(object,property)};__webpack_require__.p="";return __webpack_require__(__webpack_require__.s=77)})([function(module,exports){var core=module.exports={version:"2.6.11"};if(typeof __e=="number")__e=core},function(module,exports){var global=module.exports=typeof window!="undefined"&&window.Math==Math?window:typeof self!="undefined"&&self.Math==Math?self:Function("return this")();if(typeof __g=="number")__g=global},function(module,exports,__webpack_require__){var store=__webpack_require__(35)("wks");var uid=__webpack_require__(25);var Symbol=__webpack_require__(1).Symbol;var USE_SYMBOL=typeof Symbol=="function";var $exports=module.exports=function(name){return store[name]||(store[name]=USE_SYMBOL&&Symbol[name]||(USE_SYMBOL?Symbol:uid)("Symbol."+name))};$exports.store=store},function(module,exports,__webpack_require__){var global=__webpack_require__(1);var core=__webpack_require__(0);var ctx=__webpack_require__(12);var hide=__webpack_require__(9);var has=__webpack_require__(10);var PROTOTYPE="prototype";var $export=function(type,name,source){var IS_FORCED=type&$export.F;var IS_GLOBAL=type&$export.G;var IS_STATIC=type&$export.S;var IS_PROTO=type&$export.P;var IS_BIND=type&$export.B;var IS_WRAP=type&$export.W;var exports=IS_GLOBAL?core:core[name]||(core[name]={});var expProto=exports[PROTOTYPE];var target=IS_GLOBAL?global:IS_STATIC?global[name]:(global[name]||{})[PROTOTYPE];var key,own,out;if(IS_GLOBAL)source=name;for(key in source){own=!IS_FORCED&&target&&target[key]!==undefined;if(own&&has(exports,key))continue;out=own?target[key]:source[key];exports[key]=IS_GLOBAL&&typeof target[key]!="function"?source[key]:IS_BIND&&own?ctx(out,global):IS_WRAP&&target[key]==out?function(C){var F=function(a,b,c){if(this instanceof C){switch(arguments.length){case 0:return new C;case 1:return new C(a);case 2:return new C(a,b)}return new C(a,b,c)}return C.apply(this,arguments)};F[PROTOTYPE]=C[PROTOTYPE];return F}(out):IS_PROTO&&typeof out=="function"?ctx(Function.call,out):out;if(IS_PROTO){(exports.virtual||(exports.virtual={}))[key]=out;if(type&$export.R&&expProto&&!expProto[key])hide(expProto,key,out)}}};$export.F=1;$export.G=2;$export.S=4;$export.P=8;$export.B=16;$export.W=32;$export.U=64;$export.R=128;module.exports=$export},function(module,exports,__webpack_require__){var isObject=__webpack_require__(7);module.exports=function(it){if(!isObject(it))throw TypeError(it+" is not an object!");return it}},function(module,exports,__webpack_require__){module.exports=!__webpack_require__(13)(function(){return Object.defineProperty({},"a",{get:function(){return 7}}).a!=7})},function(module,exports,__webpack_require__){var anObject=__webpack_require__(4);var IE8_DOM_DEFINE=__webpack_require__(47);var toPrimitive=__webpack_require__(30);var dP=Object.defineProperty;exports.f=__webpack_require__(5)?Object.defineProperty:function defineProperty(O,P,Attributes){anObject(O);P=toPrimitive(P,true);anObject(Attributes);if(IE8_DOM_DEFINE)try{return dP(O,P,Attributes)}catch(e){}if("get"in Attributes||"set"in Attributes)throw TypeError("Accessors not supported!");if("value"in Attributes)O[P]=Attributes.value;return O}},function(module,exports){module.exports=function(it){return typeof it==="object"?it!==null:typeof it==="function"}},function(module,exports,__webpack_require__){var IObject=__webpack_require__(45);var defined=__webpack_require__(28);module.exports=function(it){return IObject(defined(it))}},function(module,exports,__webpack_require__){var dP=__webpack_require__(6);var createDesc=__webpack_require__(17);module.exports=__webpack_require__(5)?function(object,key,value){return dP.f(object,key,createDesc(1,value))}:function(object,key,value){object[key]=value;return object}},function(module,exports){var hasOwnProperty={}.hasOwnProperty;module.exports=function(it,key){return hasOwnProperty.call(it,key)}},function(module,exports){module.exports={}},function(module,exports,__webpack_require__){var aFunction=__webpack_require__(24);module.exports=function(fn,that,length){aFunction(fn);if(that===undefined)return fn;switch(length){case 1:return function(a){return fn.call(that,a)};case 2:return function(a,b){return fn.call(that,a,b)};case 3:return function(a,b,c){return fn.call(that,a,b,c)}}return function(){return fn.apply(that,arguments)}}},function(module,exports){module.exports=function(exec){try{return!!exec()}catch(e){return true}}},function(module,exports,__webpack_require__){"use strict";exports.__esModule=true;exports.default=function(instance,Constructor){if(!(instance instanceof Constructor)){throw new TypeError("Cannot call a class as a function")}}},function(module,exports){var toString={}.toString;module.exports=function(it){return toString.call(it).slice(8,-1)}},function(module,exports){module.exports=true},function(module,exports){module.exports=function(bitmap,value){return{enumerable:!(bitmap&1),configurable:!(bitmap&2),writable:!(bitmap&4),value:value}}},function(module,exports,__webpack_require__){var $keys=__webpack_require__(49);var enumBugKeys=__webpack_require__(36);module.exports=Object.keys||function keys(O){return $keys(O,enumBugKeys)}},function(module,exports,__webpack_require__){var defined=__webpack_require__(28);module.exports=function(it){return Object(defined(it))}},function(module,exports,__webpack_require__){"use strict";var $at=__webpack_require__(94)(true);__webpack_require__(46)(String,"String",function(iterated){this._t=String(iterated);this._i=0},function(){var O=this._t;var index=this._i;var point;if(index>=O.length)return{value:undefined,done:true};point=$at(O,index);this._i+=point.length;return{value:point,done:false}})},function(module,exports){exports.f={}.propertyIsEnumerable},function(module,exports,__webpack_require__){"use strict";exports.__esModule=true;var _defineProperty=__webpack_require__(102);var _defineProperty2=_interopRequireDefault(_defineProperty);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}exports.default=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||false;descriptor.configurable=true;if("value"in descriptor)descriptor.writable=true;(0,_defineProperty2.default)(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){if(protoProps)defineProperties(Constructor.prototype,protoProps);if(staticProps)defineProperties(Constructor,staticProps);return Constructor}}()},function(module,exports,__webpack_require__){__webpack_require__(87);var global=__webpack_require__(1);var hide=__webpack_require__(9);var Iterators=__webpack_require__(11);var TO_STRING_TAG=__webpack_require__(2)("toStringTag");var DOMIterables=("CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,"+"DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,"+"MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,"+"SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,"+"TextTrackList,TouchList").split(",");for(var i=0;i<DOMIterables.length;i++){var NAME=DOMIterables[i];var Collection=global[NAME];var proto=Collection&&Collection.prototype;if(proto&&!proto[TO_STRING_TAG])hide(proto,TO_STRING_TAG,NAME);Iterators[NAME]=Iterators.Array}},function(module,exports){module.exports=function(it){if(typeof it!="function")throw TypeError(it+" is not a function!");return it}},function(module,exports){var id=0;var px=Math.random();module.exports=function(key){return"Symbol(".concat(key===undefined?"":key,")_",(++id+px).toString(36))}},function(module,exports,__webpack_require__){var def=__webpack_require__(6).f;var has=__webpack_require__(10);var TAG=__webpack_require__(2)("toStringTag");module.exports=function(it,tag,stat){if(it&&!has(it=stat?it:it.prototype,TAG))def(it,TAG,{configurable:true,value:tag})}},function(module,exports,__webpack_require__){"use strict";(function(process){Object.defineProperty(exports,"__esModule",{value:true});exports.default={log:function log(){if(process.env.NODE_ENV!=="production"){var _console;(_console=console).log.apply(_console,arguments)}},warn:function warn(){var _console2;return(_console2=console).warn.apply(_console2,arguments)},error:function error(){var _console3;return(_console3=console).error.apply(_console3,arguments)}}}).call(exports,__webpack_require__(140))},function(module,exports){module.exports=function(it){if(it==undefined)throw TypeError("Can't call method on  "+it);return it}},function(module,exports,__webpack_require__){var isObject=__webpack_require__(7);var document=__webpack_require__(1).document;var is=isObject(document)&&isObject(document.createElement);module.exports=function(it){return is?document.createElement(it):{}}},function(module,exports,__webpack_require__){var isObject=__webpack_require__(7);module.exports=function(it,S){if(!isObject(it))return it;var fn,val;if(S&&typeof(fn=it.toString)=="function"&&!isObject(val=fn.call(it)))return val;if(typeof(fn=it.valueOf)=="function"&&!isObject(val=fn.call(it)))return val;if(!S&&typeof(fn=it.toString)=="function"&&!isObject(val=fn.call(it)))return val;throw TypeError("Can't convert object to primitive value")}},function(module,exports,__webpack_require__){var anObject=__webpack_require__(4);var dPs=__webpack_require__(91);var enumBugKeys=__webpack_require__(36);var IE_PROTO=__webpack_require__(34)("IE_PROTO");var Empty=function(){};var PROTOTYPE="prototype";var createDict=function(){var iframe=__webpack_require__(29)("iframe");var i=enumBugKeys.length;var lt="<";var gt=">";var iframeDocument;iframe.style.display="none";__webpack_require__(50).appendChild(iframe);iframe.src="javascript:";iframeDocument=iframe.contentWindow.document;iframeDocument.open();iframeDocument.write(lt+"script"+gt+"document.F=Object"+lt+"/script"+gt);iframeDocument.close();createDict=iframeDocument.F;while(i--)delete createDict[PROTOTYPE][enumBugKeys[i]];return createDict()};module.exports=Object.create||function create(O,Properties){var result;if(O!==null){Empty[PROTOTYPE]=anObject(O);result=new Empty;Empty[PROTOTYPE]=null;result[IE_PROTO]=O}else result=createDict();return Properties===undefined?result:dPs(result,Properties)}},function(module,exports,__webpack_require__){var toInteger=__webpack_require__(33);var min=Math.min;module.exports=function(it){return it>0?min(toInteger(it),9007199254740991):0}},function(module,exports){var ceil=Math.ceil;var floor=Math.floor;module.exports=function(it){return isNaN(it=+it)?0:(it>0?floor:ceil)(it)}},function(module,exports,__webpack_require__){var shared=__webpack_require__(35)("keys");var uid=__webpack_require__(25);module.exports=function(key){return shared[key]||(shared[key]=uid(key))}},function(module,exports,__webpack_require__){var core=__webpack_require__(0);var global=__webpack_require__(1);var SHARED="__core-js_shared__";var store=global[SHARED]||(global[SHARED]={});(module.exports=function(key,value){return store[key]||(store[key]=value!==undefined?value:{})})("versions",[]).push({version:core.version,mode:__webpack_require__(16)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(module,exports){module.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(module,exports,__webpack_require__){var cof=__webpack_require__(15);var TAG=__webpack_require__(2)("toStringTag");var ARG=cof(function(){return arguments}())=="Arguments";var tryGet=function(it,key){try{return it[key]}catch(e){}};module.exports=function(it){var O,T,B;return it===undefined?"Undefined":it===null?"Null":typeof(T=tryGet(O=Object(it),TAG))=="string"?T:ARG?cof(O):(B=cof(O))=="Object"&&typeof O.callee=="function"?"Arguments":B}},function(module,exports,__webpack_require__){var classof=__webpack_require__(37);var ITERATOR=__webpack_require__(2)("iterator");var Iterators=__webpack_require__(11);module.exports=__webpack_require__(0).getIteratorMethod=function(it){if(it!=undefined)return it[ITERATOR]||it["@@iterator"]||Iterators[classof(it)]}},function(module,exports){exports.f=Object.getOwnPropertySymbols},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(110),__esModule:true}},function(module,exports,__webpack_require__){"use strict";var aFunction=__webpack_require__(24);function PromiseCapability(C){var resolve,reject;this.promise=new C(function($$resolve,$$reject){if(resolve!==undefined||reject!==undefined)throw TypeError("Bad Promise constructor");resolve=$$resolve;reject=$$reject});this.resolve=aFunction(resolve);this.reject=aFunction(reject)}module.exports.f=function(C){return new PromiseCapability(C)}},function(module,exports,__webpack_require__){exports.f=__webpack_require__(2)},function(module,exports,__webpack_require__){var global=__webpack_require__(1);var core=__webpack_require__(0);var LIBRARY=__webpack_require__(16);var wksExt=__webpack_require__(42);var defineProperty=__webpack_require__(6).f;module.exports=function(name){var $Symbol=core.Symbol||(core.Symbol=LIBRARY?{}:global.Symbol||{});if(name.charAt(0)!="_"&&!(name in $Symbol))defineProperty($Symbol,name,{value:wksExt.f(name)})}},function(module,exports,__webpack_require__){"use strict";exports.__esModule=true;var _isIterable2=__webpack_require__(85);var _isIterable3=_interopRequireDefault(_isIterable2);var _getIterator2=__webpack_require__(96);var _getIterator3=_interopRequireDefault(_getIterator2);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}exports.default=function(){function sliceIterator(arr,i){var _arr=[];var _n=true;var _d=false;var _e=undefined;try{for(var _i=(0,_getIterator3.default)(arr),_s;!(_n=(_s=_i.next()).done);_n=true){_arr.push(_s.value);if(i&&_arr.length===i)break}}catch(err){_d=true;_e=err}finally{try{if(!_n&&_i["return"])_i["return"]()}finally{if(_d)throw _e}}return _arr}return function(arr,i){if(Array.isArray(arr)){return arr}else if((0,_isIterable3.default)(Object(arr))){return sliceIterator(arr,i)}else{throw new TypeError("Invalid attempt to destructure non-iterable instance")}}}()},function(module,exports,__webpack_require__){var cof=__webpack_require__(15);module.exports=Object("z").propertyIsEnumerable(0)?Object:function(it){return cof(it)=="String"?it.split(""):Object(it)}},function(module,exports,__webpack_require__){"use strict";var LIBRARY=__webpack_require__(16);var $export=__webpack_require__(3);var redefine=__webpack_require__(48);var hide=__webpack_require__(9);var Iterators=__webpack_require__(11);var $iterCreate=__webpack_require__(90);var setToStringTag=__webpack_require__(26);var getPrototypeOf=__webpack_require__(51);var ITERATOR=__webpack_require__(2)("iterator");var BUGGY=!([].keys&&"next"in[].keys());var FF_ITERATOR="@@iterator";var KEYS="keys";var VALUES="values";var returnThis=function(){return this};module.exports=function(Base,NAME,Constructor,next,DEFAULT,IS_SET,FORCED){$iterCreate(Constructor,NAME,next);var getMethod=function(kind){if(!BUGGY&&kind in proto)return proto[kind];switch(kind){case KEYS:return function keys(){return new Constructor(this,kind)};case VALUES:return function values(){return new Constructor(this,kind)}}return function entries(){return new Constructor(this,kind)}};var TAG=NAME+" Iterator";var DEF_VALUES=DEFAULT==VALUES;var VALUES_BUG=false;var proto=Base.prototype;var $native=proto[ITERATOR]||proto[FF_ITERATOR]||DEFAULT&&proto[DEFAULT];var $default=$native||getMethod(DEFAULT);var $entries=DEFAULT?!DEF_VALUES?$default:getMethod("entries"):undefined;var $anyNative=NAME=="Array"?proto.entries||$native:$native;var methods,key,IteratorPrototype;if($anyNative){IteratorPrototype=getPrototypeOf($anyNative.call(new Base));if(IteratorPrototype!==Object.prototype&&IteratorPrototype.next){setToStringTag(IteratorPrototype,TAG,true);if(!LIBRARY&&typeof IteratorPrototype[ITERATOR]!="function")hide(IteratorPrototype,ITERATOR,returnThis)}}if(DEF_VALUES&&$native&&$native.name!==VALUES){VALUES_BUG=true;$default=function values(){return $native.call(this)}}if((!LIBRARY||FORCED)&&(BUGGY||VALUES_BUG||!proto[ITERATOR])){hide(proto,ITERATOR,$default)}Iterators[NAME]=$default;Iterators[TAG]=returnThis;if(DEFAULT){methods={values:DEF_VALUES?$default:getMethod(VALUES),keys:IS_SET?$default:getMethod(KEYS),entries:$entries};if(FORCED)for(key in methods){if(!(key in proto))redefine(proto,key,methods[key])}else $export($export.P+$export.F*(BUGGY||VALUES_BUG),NAME,methods)}return methods}},function(module,exports,__webpack_require__){module.exports=!__webpack_require__(5)&&!__webpack_require__(13)(function(){return Object.defineProperty(__webpack_require__(29)("div"),"a",{get:function(){return 7}}).a!=7})},function(module,exports,__webpack_require__){module.exports=__webpack_require__(9)},function(module,exports,__webpack_require__){var has=__webpack_require__(10);var toIObject=__webpack_require__(8);var arrayIndexOf=__webpack_require__(92)(false);var IE_PROTO=__webpack_require__(34)("IE_PROTO");module.exports=function(object,names){var O=toIObject(object);var i=0;var result=[];var key;for(key in O)if(key!=IE_PROTO)has(O,key)&&result.push(key);while(names.length>i)if(has(O,key=names[i++])){~arrayIndexOf(result,key)||result.push(key)}return result}},function(module,exports,__webpack_require__){var document=__webpack_require__(1).document;module.exports=document&&document.documentElement},function(module,exports,__webpack_require__){var has=__webpack_require__(10);var toObject=__webpack_require__(19);var IE_PROTO=__webpack_require__(34)("IE_PROTO");var ObjectProto=Object.prototype;module.exports=Object.getPrototypeOf||function(O){O=toObject(O);if(has(O,IE_PROTO))return O[IE_PROTO];if(typeof O.constructor=="function"&&O instanceof O.constructor){return O.constructor.prototype}return O instanceof Object?ObjectProto:null}},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(99),__esModule:true}},function(module,exports,__webpack_require__){"use strict";exports.__esModule=true;var _assign=__webpack_require__(106);var _assign2=_interopRequireDefault(_assign);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}exports.default=_assign2.default||function(target){for(var i=1;i<arguments.length;i++){var source=arguments[i];for(var key in source){if(Object.prototype.hasOwnProperty.call(source,key)){target[key]=source[key]}}}return target}},function(module,exports){},function(module,exports,__webpack_require__){var anObject=__webpack_require__(4);module.exports=function(iterator,fn,value,entries){try{return entries?fn(anObject(value)[0],value[1]):fn(value)}catch(e){var ret=iterator["return"];if(ret!==undefined)anObject(ret.call(iterator));throw e}}},function(module,exports,__webpack_require__){var Iterators=__webpack_require__(11);var ITERATOR=__webpack_require__(2)("iterator");var ArrayProto=Array.prototype;module.exports=function(it){return it!==undefined&&(Iterators.Array===it||ArrayProto[ITERATOR]===it)}},function(module,exports,__webpack_require__){var anObject=__webpack_require__(4);var aFunction=__webpack_require__(24);var SPECIES=__webpack_require__(2)("species");module.exports=function(O,D){var C=anObject(O).constructor;var S;return C===undefined||(S=anObject(C)[SPECIES])==undefined?D:aFunction(S)}},function(module,exports,__webpack_require__){var ctx=__webpack_require__(12);var invoke=__webpack_require__(114);var html=__webpack_require__(50);var cel=__webpack_require__(29);var global=__webpack_require__(1);var process=global.process;var setTask=global.setImmediate;var clearTask=global.clearImmediate;var MessageChannel=global.MessageChannel;var Dispatch=global.Dispatch;var counter=0;var queue={};var ONREADYSTATECHANGE="onreadystatechange";var defer,channel,port;var run=function(){var id=+this;if(queue.hasOwnProperty(id)){var fn=queue[id];delete queue[id];fn()}};var listener=function(event){run.call(event.data)};if(!setTask||!clearTask){setTask=function setImmediate(fn){var args=[];var i=1;while(arguments.length>i)args.push(arguments[i++]);queue[++counter]=function(){invoke(typeof fn=="function"?fn:Function(fn),args)};defer(counter);return counter};clearTask=function clearImmediate(id){delete queue[id]};if(__webpack_require__(15)(process)=="process"){defer=function(id){process.nextTick(ctx(run,id,1))}}else if(Dispatch&&Dispatch.now){defer=function(id){Dispatch.now(ctx(run,id,1))}}else if(MessageChannel){channel=new MessageChannel;port=channel.port2;channel.port1.onmessage=listener;defer=ctx(port.postMessage,port,1)}else if(global.addEventListener&&typeof postMessage=="function"&&!global.importScripts){defer=function(id){global.postMessage(id+"","*")};global.addEventListener("message",listener,false)}else if(ONREADYSTATECHANGE in cel("script")){defer=function(id){html.appendChild(cel("script"))[ONREADYSTATECHANGE]=function(){html.removeChild(this);run.call(id)}}}else{defer=function(id){setTimeout(ctx(run,id,1),0)}}}module.exports={set:setTask,clear:clearTask}},function(module,exports){module.exports=function(exec){try{return{e:false,v:exec()}}catch(e){return{e:true,v:e}}}},function(module,exports,__webpack_require__){var anObject=__webpack_require__(4);var isObject=__webpack_require__(7);var newPromiseCapability=__webpack_require__(41);module.exports=function(C,x){anObject(C);if(isObject(x)&&x.constructor===C)return x;var promiseCapability=newPromiseCapability.f(C);var resolve=promiseCapability.resolve;resolve(x);return promiseCapability.promise}},function(module,exports,__webpack_require__){var ITERATOR=__webpack_require__(2)("iterator");var SAFE_CLOSING=false;try{var riter=[7][ITERATOR]();riter["return"]=function(){SAFE_CLOSING=true};Array.from(riter,function(){throw 2})}catch(e){}module.exports=function(exec,skipClosing){if(!skipClosing&&!SAFE_CLOSING)return false;var safe=false;try{var arr=[7];var iter=arr[ITERATOR]();iter.next=function(){return{done:safe=true}};arr[ITERATOR]=function(){return iter};exec(arr)}catch(e){}return safe}},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(121),__esModule:true}},function(module,exports,__webpack_require__){var $export=__webpack_require__(3);var core=__webpack_require__(0);var fails=__webpack_require__(13);module.exports=function(KEY,exec){var fn=(core.Object||{})[KEY]||Object[KEY];var exp={};exp[KEY]=exec(fn);$export($export.S+$export.F*fails(function(){fn(1)}),"Object",exp)}},function(module,exports,__webpack_require__){"use strict";exports.__esModule=true;var _typeof2=__webpack_require__(65);var _typeof3=_interopRequireDefault(_typeof2);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}exports.default=function(self,call){if(!self){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return call&&((typeof call==="undefined"?"undefined":(0,_typeof3.default)(call))==="object"||typeof call==="function")?call:self}},function(module,exports,__webpack_require__){"use strict";exports.__esModule=true;var _iterator=__webpack_require__(123);var _iterator2=_interopRequireDefault(_iterator);var _symbol=__webpack_require__(125);var _symbol2=_interopRequireDefault(_symbol);var _typeof=typeof _symbol2.default==="function"&&typeof _iterator2.default==="symbol"?function(obj){return typeof obj}:function(obj){return obj&&typeof _symbol2.default==="function"&&obj.constructor===_symbol2.default&&obj!==_symbol2.default.prototype?"symbol":typeof obj};function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}exports.default=typeof _symbol2.default==="function"&&_typeof(_iterator2.default)==="symbol"?function(obj){return typeof obj==="undefined"?"undefined":_typeof(obj)}:function(obj){return obj&&typeof _symbol2.default==="function"&&obj.constructor===_symbol2.default&&obj!==_symbol2.default.prototype?"symbol":typeof obj==="undefined"?"undefined":_typeof(obj)}},function(module,exports,__webpack_require__){var META=__webpack_require__(25)("meta");var isObject=__webpack_require__(7);var has=__webpack_require__(10);var setDesc=__webpack_require__(6).f;var id=0;var isExtensible=Object.isExtensible||function(){return true};var FREEZE=!__webpack_require__(13)(function(){return isExtensible(Object.preventExtensions({}))});var setMeta=function(it){setDesc(it,META,{value:{i:"O"+ ++id,w:{}}})};var fastKey=function(it,create){if(!isObject(it))return typeof it=="symbol"?it:(typeof it=="string"?"S":"P")+it;if(!has(it,META)){if(!isExtensible(it))return"F";if(!create)return"E";setMeta(it)}return it[META].i};var getWeak=function(it,create){if(!has(it,META)){if(!isExtensible(it))return true;if(!create)return false;setMeta(it)}return it[META].w};var onFreeze=function(it){if(FREEZE&&meta.NEED&&isExtensible(it)&&!has(it,META))setMeta(it);return it};var meta=module.exports={KEY:META,NEED:false,fastKey:fastKey,getWeak:getWeak,onFreeze:onFreeze}},function(module,exports,__webpack_require__){var $keys=__webpack_require__(49);var hiddenKeys=__webpack_require__(36).concat("length","prototype");exports.f=Object.getOwnPropertyNames||function getOwnPropertyNames(O){return $keys(O,hiddenKeys)}},function(module,exports,__webpack_require__){var pIE=__webpack_require__(21);var createDesc=__webpack_require__(17);var toIObject=__webpack_require__(8);var toPrimitive=__webpack_require__(30);var has=__webpack_require__(10);var IE8_DOM_DEFINE=__webpack_require__(47);var gOPD=Object.getOwnPropertyDescriptor;exports.f=__webpack_require__(5)?gOPD:function getOwnPropertyDescriptor(O,P){O=toIObject(O);P=toPrimitive(P,true);if(IE8_DOM_DEFINE)try{return gOPD(O,P)}catch(e){}if(has(O,P))return createDesc(!pIE.f.call(O,P),O[P])}},function(module,exports,__webpack_require__){"use strict";exports.__esModule=true;var _setPrototypeOf=__webpack_require__(133);var _setPrototypeOf2=_interopRequireDefault(_setPrototypeOf);var _create=__webpack_require__(137);var _create2=_interopRequireDefault(_create);var _typeof2=__webpack_require__(65);var _typeof3=_interopRequireDefault(_typeof2);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}exports.default=function(subClass,superClass){if(typeof superClass!=="function"&&superClass!==null){throw new TypeError("Super expression must either be null or a function, not "+(typeof superClass==="undefined"?"undefined":(0,_typeof3.default)(superClass)))}subClass.prototype=(0,_create2.default)(superClass&&superClass.prototype,{constructor:{value:subClass,enumerable:false,writable:true,configurable:true}});if(superClass)_setPrototypeOf2.default?(0,_setPrototypeOf2.default)(subClass,superClass):subClass.__proto__=superClass}},function(module,exports,__webpack_require__){"use strict";var R=typeof Reflect==="object"?Reflect:null;var ReflectApply=R&&typeof R.apply==="function"?R.apply:function ReflectApply(target,receiver,args){return Function.prototype.apply.call(target,receiver,args)};var ReflectOwnKeys;if(R&&typeof R.ownKeys==="function"){ReflectOwnKeys=R.ownKeys}else if(Object.getOwnPropertySymbols){ReflectOwnKeys=function ReflectOwnKeys(target){return Object.getOwnPropertyNames(target).concat(Object.getOwnPropertySymbols(target))}}else{ReflectOwnKeys=function ReflectOwnKeys(target){return Object.getOwnPropertyNames(target)}}function ProcessEmitWarning(warning){if(console&&console.warn)console.warn(warning)}var NumberIsNaN=Number.isNaN||function NumberIsNaN(value){return value!==value};function EventEmitter(){EventEmitter.init.call(this)}module.exports=EventEmitter;EventEmitter.EventEmitter=EventEmitter;EventEmitter.prototype._events=undefined;EventEmitter.prototype._eventsCount=0;EventEmitter.prototype._maxListeners=undefined;var defaultMaxListeners=10;function checkListener(listener){if(typeof listener!=="function"){throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof listener)}}Object.defineProperty(EventEmitter,"defaultMaxListeners",{enumerable:true,get:function(){return defaultMaxListeners},set:function(arg){if(typeof arg!=="number"||arg<0||NumberIsNaN(arg)){throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+arg+".")}defaultMaxListeners=arg}});EventEmitter.init=function(){if(this._events===undefined||this._events===Object.getPrototypeOf(this)._events){this._events=Object.create(null);this._eventsCount=0}this._maxListeners=this._maxListeners||undefined};EventEmitter.prototype.setMaxListeners=function setMaxListeners(n){if(typeof n!=="number"||n<0||NumberIsNaN(n)){throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+n+".")}this._maxListeners=n;return this};function _getMaxListeners(that){if(that._maxListeners===undefined)return EventEmitter.defaultMaxListeners;return that._maxListeners}EventEmitter.prototype.getMaxListeners=function getMaxListeners(){return _getMaxListeners(this)};EventEmitter.prototype.emit=function emit(type){var args=[];for(var i=1;i<arguments.length;i++)args.push(arguments[i]);var doError=type==="error";var events=this._events;if(events!==undefined)doError=doError&&events.error===undefined;else if(!doError)return false;if(doError){var er;if(args.length>0)er=args[0];if(er instanceof Error){throw er}var err=new Error("Unhandled error."+(er?" ("+er.message+")":""));err.context=er;throw err}var handler=events[type];if(handler===undefined)return false;if(typeof handler==="function"){ReflectApply(handler,this,args)}else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)ReflectApply(listeners[i],this,args)}return true};function _addListener(target,type,listener,prepend){var m;var events;var existing;checkListener(listener);events=target._events;if(events===undefined){events=target._events=Object.create(null);target._eventsCount=0}else{if(events.newListener!==undefined){target.emit("newListener",type,listener.listener?listener.listener:listener);events=target._events}existing=events[type]}if(existing===undefined){existing=events[type]=listener;++target._eventsCount}else{if(typeof existing==="function"){existing=events[type]=prepend?[listener,existing]:[existing,listener]}else if(prepend){existing.unshift(listener)}else{existing.push(listener)}m=_getMaxListeners(target);if(m>0&&existing.length>m&&!existing.warned){existing.warned=true;var w=new Error("Possible EventEmitter memory leak detected. "+existing.length+" "+String(type)+" listeners "+"added. Use emitter.setMaxListeners() to "+"increase limit");w.name="MaxListenersExceededWarning";w.emitter=target;w.type=type;w.count=existing.length;ProcessEmitWarning(w)}}return target}EventEmitter.prototype.addListener=function addListener(type,listener){return _addListener(this,type,listener,false)};EventEmitter.prototype.on=EventEmitter.prototype.addListener;EventEmitter.prototype.prependListener=function prependListener(type,listener){return _addListener(this,type,listener,true)};function onceWrapper(){if(!this.fired){this.target.removeListener(this.type,this.wrapFn);this.fired=true;if(arguments.length===0)return this.listener.call(this.target);return this.listener.apply(this.target,arguments)}}function _onceWrap(target,type,listener){var state={fired:false,wrapFn:undefined,target:target,type:type,listener:listener};var wrapped=onceWrapper.bind(state);wrapped.listener=listener;state.wrapFn=wrapped;return wrapped}EventEmitter.prototype.once=function once(type,listener){checkListener(listener);this.on(type,_onceWrap(this,type,listener));return this};EventEmitter.prototype.prependOnceListener=function prependOnceListener(type,listener){checkListener(listener);this.prependListener(type,_onceWrap(this,type,listener));return this};EventEmitter.prototype.removeListener=function removeListener(type,listener){var list,events,position,i,originalListener;checkListener(listener);events=this._events;if(events===undefined)return this;list=events[type];if(list===undefined)return this;if(list===listener||list.listener===listener){if(--this._eventsCount===0)this._events=Object.create(null);else{delete events[type];if(events.removeListener)this.emit("removeListener",type,list.listener||listener)}}else if(typeof list!=="function"){position=-1;for(i=list.length-1;i>=0;i--){if(list[i]===listener||list[i].listener===listener){originalListener=list[i].listener;position=i;break}}if(position<0)return this;if(position===0)list.shift();else{spliceOne(list,position)}if(list.length===1)events[type]=list[0];if(events.removeListener!==undefined)this.emit("removeListener",type,originalListener||listener)}return this};EventEmitter.prototype.off=EventEmitter.prototype.removeListener;EventEmitter.prototype.removeAllListeners=function removeAllListeners(type){var listeners,events,i;events=this._events;if(events===undefined)return this;if(events.removeListener===undefined){if(arguments.length===0){this._events=Object.create(null);this._eventsCount=0}else if(events[type]!==undefined){if(--this._eventsCount===0)this._events=Object.create(null);else delete events[type]}return this}if(arguments.length===0){var keys=Object.keys(events);var key;for(i=0;i<keys.length;++i){key=keys[i];if(key==="removeListener")continue;this.removeAllListeners(key)}this.removeAllListeners("removeListener");this._events=Object.create(null);this._eventsCount=0;return this}listeners=events[type];if(typeof listeners==="function"){this.removeListener(type,listeners)}else if(listeners!==undefined){for(i=listeners.length-1;i>=0;i--){this.removeListener(type,listeners[i])}}return this};function _listeners(target,type,unwrap){var events=target._events;if(events===undefined)return[];var evlistener=events[type];if(evlistener===undefined)return[];if(typeof evlistener==="function")return unwrap?[evlistener.listener||evlistener]:[evlistener];return unwrap?unwrapListeners(evlistener):arrayClone(evlistener,evlistener.length)}EventEmitter.prototype.listeners=function listeners(type){return _listeners(this,type,true)};EventEmitter.prototype.rawListeners=function rawListeners(type){return _listeners(this,type,false)};EventEmitter.listenerCount=function(emitter,type){if(typeof emitter.listenerCount==="function"){return emitter.listenerCount(type)}else{return listenerCount.call(emitter,type)}};EventEmitter.prototype.listenerCount=listenerCount;function listenerCount(type){var events=this._events;if(events!==undefined){var evlistener=events[type];if(typeof evlistener==="function"){return 1}else if(evlistener!==undefined){return evlistener.length}}return 0}EventEmitter.prototype.eventNames=function eventNames(){return this._eventsCount>0?ReflectOwnKeys(this._events):[]};function arrayClone(arr,n){var copy=new Array(n);for(var i=0;i<n;++i)copy[i]=arr[i];return copy}function spliceOne(list,index){for(;index+1<list.length;index++)list[index]=list[index+1];list.pop()}function unwrapListeners(arr){var ret=new Array(arr.length);for(var i=0;i<ret.length;++i){ret[i]=arr[i].listener||arr[i]}return ret}},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var _freeze=__webpack_require__(72);var _freeze2=_interopRequireDefault(_freeze);var _promise=__webpack_require__(40);var _promise2=_interopRequireDefault(_promise);var _extends2=__webpack_require__(53);var _extends3=_interopRequireDefault(_extends2);var _classCallCheck2=__webpack_require__(14);var _classCallCheck3=_interopRequireDefault(_classCallCheck2);var _createClass2=__webpack_require__(22);var _createClass3=_interopRequireDefault(_createClass2);var _uuid=__webpack_require__(143);var _uuid2=_interopRequireDefault(_uuid);var _errors=__webpack_require__(146);var _errors2=_interopRequireDefault(_errors);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var JSONRPCVersion="2.0";var JSONRPC=function(){function JSONRPC(dispatcher){var methods=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};(0,_classCallCheck3.default)(this,JSONRPC);this.version=JSONRPCVersion;this.deferreds={};this.methods=methods;this.dispatcher=dispatcher}(0,_createClass3.default)(JSONRPC,[{key:"send",value:function send(message){var data=(0,_extends3.default)({},message);data.jsonrpc=this.version;this.dispatcher(data)}},{key:"notification",value:function notification(method){var params=arguments.length>1&&arguments[1]!==undefined?arguments[1]:[];this.send({method:method,params:params})}},{key:"request",value:function request(method){var _this=this;var params=arguments.length>1&&arguments[1]!==undefined?arguments[1]:[];return new _promise2.default(function(resolve,reject){var id=_uuid2.default.v4();_this.deferreds[id]={resolve:resolve,reject:reject};_this.send({id:id,method:method,params:params})})}},{key:"handle",value:function handle(message){if(message.method){if(message.id){this.handleRequest(message)}else{this.handleNotification(message)}}else if(message.id){this.handleResponse(message)}}},{key:"handleResponse",value:function handleResponse(response){var deferred=this.deferreds[response.id];if(deferred===undefined){return}if(response.error){deferred.reject(response.error)}else{deferred.resolve(response.result)}delete this.deferreds[response.id]}},{key:"handleRequest",value:function handleRequest(request){var _this2=this;var method=this.methods[request.method];if(typeof method!=="function"){var error={message:"The method "+method+" was not found.",code:_errors2.default.METHOD_NOT_FOUND};this.send({id:request.id,error:error});return}method.apply(request,request.params).then(function(result){_this2.send({id:request.id,result:result})}).catch(function(message){var error={message:message,code:_errors2.default.INTERNAL_ERROR};_this2.send({id:request.id,error:error})})}},{key:"handleNotification",value:function handleNotification(request){var method=this.methods[request.method];if(method&&typeof method==="function"){method.apply(request,request.params)}}}]);return JSONRPC}();exports.default=(0,_freeze2.default)(JSONRPC)},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(141),__esModule:true}},function(module,exports){var getRandomValues=typeof crypto!="undefined"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto!="undefined"&&typeof window.msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto);if(getRandomValues){var rnds8=new Uint8Array(16);module.exports=function whatwgRNG(){getRandomValues(rnds8);return rnds8}}else{var rnds=new Array(16);module.exports=function mathRNG(){for(var i=0,r;i<16;i++){if((i&3)===0)r=Math.random()*4294967296;rnds[i]=r>>>((i&3)<<3)&255}return rnds}}},function(module,exports){var byteToHex=[];for(var i=0;i<256;++i){byteToHex[i]=(i+256).toString(16).substr(1)}function bytesToUuid(buf,offset){var i=offset||0;var bth=byteToHex;return[bth[buf[i++]],bth[buf[i++]],bth[buf[i++]],bth[buf[i++]],"-",bth[buf[i++]],bth[buf[i++]],"-",bth[buf[i++]],bth[buf[i++]],"-",bth[buf[i++]],bth[buf[i++]],"-",bth[buf[i++]],bth[buf[i++]],bth[buf[i++]],bth[buf[i++]],bth[buf[i++]],bth[buf[i++]]].join("")}module.exports=bytesToUuid},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var _classCallCheck2=__webpack_require__(14);var _classCallCheck3=_interopRequireDefault(_classCallCheck2);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var URI=function URI(uri){(0,_classCallCheck3.default)(this,URI);var a=document.createElement("a");a.href=uri;this.protocol=a.protocol;this.pathname=a.pathname;var portMatch=this.protocol==="http:"?/(:80)$/:/(:443)$/;this.host=a.host.replace(portMatch,"");this.origin=this.protocol+"//"+this.host};exports.default=URI},function(module,exports,__webpack_require__){"use strict";exports.__esModule=true;var _from=__webpack_require__(150);var _from2=_interopRequireDefault(_from);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}exports.default=function(arr){if(Array.isArray(arr)){for(var i=0,arr2=Array(arr.length);i<arr.length;i++){arr2[i]=arr[i]}return arr2}else{return(0,_from2.default)(arr)}}},function(module,exports,__webpack_require__){module.exports=__webpack_require__(78)},function(module,exports,__webpack_require__){__webpack_require__(79);__webpack_require__(80)},function(module,exports){},function(module,exports,__webpack_require__){"use strict";var _cernerSmartEmbeddableLib=__webpack_require__(81);var _cernerSmartEmbeddableLib2=_interopRequireDefault(_cernerSmartEmbeddableLib);var _comOverrider=__webpack_require__(157);var _comOverrider2=_interopRequireDefault(_comOverrider);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}_cernerSmartEmbeddableLib2.default.init();_cernerSmartEmbeddableLib2.default.listenForCustomFrameHeight();window.CernerSmartEmbeddableLib=window.CernerSmartEmbeddableLib||{};window.CernerSmartEmbeddableLib.calcFrameHeight=_cernerSmartEmbeddableLib2.default.calcFrameHeight;window.CernerSmartEmbeddableLib.setFrameHeight=_cernerSmartEmbeddableLib2.default.setFrameHeight;_comOverrider2.default.override(_cernerSmartEmbeddableLib2.default)},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var _xfc=__webpack_require__(82);var CernerSmartEmbeddableLib={init:function init(){_xfc.Provider.init({acls:["https://embedded.cerner.com","https://embedded.sandboxcerner.com","https://embedded.devcerner.com","https://embedded.applications.ca.cerner.com","https://embedded.ca.cernerpowerchart.net","https://embedded.applications.au.cerner.com","https://embedded.au.cernerpowerchart.net","https://embedded.emea-2.cerner.com","https://embedded.applications.uae-1.cerner.com"]})},calcFrameHeight:function calcFrameHeight(){return window.document.getElementsByTagName("html")[0].scrollHeight},setFrameHeight:function setFrameHeight(h){_xfc.Provider.trigger("iframeCustomResizer",{height:h})},listenForCustomFrameHeight:function listenForCustomFrameHeight(){_xfc.Provider.on("iframeCustomResizer",function(){var height=window.CernerSmartEmbeddableLib.calcFrameHeight()+"px";CernerSmartEmbeddableLib.setFrameHeight(height)})},invokeAPI:function invokeAPI(apiName,params){if(apiName&&params){_xfc.Provider.trigger("invokeCOMApi",{name:apiName,params:params})}}};exports.default=CernerSmartEmbeddableLib},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.Consumer=exports.Provider=undefined;var _consumer=__webpack_require__(83);var _consumer2=_interopRequireDefault(_consumer);var _provider=__webpack_require__(147);var _provider2=_interopRequireDefault(_provider);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}exports.Provider=_provider2.default;exports.Consumer=_consumer2.default},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var _consumer=__webpack_require__(84);var _consumer2=_interopRequireDefault(_consumer);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}exports.default=new _consumer2.default},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var _slicedToArray2=__webpack_require__(44);var _slicedToArray3=_interopRequireDefault(_slicedToArray2);var _entries=__webpack_require__(52);var _entries2=_interopRequireDefault(_entries);var _classCallCheck2=__webpack_require__(14);var _classCallCheck3=_interopRequireDefault(_classCallCheck2);var _createClass2=__webpack_require__(22);var _createClass3=_interopRequireDefault(_createClass2);var _frame=__webpack_require__(105);var _frame2=_interopRequireDefault(_frame);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var Consumer=function(){function Consumer(){(0,_classCallCheck3.default)(this,Consumer)}(0,_createClass3.default)(Consumer,[{key:"init",value:function init(){var globalHandlers=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};this.globalHandlers=globalHandlers}},{key:"mount",value:function mount(container,source){var options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};var frame=new _frame2.default;frame.init(container,source,options);(0,_entries2.default)(this.globalHandlers).forEach(function(_ref){var _ref2=(0,_slicedToArray3.default)(_ref,2),event=_ref2[0],handler=_ref2[1];var handlersArray=[].concat(handler);handlersArray.forEach(function(eventHandler){if(typeof eventHandler==="function"){frame.on(event,eventHandler)}})});frame.mount();return frame}}]);return Consumer}();exports.default=Consumer},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(86),__esModule:true}},function(module,exports,__webpack_require__){__webpack_require__(23);__webpack_require__(20);module.exports=__webpack_require__(95)},function(module,exports,__webpack_require__){"use strict";var addToUnscopables=__webpack_require__(88);var step=__webpack_require__(89);var Iterators=__webpack_require__(11);var toIObject=__webpack_require__(8);module.exports=__webpack_require__(46)(Array,"Array",function(iterated,kind){this._t=toIObject(iterated);this._i=0;this._k=kind},function(){var O=this._t;var kind=this._k;var index=this._i++;if(!O||index>=O.length){this._t=undefined;return step(1)}if(kind=="keys")return step(0,index);if(kind=="values")return step(0,O[index]);return step(0,[index,O[index]])},"values");Iterators.Arguments=Iterators.Array;addToUnscopables("keys");addToUnscopables("values");addToUnscopables("entries")},function(module,exports){module.exports=function(){}},function(module,exports){module.exports=function(done,value){return{value:value,done:!!done}}},function(module,exports,__webpack_require__){"use strict";var create=__webpack_require__(31);var descriptor=__webpack_require__(17);var setToStringTag=__webpack_require__(26);var IteratorPrototype={};__webpack_require__(9)(IteratorPrototype,__webpack_require__(2)("iterator"),function(){return this});module.exports=function(Constructor,NAME,next){Constructor.prototype=create(IteratorPrototype,{next:descriptor(1,next)});setToStringTag(Constructor,NAME+" Iterator")}},function(module,exports,__webpack_require__){var dP=__webpack_require__(6);var anObject=__webpack_require__(4);var getKeys=__webpack_require__(18);module.exports=__webpack_require__(5)?Object.defineProperties:function defineProperties(O,Properties){anObject(O);var keys=getKeys(Properties);var length=keys.length;var i=0;var P;while(length>i)dP.f(O,P=keys[i++],Properties[P]);return O}},function(module,exports,__webpack_require__){var toIObject=__webpack_require__(8);var toLength=__webpack_require__(32);var toAbsoluteIndex=__webpack_require__(93);module.exports=function(IS_INCLUDES){return function($this,el,fromIndex){var O=toIObject($this);var length=toLength(O.length);var index=toAbsoluteIndex(fromIndex,length);var value;if(IS_INCLUDES&&el!=el)while(length>index){value=O[index++];if(value!=value)return true}else for(;length>index;index++)if(IS_INCLUDES||index in O){if(O[index]===el)return IS_INCLUDES||index||0}return!IS_INCLUDES&&-1}}},function(module,exports,__webpack_require__){"use strict";var toInteger=__webpack_require__(33);var max=Math.max;var min=Math.min;module.exports=function(index,length){index=toInteger(index);return index<0?max(index+length,0):min(index,length)}},function(module,exports,__webpack_require__){var toInteger=__webpack_require__(33);var defined=__webpack_require__(28);module.exports=function(TO_STRING){return function(that,pos){var s=String(defined(that));var i=toInteger(pos);var l=s.length;var a,b;if(i<0||i>=l)return TO_STRING?"":undefined;a=s.charCodeAt(i);return a<55296||a>56319||i+1===l||(b=s.charCodeAt(i+1))<56320||b>57343?TO_STRING?s.charAt(i):a:TO_STRING?s.slice(i,i+2):(a-55296<<10)+(b-56320)+65536}}},function(module,exports,__webpack_require__){var classof=__webpack_require__(37);var ITERATOR=__webpack_require__(2)("iterator");var Iterators=__webpack_require__(11);module.exports=__webpack_require__(0).isIterable=function(it){var O=Object(it);return O[ITERATOR]!==undefined||"@@iterator"in O||Iterators.hasOwnProperty(classof(O))}},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(97),__esModule:true}},function(module,exports,__webpack_require__){__webpack_require__(23);__webpack_require__(20);module.exports=__webpack_require__(98)},function(module,exports,__webpack_require__){var anObject=__webpack_require__(4);var get=__webpack_require__(38);module.exports=__webpack_require__(0).getIterator=function(it){var iterFn=get(it);if(typeof iterFn!="function")throw TypeError(it+" is not iterable!");return anObject(iterFn.call(it))}},function(module,exports,__webpack_require__){__webpack_require__(100);module.exports=__webpack_require__(0).Object.entries},function(module,exports,__webpack_require__){var $export=__webpack_require__(3);var $entries=__webpack_require__(101)(true);$export($export.S,"Object",{entries:function entries(it){return $entries(it)}})},function(module,exports,__webpack_require__){var DESCRIPTORS=__webpack_require__(5);var getKeys=__webpack_require__(18);var toIObject=__webpack_require__(8);var isEnum=__webpack_require__(21).f;module.exports=function(isEntries){return function(it){var O=toIObject(it);var keys=getKeys(O);var length=keys.length;var i=0;var result=[];var key;while(length>i){key=keys[i++];if(!DESCRIPTORS||isEnum.call(O,key)){result.push(isEntries?[key,O[key]]:O[key])}}return result}}},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(103),__esModule:true}},function(module,exports,__webpack_require__){__webpack_require__(104);var $Object=__webpack_require__(0).Object;module.exports=function defineProperty(it,key,desc){return $Object.defineProperty(it,key,desc)}},function(module,exports,__webpack_require__){var $export=__webpack_require__(3);$export($export.S+$export.F*!__webpack_require__(5),"Object",{defineProperty:__webpack_require__(6).f})},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var _slicedToArray2=__webpack_require__(44);var _slicedToArray3=_interopRequireDefault(_slicedToArray2);var _entries=__webpack_require__(52);var _entries2=_interopRequireDefault(_entries);var _extends2=__webpack_require__(53);var _extends3=_interopRequireDefault(_extends2);var _promise=__webpack_require__(40);var _promise2=_interopRequireDefault(_promise);var _getPrototypeOf=__webpack_require__(62);var _getPrototypeOf2=_interopRequireDefault(_getPrototypeOf);var _classCallCheck2=__webpack_require__(14);var _classCallCheck3=_interopRequireDefault(_classCallCheck2);var _createClass2=__webpack_require__(22);var _createClass3=_interopRequireDefault(_createClass2);var _possibleConstructorReturn2=__webpack_require__(64);var _possibleConstructorReturn3=_interopRequireDefault(_possibleConstructorReturn2);var _inherits2=__webpack_require__(69);var _inherits3=_interopRequireDefault(_inherits2);var _events=__webpack_require__(70);var _logger=__webpack_require__(27);var _logger2=_interopRequireDefault(_logger);var _jsonrpcDispatch=__webpack_require__(71);var _jsonrpcDispatch2=_interopRequireDefault(_jsonrpcDispatch);var _uri=__webpack_require__(75);var _uri2=_interopRequireDefault(_uri);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var Frame=function(_EventEmitter){(0,_inherits3.default)(Frame,_EventEmitter);function Frame(props){(0,_classCallCheck3.default)(this,Frame);var _this=(0,_possibleConstructorReturn3.default)(this,(Frame.__proto__||(0,_getPrototypeOf2.default)(Frame)).call(this,props));_this.handleProviderMessage=_this.handleProviderMessage.bind(_this);_this.initIframeResizer=_this.initIframeResizer.bind(_this);_this.send=_this.send.bind(_this);_this.cleanup=_this.cleanup.bind(_this);_this.load=_this.load.bind(_this);return _this}(0,_createClass3.default)(Frame,[{key:"init",value:function init(container,source){var _ref=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{},_ref$secret=_ref.secret,secret=_ref$secret===undefined?null:_ref$secret,_ref$resizeConfig=_ref.resizeConfig,resizeConfig=_ref$resizeConfig===undefined?{}:_ref$resizeConfig,_ref$iframeAttrs=_ref.iframeAttrs,iframeAttrs=_ref$iframeAttrs===undefined?{}:_ref$iframeAttrs;this.source=source;this.container=container;this.iframe=null;this.iframeAttrs=iframeAttrs;this.wrapper=null;this.origin=new _uri2.default(this.source).origin;this.secret=secret;this.resizeConfig=resizeConfig;var self=this;this.JSONRPC=new _jsonrpcDispatch2.default(self.send,{launch:function launch(){self.wrapper.setAttribute("data-status","launched");self.emit("xfc.launched");return _promise2.default.resolve()},authorized:function authorized(){var detail=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};self.wrapper.setAttribute("data-status","authorized");self.emit("xfc.authorized",detail);self.initIframeResizer();return _promise2.default.resolve()},unload:function unload(){var detail=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};self.wrapper.setAttribute("data-status","unloaded");self.emit("xfc.unload",detail);return _promise2.default.resolve()},resize:function resize(){var height=arguments.length>0&&arguments[0]!==undefined?arguments[0]:null;var width=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;if(typeof resizeConfig.customCalculationMethod==="function"){resizeConfig.customCalculationMethod.call(self.iframe);return _promise2.default.resolve()}if(height){self.iframe.style.height=height}if(width){self.iframe.style.width=width}return _promise2.default.resolve()},event:function event(_event,detail){self.emit(_event,detail);return _promise2.default.resolve()},authorizeConsumer:function authorizeConsumer(){return _promise2.default.resolve("hello")},challengeConsumer:function challengeConsumer(){return _promise2.default.resolve(self.secret)},loadPage:function loadPage(url){self.load(url);return _promise2.default.resolve()}})}},{key:"initIframeResizer",value:function initIframeResizer(){var config=this.resizeConfig;if(config.fixedHeight||config.fixedWidth){if(config.fixedHeight){this.iframe.style.height=config.fixedHeight}if(config.fixedWidth){this.iframe.style.width=config.fixedWidth}}else{if(typeof config.customCalculationMethod==="function"){config=(0,_extends3.default)({},config);config.customCal=true;delete config.customCalculationMethod}this.JSONRPC.notification("resize",[config])}}},{key:"mount",value:function mount(){if(this.iframe)return;window.addEventListener("message",this.handleProviderMessage);this.wrapper=document.createElement("div");this.wrapper.className="xfc";this.wrapper.setAttribute("data-status","mounted");this.container.appendChild(this.wrapper);var iframe=document.createElement("iframe");iframe.src=this.source;if(!this.resizeConfig.scrolling){iframe.style.overflow="hidden";iframe.scrolling="no"}(0,_entries2.default)(this.iframeAttrs).forEach(function(_ref2){var _ref3=(0,_slicedToArray3.default)(_ref2,2),key=_ref3[0],value=_ref3[1];iframe.setAttribute(key,value)});this.iframe=iframe;this.wrapper.appendChild(iframe);this.emit("xfc.mounted")}},{key:"unmount",value:function unmount(){if(this.wrapper.parentNode===this.container){this.container.removeChild(this.wrapper);this.emit("xfc.unmounted");this.cleanup()}}},{key:"cleanup",value:function cleanup(){window.removeEventListener("message",this.handleProviderMessage);this.iframe=null;this.wrapper=null}},{key:"load",value:function load(url){this.origin=new _uri2.default(url).origin;this.source=url;this.wrapper.setAttribute("data-status","mounted");this.iframe.src=url}},{key:"handleProviderMessage",value:function handleProviderMessage(event){if(!event.data.jsonrpc||!this.iframe)return;if(this.iframe.contentWindow!==event.source)return;this.origin=event.origin||event.originalEvent.origin;_logger2.default.log("<< consumer",event.origin,event.data);this.JSONRPC.handle(event.data)}},{key:"send",value:function send(message){if(message){_logger2.default.log(">> consumer",this.origin,message);this.iframe.contentWindow.postMessage(message,this.origin)}}},{key:"trigger",value:function trigger(event,detail){this.JSONRPC.notification("event",[event,detail])}}]);return Frame}(_events.EventEmitter);exports.default=Frame},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(107),__esModule:true}},function(module,exports,__webpack_require__){__webpack_require__(108);module.exports=__webpack_require__(0).Object.assign},function(module,exports,__webpack_require__){var $export=__webpack_require__(3);$export($export.S+$export.F,"Object",{assign:__webpack_require__(109)})},function(module,exports,__webpack_require__){"use strict";var DESCRIPTORS=__webpack_require__(5);var getKeys=__webpack_require__(18);var gOPS=__webpack_require__(39);var pIE=__webpack_require__(21);var toObject=__webpack_require__(19);var IObject=__webpack_require__(45);var $assign=Object.assign;module.exports=!$assign||__webpack_require__(13)(function(){var A={};var B={};var S=Symbol();var K="abcdefghijklmnopqrst";A[S]=7;K.split("").forEach(function(k){B[k]=k});return $assign({},A)[S]!=7||Object.keys($assign({},B)).join("")!=K})?function assign(target,source){var T=toObject(target);var aLen=arguments.length;var index=1;var getSymbols=gOPS.f;var isEnum=pIE.f;while(aLen>index){var S=IObject(arguments[index++]);var keys=getSymbols?getKeys(S).concat(getSymbols(S)):getKeys(S);var length=keys.length;var j=0;var key;while(length>j){key=keys[j++];if(!DESCRIPTORS||isEnum.call(S,key))T[key]=S[key]}}return T}:$assign},function(module,exports,__webpack_require__){__webpack_require__(54);__webpack_require__(20);__webpack_require__(23);__webpack_require__(111);__webpack_require__(119);__webpack_require__(120);module.exports=__webpack_require__(0).Promise},function(module,exports,__webpack_require__){"use strict";var LIBRARY=__webpack_require__(16);var global=__webpack_require__(1);var ctx=__webpack_require__(12);var classof=__webpack_require__(37);var $export=__webpack_require__(3);var isObject=__webpack_require__(7);var aFunction=__webpack_require__(24);var anInstance=__webpack_require__(112);var forOf=__webpack_require__(113);var speciesConstructor=__webpack_require__(57);var task=__webpack_require__(58).set;var microtask=__webpack_require__(115)();var newPromiseCapabilityModule=__webpack_require__(41);var perform=__webpack_require__(59);var userAgent=__webpack_require__(116);var promiseResolve=__webpack_require__(60);var PROMISE="Promise";var TypeError=global.TypeError;var process=global.process;var versions=process&&process.versions;var v8=versions&&versions.v8||"";var $Promise=global[PROMISE];var isNode=classof(process)=="process";var empty=function(){};var Internal,newGenericPromiseCapability,OwnPromiseCapability,Wrapper;var newPromiseCapability=newGenericPromiseCapability=newPromiseCapabilityModule.f;var USE_NATIVE=!!function(){try{var promise=$Promise.resolve(1);var FakePromise=(promise.constructor={})[__webpack_require__(2)("species")]=function(exec){exec(empty,empty)};return(isNode||typeof PromiseRejectionEvent=="function")&&promise.then(empty)instanceof FakePromise&&v8.indexOf("6.6")!==0&&userAgent.indexOf("Chrome/66")===-1}catch(e){}}();var isThenable=function(it){var then;return isObject(it)&&typeof(then=it.then)=="function"?then:false};var notify=function(promise,isReject){if(promise._n)return;promise._n=true;var chain=promise._c;microtask(function(){var value=promise._v;var ok=promise._s==1;var i=0;var run=function(reaction){var handler=ok?reaction.ok:reaction.fail;var resolve=reaction.resolve;var reject=reaction.reject;var domain=reaction.domain;var result,then,exited;try{if(handler){if(!ok){if(promise._h==2)onHandleUnhandled(promise);promise._h=1}if(handler===true)result=value;else{if(domain)domain.enter();result=handler(value);if(domain){domain.exit();exited=true}}if(result===reaction.promise){reject(TypeError("Promise-chain cycle"))}else if(then=isThenable(result)){then.call(result,resolve,reject)}else resolve(result)}else reject(value)}catch(e){if(domain&&!exited)domain.exit();reject(e)}};while(chain.length>i)run(chain[i++]);promise._c=[];promise._n=false;if(isReject&&!promise._h)onUnhandled(promise)})};var onUnhandled=function(promise){task.call(global,function(){var value=promise._v;var unhandled=isUnhandled(promise);var result,handler,console;if(unhandled){result=perform(function(){if(isNode){process.emit("unhandledRejection",value,promise)}else if(handler=global.onunhandledrejection){handler({promise:promise,reason:value})}else if((console=global.console)&&console.error){console.error("Unhandled promise rejection",value)}});promise._h=isNode||isUnhandled(promise)?2:1}promise._a=undefined;if(unhandled&&result.e)throw result.v})};var isUnhandled=function(promise){return promise._h!==1&&(promise._a||promise._c).length===0};var onHandleUnhandled=function(promise){task.call(global,function(){var handler;if(isNode){process.emit("rejectionHandled",promise)}else if(handler=global.onrejectionhandled){handler({promise:promise,reason:promise._v})}})};var $reject=function(value){var promise=this;if(promise._d)return;promise._d=true;promise=promise._w||promise;promise._v=value;promise._s=2;if(!promise._a)promise._a=promise._c.slice();notify(promise,true)};var $resolve=function(value){var promise=this;var then;if(promise._d)return;promise._d=true;promise=promise._w||promise;try{if(promise===value)throw TypeError("Promise can't be resolved itself");if(then=isThenable(value)){microtask(function(){var wrapper={_w:promise,_d:false};try{then.call(value,ctx($resolve,wrapper,1),ctx($reject,wrapper,1))}catch(e){$reject.call(wrapper,e)}})}else{promise._v=value;promise._s=1;notify(promise,false)}}catch(e){$reject.call({_w:promise,_d:false},e)}};if(!USE_NATIVE){$Promise=function Promise(executor){anInstance(this,$Promise,PROMISE,"_h");aFunction(executor);Internal.call(this);try{executor(ctx($resolve,this,1),ctx($reject,this,1))}catch(err){$reject.call(this,err)}};Internal=function Promise(executor){this._c=[];this._a=undefined;this._s=0;this._d=false;this._v=undefined;this._h=0;this._n=false};Internal.prototype=__webpack_require__(117)($Promise.prototype,{then:function then(onFulfilled,onRejected){var reaction=newPromiseCapability(speciesConstructor(this,$Promise));reaction.ok=typeof onFulfilled=="function"?onFulfilled:true;reaction.fail=typeof onRejected=="function"&&onRejected;reaction.domain=isNode?process.domain:undefined;this._c.push(reaction);if(this._a)this._a.push(reaction);if(this._s)notify(this,false);return reaction.promise},catch:function(onRejected){return this.then(undefined,onRejected)}});OwnPromiseCapability=function(){var promise=new Internal;this.promise=promise;this.resolve=ctx($resolve,promise,1);this.reject=ctx($reject,promise,1)};newPromiseCapabilityModule.f=newPromiseCapability=function(C){return C===$Promise||C===Wrapper?new OwnPromiseCapability(C):newGenericPromiseCapability(C)}}$export($export.G+$export.W+$export.F*!USE_NATIVE,{Promise:$Promise});__webpack_require__(26)($Promise,PROMISE);__webpack_require__(118)(PROMISE);Wrapper=__webpack_require__(0)[PROMISE];$export($export.S+$export.F*!USE_NATIVE,PROMISE,{reject:function reject(r){var capability=newPromiseCapability(this);var $$reject=capability.reject;$$reject(r);return capability.promise}});$export($export.S+$export.F*(LIBRARY||!USE_NATIVE),PROMISE,{resolve:function resolve(x){return promiseResolve(LIBRARY&&this===Wrapper?$Promise:this,x)}});$export($export.S+$export.F*!(USE_NATIVE&&__webpack_require__(61)(function(iter){$Promise.all(iter)["catch"](empty)})),PROMISE,{all:function all(iterable){var C=this;var capability=newPromiseCapability(C);var resolve=capability.resolve;var reject=capability.reject;var result=perform(function(){var values=[];var index=0;var remaining=1;forOf(iterable,false,function(promise){var $index=index++;var alreadyCalled=false;values.push(undefined);remaining++;C.resolve(promise).then(function(value){if(alreadyCalled)return;alreadyCalled=true;values[$index]=value;--remaining||resolve(values)},reject)});--remaining||resolve(values)});if(result.e)reject(result.v);return capability.promise},race:function race(iterable){var C=this;var capability=newPromiseCapability(C);var reject=capability.reject;var result=perform(function(){forOf(iterable,false,function(promise){C.resolve(promise).then(capability.resolve,reject)})});if(result.e)reject(result.v);return capability.promise}})},function(module,exports){module.exports=function(it,Constructor,name,forbiddenField){if(!(it instanceof Constructor)||forbiddenField!==undefined&&forbiddenField in it){throw TypeError(name+": incorrect invocation!")}return it}},function(module,exports,__webpack_require__){var ctx=__webpack_require__(12);var call=__webpack_require__(55);var isArrayIter=__webpack_require__(56);var anObject=__webpack_require__(4);var toLength=__webpack_require__(32);var getIterFn=__webpack_require__(38);var BREAK={};var RETURN={};var exports=module.exports=function(iterable,entries,fn,that,ITERATOR){var iterFn=ITERATOR?function(){return iterable}:getIterFn(iterable);var f=ctx(fn,that,entries?2:1);var index=0;var length,step,iterator,result;if(typeof iterFn!="function")throw TypeError(iterable+" is not iterable!");if(isArrayIter(iterFn))for(length=toLength(iterable.length);length>index;index++){result=entries?f(anObject(step=iterable[index])[0],step[1]):f(iterable[index]);if(result===BREAK||result===RETURN)return result}else for(iterator=iterFn.call(iterable);!(step=iterator.next()).done;){result=call(iterator,f,step.value,entries);if(result===BREAK||result===RETURN)return result}};exports.BREAK=BREAK;exports.RETURN=RETURN},function(module,exports){module.exports=function(fn,args,that){var un=that===undefined;switch(args.length){case 0:return un?fn():fn.call(that);case 1:return un?fn(args[0]):fn.call(that,args[0]);case 2:return un?fn(args[0],args[1]):fn.call(that,args[0],args[1]);case 3:return un?fn(args[0],args[1],args[2]):fn.call(that,args[0],args[1],args[2]);case 4:return un?fn(args[0],args[1],args[2],args[3]):fn.call(that,args[0],args[1],args[2],args[3])}return fn.apply(that,args)}},function(module,exports,__webpack_require__){var global=__webpack_require__(1);var macrotask=__webpack_require__(58).set;var Observer=global.MutationObserver||global.WebKitMutationObserver;var process=global.process;var Promise=global.Promise;var isNode=__webpack_require__(15)(process)=="process";module.exports=function(){var head,last,notify;var flush=function(){var parent,fn;if(isNode&&(parent=process.domain))parent.exit();while(head){fn=head.fn;head=head.next;try{fn()}catch(e){if(head)notify();else last=undefined;throw e}}last=undefined;if(parent)parent.enter()};if(isNode){notify=function(){process.nextTick(flush)}}else if(Observer&&!(global.navigator&&global.navigator.standalone)){var toggle=true;var node=document.createTextNode("");new Observer(flush).observe(node,{characterData:true});notify=function(){node.data=toggle=!toggle}}else if(Promise&&Promise.resolve){var promise=Promise.resolve(undefined);notify=function(){promise.then(flush)}}else{notify=function(){macrotask.call(global,flush)}}return function(fn){var task={fn:fn,next:undefined};if(last)last.next=task;if(!head){head=task;notify()}last=task}}},function(module,exports,__webpack_require__){var global=__webpack_require__(1);var navigator=global.navigator;module.exports=navigator&&navigator.userAgent||""},function(module,exports,__webpack_require__){var hide=__webpack_require__(9);module.exports=function(target,src,safe){for(var key in src){if(safe&&target[key])target[key]=src[key];else hide(target,key,src[key])}return target}},function(module,exports,__webpack_require__){"use strict";var global=__webpack_require__(1);var core=__webpack_require__(0);var dP=__webpack_require__(6);var DESCRIPTORS=__webpack_require__(5);var SPECIES=__webpack_require__(2)("species");module.exports=function(KEY){var C=typeof core[KEY]=="function"?core[KEY]:global[KEY];if(DESCRIPTORS&&C&&!C[SPECIES])dP.f(C,SPECIES,{configurable:true,get:function(){return this}})}},function(module,exports,__webpack_require__){"use strict";var $export=__webpack_require__(3);var core=__webpack_require__(0);var global=__webpack_require__(1);var speciesConstructor=__webpack_require__(57);var promiseResolve=__webpack_require__(60);$export($export.P+$export.R,"Promise",{finally:function(onFinally){var C=speciesConstructor(this,core.Promise||global.Promise);var isFunction=typeof onFinally=="function";return this.then(isFunction?function(x){return promiseResolve(C,onFinally()).then(function(){return x})}:onFinally,isFunction?function(e){return promiseResolve(C,onFinally()).then(function(){throw e})}:onFinally)}})},function(module,exports,__webpack_require__){"use strict";var $export=__webpack_require__(3);var newPromiseCapability=__webpack_require__(41);var perform=__webpack_require__(59);$export($export.S,"Promise",{try:function(callbackfn){var promiseCapability=newPromiseCapability.f(this);var result=perform(callbackfn);(result.e?promiseCapability.reject:promiseCapability.resolve)(result.v);return promiseCapability.promise}})},function(module,exports,__webpack_require__){__webpack_require__(122);module.exports=__webpack_require__(0).Object.getPrototypeOf},function(module,exports,__webpack_require__){var toObject=__webpack_require__(19);var $getPrototypeOf=__webpack_require__(51);__webpack_require__(63)("getPrototypeOf",function(){return function getPrototypeOf(it){return $getPrototypeOf(toObject(it))}})},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(124),__esModule:true}},function(module,exports,__webpack_require__){__webpack_require__(20);__webpack_require__(23);module.exports=__webpack_require__(42).f("iterator")},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(126),__esModule:true}},function(module,exports,__webpack_require__){"use strict";__webpack_require__(127);__webpack_require__(54);__webpack_require__(131);__webpack_require__(132);module.exports=__webpack_require__(0).Symbol},function(module,exports,__webpack_require__){"use strict";var global=__webpack_require__(1);var has=__webpack_require__(10);var DESCRIPTORS=__webpack_require__(5);var $export=__webpack_require__(3);var redefine=__webpack_require__(48);var META=__webpack_require__(66).KEY;var $fails=__webpack_require__(13);var shared=__webpack_require__(35);var setToStringTag=__webpack_require__(26);var uid=__webpack_require__(25);var wks=__webpack_require__(2);var wksExt=__webpack_require__(42);var wksDefine=__webpack_require__(43);var enumKeys=__webpack_require__(128);var isArray=__webpack_require__(129);var anObject=__webpack_require__(4);var isObject=__webpack_require__(7);var toObject=__webpack_require__(19);var toIObject=__webpack_require__(8);var toPrimitive=__webpack_require__(30);var createDesc=__webpack_require__(17);var _create=__webpack_require__(31);var gOPNExt=__webpack_require__(130);var $GOPD=__webpack_require__(68);var $GOPS=__webpack_require__(39);var $DP=__webpack_require__(6);var $keys=__webpack_require__(18);var gOPD=$GOPD.f;var dP=$DP.f;var gOPN=gOPNExt.f;var $Symbol=global.Symbol;var $JSON=global.JSON;var _stringify=$JSON&&$JSON.stringify;var PROTOTYPE="prototype";var HIDDEN=wks("_hidden");var TO_PRIMITIVE=wks("toPrimitive");var isEnum={}.propertyIsEnumerable;var SymbolRegistry=shared("symbol-registry");var AllSymbols=shared("symbols");var OPSymbols=shared("op-symbols");var ObjectProto=Object[PROTOTYPE];var USE_NATIVE=typeof $Symbol=="function"&&!!$GOPS.f;var QObject=global.QObject;var setter=!QObject||!QObject[PROTOTYPE]||!QObject[PROTOTYPE].findChild;var setSymbolDesc=DESCRIPTORS&&$fails(function(){return _create(dP({},"a",{get:function(){return dP(this,"a",{value:7}).a}})).a!=7})?function(it,key,D){var protoDesc=gOPD(ObjectProto,key);if(protoDesc)delete ObjectProto[key];dP(it,key,D);if(protoDesc&&it!==ObjectProto)dP(ObjectProto,key,protoDesc)}:dP;var wrap=function(tag){var sym=AllSymbols[tag]=_create($Symbol[PROTOTYPE]);sym._k=tag;return sym};var isSymbol=USE_NATIVE&&typeof $Symbol.iterator=="symbol"?function(it){return typeof it=="symbol"}:function(it){return it instanceof $Symbol};var $defineProperty=function defineProperty(it,key,D){if(it===ObjectProto)$defineProperty(OPSymbols,key,D);anObject(it);key=toPrimitive(key,true);anObject(D);if(has(AllSymbols,key)){if(!D.enumerable){if(!has(it,HIDDEN))dP(it,HIDDEN,createDesc(1,{}));it[HIDDEN][key]=true}else{if(has(it,HIDDEN)&&it[HIDDEN][key])it[HIDDEN][key]=false;D=_create(D,{enumerable:createDesc(0,false)})}return setSymbolDesc(it,key,D)}return dP(it,key,D)};var $defineProperties=function defineProperties(it,P){anObject(it);var keys=enumKeys(P=toIObject(P));var i=0;var l=keys.length;var key;while(l>i)$defineProperty(it,key=keys[i++],P[key]);return it};var $create=function create(it,P){return P===undefined?_create(it):$defineProperties(_create(it),P)};var $propertyIsEnumerable=function propertyIsEnumerable(key){var E=isEnum.call(this,key=toPrimitive(key,true));if(this===ObjectProto&&has(AllSymbols,key)&&!has(OPSymbols,key))return false;return E||!has(this,key)||!has(AllSymbols,key)||has(this,HIDDEN)&&this[HIDDEN][key]?E:true};var $getOwnPropertyDescriptor=function getOwnPropertyDescriptor(it,key){it=toIObject(it);key=toPrimitive(key,true);if(it===ObjectProto&&has(AllSymbols,key)&&!has(OPSymbols,key))return;var D=gOPD(it,key);if(D&&has(AllSymbols,key)&&!(has(it,HIDDEN)&&it[HIDDEN][key]))D.enumerable=true;return D};var $getOwnPropertyNames=function getOwnPropertyNames(it){var names=gOPN(toIObject(it));var result=[];var i=0;var key;while(names.length>i){if(!has(AllSymbols,key=names[i++])&&key!=HIDDEN&&key!=META)result.push(key)}return result};var $getOwnPropertySymbols=function getOwnPropertySymbols(it){var IS_OP=it===ObjectProto;var names=gOPN(IS_OP?OPSymbols:toIObject(it));var result=[];var i=0;var key;while(names.length>i){if(has(AllSymbols,key=names[i++])&&(IS_OP?has(ObjectProto,key):true))result.push(AllSymbols[key])}return result};if(!USE_NATIVE){$Symbol=function Symbol(){if(this instanceof $Symbol)throw TypeError("Symbol is not a constructor!");var tag=uid(arguments.length>0?arguments[0]:undefined);var $set=function(value){if(this===ObjectProto)$set.call(OPSymbols,value);if(has(this,HIDDEN)&&has(this[HIDDEN],tag))this[HIDDEN][tag]=false;setSymbolDesc(this,tag,createDesc(1,value))};if(DESCRIPTORS&&setter)setSymbolDesc(ObjectProto,tag,{configurable:true,set:$set});return wrap(tag)};redefine($Symbol[PROTOTYPE],"toString",function toString(){return this._k});$GOPD.f=$getOwnPropertyDescriptor;$DP.f=$defineProperty;__webpack_require__(67).f=gOPNExt.f=$getOwnPropertyNames;__webpack_require__(21).f=$propertyIsEnumerable;$GOPS.f=$getOwnPropertySymbols;if(DESCRIPTORS&&!__webpack_require__(16)){redefine(ObjectProto,"propertyIsEnumerable",$propertyIsEnumerable,true)}wksExt.f=function(name){return wrap(wks(name))}}$export($export.G+$export.W+$export.F*!USE_NATIVE,{Symbol:$Symbol});for(var es6Symbols="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),j=0;es6Symbols.length>j;)wks(es6Symbols[j++]);for(var wellKnownSymbols=$keys(wks.store),k=0;wellKnownSymbols.length>k;)wksDefine(wellKnownSymbols[k++]);$export($export.S+$export.F*!USE_NATIVE,"Symbol",{for:function(key){return has(SymbolRegistry,key+="")?SymbolRegistry[key]:SymbolRegistry[key]=$Symbol(key)},keyFor:function keyFor(sym){if(!isSymbol(sym))throw TypeError(sym+" is not a symbol!");for(var key in SymbolRegistry)if(SymbolRegistry[key]===sym)return key},useSetter:function(){setter=true},useSimple:function(){setter=false}});$export($export.S+$export.F*!USE_NATIVE,"Object",{create:$create,defineProperty:$defineProperty,defineProperties:$defineProperties,getOwnPropertyDescriptor:$getOwnPropertyDescriptor,getOwnPropertyNames:$getOwnPropertyNames,getOwnPropertySymbols:$getOwnPropertySymbols});var FAILS_ON_PRIMITIVES=$fails(function(){$GOPS.f(1)});$export($export.S+$export.F*FAILS_ON_PRIMITIVES,"Object",{getOwnPropertySymbols:function getOwnPropertySymbols(it){return $GOPS.f(toObject(it))}});$JSON&&$export($export.S+$export.F*(!USE_NATIVE||$fails(function(){var S=$Symbol();return _stringify([S])!="[null]"||_stringify({a:S})!="{}"||_stringify(Object(S))!="{}"})),"JSON",{stringify:function stringify(it){var args=[it];var i=1;var replacer,$replacer;while(arguments.length>i)args.push(arguments[i++]);$replacer=replacer=args[1];if(!isObject(replacer)&&it===undefined||isSymbol(it))return;if(!isArray(replacer))replacer=function(key,value){if(typeof $replacer=="function")value=$replacer.call(this,key,value);if(!isSymbol(value))return value};args[1]=replacer;return _stringify.apply($JSON,args)}});$Symbol[PROTOTYPE][TO_PRIMITIVE]||__webpack_require__(9)($Symbol[PROTOTYPE],TO_PRIMITIVE,$Symbol[PROTOTYPE].valueOf);setToStringTag($Symbol,"Symbol");setToStringTag(Math,"Math",true);setToStringTag(global.JSON,"JSON",true)},function(module,exports,__webpack_require__){var getKeys=__webpack_require__(18);var gOPS=__webpack_require__(39);var pIE=__webpack_require__(21);module.exports=function(it){var result=getKeys(it);var getSymbols=gOPS.f;if(getSymbols){var symbols=getSymbols(it);var isEnum=pIE.f;var i=0;var key;while(symbols.length>i)if(isEnum.call(it,key=symbols[i++]))result.push(key)}return result}},function(module,exports,__webpack_require__){var cof=__webpack_require__(15);module.exports=Array.isArray||function isArray(arg){return cof(arg)=="Array"}},function(module,exports,__webpack_require__){var toIObject=__webpack_require__(8);var gOPN=__webpack_require__(67).f;var toString={}.toString;var windowNames=typeof window=="object"&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];var getWindowNames=function(it){try{return gOPN(it)}catch(e){return windowNames.slice()}};module.exports.f=function getOwnPropertyNames(it){return windowNames&&toString.call(it)=="[object Window]"?getWindowNames(it):gOPN(toIObject(it))}},function(module,exports,__webpack_require__){__webpack_require__(43)("asyncIterator")},function(module,exports,__webpack_require__){__webpack_require__(43)("observable")},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(134),__esModule:true}},function(module,exports,__webpack_require__){__webpack_require__(135);module.exports=__webpack_require__(0).Object.setPrototypeOf},function(module,exports,__webpack_require__){var $export=__webpack_require__(3);$export($export.S,"Object",{setPrototypeOf:__webpack_require__(136).set})},function(module,exports,__webpack_require__){var isObject=__webpack_require__(7);var anObject=__webpack_require__(4);var check=function(O,proto){anObject(O);if(!isObject(proto)&&proto!==null)throw TypeError(proto+": can't set as prototype!")};module.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(test,buggy,set){try{set=__webpack_require__(12)(Function.call,__webpack_require__(68).f(Object.prototype,"__proto__").set,2);set(test,[]);buggy=!(test instanceof Array)}catch(e){buggy=true}return function setPrototypeOf(O,proto){check(O,proto);if(buggy)O.__proto__=proto;else set(O,proto);return O}}({},false):undefined),check:check}},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(138),__esModule:true}},function(module,exports,__webpack_require__){__webpack_require__(139);var $Object=__webpack_require__(0).Object;module.exports=function create(P,D){return $Object.create(P,D)}},function(module,exports,__webpack_require__){var $export=__webpack_require__(3);$export($export.S,"Object",{create:__webpack_require__(31)})},function(module,exports){var process=module.exports={};var cachedSetTimeout;var cachedClearTimeout;function defaultSetTimout(){throw new Error("setTimeout has not been defined")}function defaultClearTimeout(){throw new Error("clearTimeout has not been defined")}(function(){try{if(typeof setTimeout==="function"){cachedSetTimeout=setTimeout}else{cachedSetTimeout=defaultSetTimout}}catch(e){cachedSetTimeout=defaultSetTimout}try{if(typeof clearTimeout==="function"){cachedClearTimeout=clearTimeout}else{cachedClearTimeout=defaultClearTimeout}}catch(e){cachedClearTimeout=defaultClearTimeout}})();function runTimeout(fun){if(cachedSetTimeout===setTimeout){return setTimeout(fun,0)}if((cachedSetTimeout===defaultSetTimout||!cachedSetTimeout)&&setTimeout){cachedSetTimeout=setTimeout;return setTimeout(fun,0)}try{return cachedSetTimeout(fun,0)}catch(e){try{return cachedSetTimeout.call(null,fun,0)}catch(e){return cachedSetTimeout.call(this,fun,0)}}}function runClearTimeout(marker){if(cachedClearTimeout===clearTimeout){return clearTimeout(marker)}if((cachedClearTimeout===defaultClearTimeout||!cachedClearTimeout)&&clearTimeout){cachedClearTimeout=clearTimeout;return clearTimeout(marker)}try{return cachedClearTimeout(marker)}catch(e){try{return cachedClearTimeout.call(null,marker)}catch(e){return cachedClearTimeout.call(this,marker)}}}var queue=[];var draining=false;var currentQueue;var queueIndex=-1;function cleanUpNextTick(){if(!draining||!currentQueue){return}draining=false;if(currentQueue.length){queue=currentQueue.concat(queue)}else{queueIndex=-1}if(queue.length){drainQueue()}}function drainQueue(){if(draining){return}var timeout=runTimeout(cleanUpNextTick);draining=true;var len=queue.length;while(len){currentQueue=queue;queue=[];while(++queueIndex<len){if(currentQueue){currentQueue[queueIndex].run()}}queueIndex=-1;len=queue.length}currentQueue=null;draining=false;runClearTimeout(timeout)}process.nextTick=function(fun){var args=new Array(arguments.length-1);if(arguments.length>1){for(var i=1;i<arguments.length;i++){args[i-1]=arguments[i]}}queue.push(new Item(fun,args));if(queue.length===1&&!draining){runTimeout(drainQueue)}};function Item(fun,array){this.fun=fun;this.array=array}Item.prototype.run=function(){this.fun.apply(null,this.array)};process.title="browser";process.browser=true;process.env={};process.argv=[];process.version="";process.versions={};function noop(){}process.on=noop;process.addListener=noop;process.once=noop;process.off=noop;process.removeListener=noop;process.removeAllListeners=noop;process.emit=noop;process.prependListener=noop;process.prependOnceListener=noop;process.listeners=function(name){return[]};process.binding=function(name){throw new Error("process.binding is not supported")};process.cwd=function(){return"/"};process.chdir=function(dir){throw new Error("process.chdir is not supported")};process.umask=function(){return 0}},function(module,exports,__webpack_require__){__webpack_require__(142);module.exports=__webpack_require__(0).Object.freeze},function(module,exports,__webpack_require__){var isObject=__webpack_require__(7);var meta=__webpack_require__(66).onFreeze;__webpack_require__(63)("freeze",function($freeze){return function freeze(it){return $freeze&&isObject(it)?$freeze(meta(it)):it}})},function(module,exports,__webpack_require__){"use strict";var v1=__webpack_require__(144);var v4=__webpack_require__(145);var uuid=v4;uuid.v1=v1;uuid.v4=v4;module.exports=uuid},function(module,exports,__webpack_require__){var rng=__webpack_require__(73);var bytesToUuid=__webpack_require__(74);var _nodeId;var _clockseq;var _lastMSecs=0;var _lastNSecs=0;function v1(options,buf,offset){var i=buf&&offset||0;var b=buf||[];options=options||{};var node=options.node||_nodeId;var clockseq=options.clockseq!==undefined?options.clockseq:_clockseq;if(node==null||clockseq==null){var seedBytes=rng();if(node==null){node=_nodeId=[seedBytes[0]|1,seedBytes[1],seedBytes[2],seedBytes[3],seedBytes[4],seedBytes[5]]}if(clockseq==null){clockseq=_clockseq=(seedBytes[6]<<8|seedBytes[7])&16383}}var msecs=options.msecs!==undefined?options.msecs:(new Date).getTime();var nsecs=options.nsecs!==undefined?options.nsecs:_lastNSecs+1;var dt=msecs-_lastMSecs+(nsecs-_lastNSecs)/1e4;if(dt<0&&options.clockseq===undefined){clockseq=clockseq+1&16383}if((dt<0||msecs>_lastMSecs)&&options.nsecs===undefined){nsecs=0}if(nsecs>=1e4){throw new Error("uuid.v1(): Can't create more than 10M uuids/sec")}_lastMSecs=msecs;_lastNSecs=nsecs;_clockseq=clockseq;msecs+=122192928e5;var tl=((msecs&268435455)*1e4+nsecs)%4294967296;b[i++]=tl>>>24&255;b[i++]=tl>>>16&255;b[i++]=tl>>>8&255;b[i++]=tl&255;var tmh=msecs/4294967296*1e4&268435455;b[i++]=tmh>>>8&255;b[i++]=tmh&255;b[i++]=tmh>>>24&15|16;b[i++]=tmh>>>16&255;b[i++]=clockseq>>>8|128;b[i++]=clockseq&255;for(var n=0;n<6;++n){b[i+n]=node[n]}return buf?buf:bytesToUuid(b)}module.exports=v1},function(module,exports,__webpack_require__){var rng=__webpack_require__(73);var bytesToUuid=__webpack_require__(74);function v4(options,buf,offset){var i=buf&&offset||0;if(typeof options=="string"){buf=options==="binary"?new Array(16):null;options=null}options=options||{};var rnds=options.random||(options.rng||rng)();rnds[6]=rnds[6]&15|64;rnds[8]=rnds[8]&63|128;if(buf){for(var ii=0;ii<16;++ii){buf[i+ii]=rnds[ii]}}return buf||bytesToUuid(rnds)}module.exports=v4},function(module,exports,__webpack_require__){"use strict";var _freeze=__webpack_require__(72);var _freeze2=_interopRequireDefault(_freeze);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var PARSE_ERROR=(0,_freeze2.default)({message:"Parse error",code:-32700});var INVALID_REQUEST=(0,_freeze2.default)({message:"Invalid request",code:-32600});var METHOD_NOT_FOUND=(0,_freeze2.default)({message:"Method not found",code:-32601});var INVALID_PARAMS=(0,_freeze2.default)({message:"Invalid params",code:-32602});var INTERNAL_ERROR=(0,_freeze2.default)({message:"Internal error",code:-32603});module.exports=(0,_freeze2.default)({PARSE_ERROR:PARSE_ERROR,INVALID_REQUEST:INVALID_REQUEST,METHOD_NOT_FOUND:METHOD_NOT_FOUND,INVALID_PARAMS:INVALID_PARAMS,INTERNAL_ERROR:INTERNAL_ERROR})},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var _provider=__webpack_require__(148);var _provider2=_interopRequireDefault(_provider);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}exports.default=new _provider2.default},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var _classCallCheck2=__webpack_require__(14);var _classCallCheck3=_interopRequireDefault(_classCallCheck2);var _createClass2=__webpack_require__(22);var _createClass3=_interopRequireDefault(_createClass2);var _application=__webpack_require__(149);var _application2=_interopRequireDefault(_application);var _logger=__webpack_require__(27);var _logger2=_interopRequireDefault(_logger);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var Provider=function(){function Provider(){(0,_classCallCheck3.default)(this,Provider)}(0,_createClass3.default)(Provider,[{key:"init",value:function init(config){var enforceSecurity=config.secret||config.acls.some(function(x){return x!=="*"});if(enforceSecurity&&window.self!==window.top&&!(document.documentElement.hasAttribute&&document.documentElement.hasAttribute("hidden"))){document.documentElement.setAttribute("hidden",null);_logger2.default.warn("Security warning: Hidden attribute not detected on document and has been added.")}this.application=new _application2.default;this.application.init(config);this.application.launch()}},{key:"on",value:function on(eventName,listener){this.application.on(eventName,listener)}},{key:"fullscreen",value:function fullscreen(source){this.application.fullscreen(source)}},{key:"httpError",value:function httpError(error){this.application.httpError(error)}},{key:"trigger",value:function trigger(event,detail){this.application.trigger(event,detail)}},{key:"loadPage",value:function loadPage(url){this.application.loadPage(url)}}]);return Provider}();exports.default=Provider},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var _toConsumableArray2=__webpack_require__(76);var _toConsumableArray3=_interopRequireDefault(_toConsumableArray2);var _promise=__webpack_require__(40);var _promise2=_interopRequireDefault(_promise);var _getPrototypeOf=__webpack_require__(62);var _getPrototypeOf2=_interopRequireDefault(_getPrototypeOf);var _classCallCheck2=__webpack_require__(14);var _classCallCheck3=_interopRequireDefault(_classCallCheck2);var _createClass2=__webpack_require__(22);var _createClass3=_interopRequireDefault(_createClass2);var _possibleConstructorReturn2=__webpack_require__(64);var _possibleConstructorReturn3=_interopRequireDefault(_possibleConstructorReturn2);var _inherits2=__webpack_require__(69);var _inherits3=_interopRequireDefault(_inherits2);var _jsonrpcDispatch=__webpack_require__(71);var _jsonrpcDispatch2=_interopRequireDefault(_jsonrpcDispatch);var _string=__webpack_require__(154);var _events=__webpack_require__(70);var _uri=__webpack_require__(75);var _uri2=_interopRequireDefault(_uri);var _logger=__webpack_require__(27);var _logger2=_interopRequireDefault(_logger);var _dimension=__webpack_require__(155);var _mutationObserver=__webpack_require__(156);var _mutationObserver2=_interopRequireDefault(_mutationObserver);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var Application=function(_EventEmitter){(0,_inherits3.default)(Application,_EventEmitter);function Application(){(0,_classCallCheck3.default)(this,Application);return(0,_possibleConstructorReturn3.default)(this,(Application.__proto__||(0,_getPrototypeOf2.default)(Application)).apply(this,arguments))}(0,_createClass3.default)(Application,[{key:"init",value:function init(_ref){var _ref$acls=_ref.acls,acls=_ref$acls===undefined?[]:_ref$acls,_ref$secret=_ref.secret,secret=_ref$secret===undefined?null:_ref$secret,_ref$onReady=_ref.onReady,onReady=_ref$onReady===undefined?null:_ref$onReady,_ref$targetSelectors=_ref.targetSelectors,targetSelectors=_ref$targetSelectors===undefined?"":_ref$targetSelectors;this.acls=[].concat(acls);this.secret=secret;this.onReady=onReady;this.targetSelectors=targetSelectors;this.resizeConfig=null;this.requestResize=this.requestResize.bind(this);this.handleConsumerMessage=this.handleConsumerMessage.bind(this);this.authorizeConsumer=this.authorizeConsumer.bind(this);this.verifyChallenge=this.verifyChallenge.bind(this);this.emitError=this.emitError.bind(this);this.unload=this.unload.bind(this);document.addEventListener("load",this.imageRequestResize.bind(this),true);var self=this;this.JSONRPC=new _jsonrpcDispatch2.default(self.send.bind(self),{event:function event(_event,detail){self.emit(_event,detail);return _promise2.default.resolve()},resize:function resize(){var config=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};self.resizeConfig=config;self.requestResize();var observer=new _mutationObserver2.default(function(mutations){return self.requestResize()});observer.observe(document.body,{attributes:true,childList:true,characterData:true,subtree:true});var interval=100;var resizeTimer=null;window.onresize=function(event){clearTimeout(resizeTimer);resizeTimer=setTimeout(function(){return self.requestResize()},interval)};return _promise2.default.resolve()}})}},{key:"imageRequestResize",value:function imageRequestResize(event){var tgt=event.target;if(tgt.tagName==="IMG"&&!(tgt.hasAttribute("height")||tgt.hasAttribute("width"))){this.requestResize()}}},{key:"requestResize",value:function requestResize(){if(!this.resizeConfig)return;if(this.resizeConfig.customCal){this.JSONRPC.notification("resize")}else if(this.resizeConfig.autoResizeWidth){var width=(0,_dimension.calculateWidth)(this.resizeConfig.WidthCalculationMethod);this.JSONRPC.notification("resize",[null,width+"px"])}else{var height=(0,_dimension.calculateHeight)(this.resizeConfig.heightCalculationMethod);if(this.targetSelectors||this.resizeConfig.targetSelectors){var targetSelectors=[this.targetSelectors,this.resizeConfig.targetSelectors].filter(function(val){return val}).join(", ");var heights=[].slice.call(document.querySelectorAll(targetSelectors)).map(_dimension.getOffsetHeightToBody);height=Math.max.apply(Math,(0,_toConsumableArray3.default)(heights).concat([height]))}this.JSONRPC.notification("resize",[height+"px"])}}},{key:"trigger",value:function trigger(event,detail){this.JSONRPC.notification("event",[event,detail])}},{key:"fullscreen",value:function fullscreen(url){this.trigger("xfc.fullscreen",url)}},{key:"httpError",value:function httpError(){var error=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};this.trigger("xfc.provider.httpError",error)}},{key:"loadPage",value:function loadPage(url){this.JSONRPC.notification("loadPage",[url])}},{key:"launch",value:function launch(){if(window.self!==window.top){window.addEventListener("message",this.handleConsumerMessage);window.addEventListener("beforeunload",this.unload);this.JSONRPC.notification("launch");if(this.acls.some(function(x){return x!=="*"})){this.JSONRPC.request("authorizeConsumer",[]).then(this.authorizeConsumer).catch(this.emitError)}else if(this.secret){this.JSONRPC.request("challengeConsumer",[]).then(this.verifyChallenge).catch(this.emitError)}else{this.authorizeConsumer()}}else{this.authorizeConsumer()}}},{key:"handleConsumerMessage",value:function handleConsumerMessage(event){if(!event.data.jsonrpc||event.source!==window.parent){return}_logger2.default.log("<< provider",event.origin,event.data);var origin=event.origin||event.originalEvent.origin;if(!this.activeACL&&this.acls.indexOf(origin)!==-1){this.activeACL=origin}if(this.acls.indexOf("*")!==-1||this.acls.indexOf(origin)!==-1){this.JSONRPC.handle(event.data)}}},{key:"send",value:function send(message){if(window.self===window.top){return}if(this.acls.length<1){_logger2.default.error("Message not sent, no acls provided.")}if(message){_logger2.default.log(">> provider",this.acls,message);if(this.activeACL){parent.postMessage(message,this.activeACL)}else{this.acls.forEach(function(uri){return parent.postMessage(message,uri)})}}}},{key:"verifyChallenge",value:function verifyChallenge(secretAttempt){var _this2=this;var authorize=function authorize(){_this2.acls=["*"];_this2.authorizeConsumer()};if(typeof this.secret==="string"&&(0,_string.fixedTimeCompare)(this.secret,secretAttempt)){authorize()}else if(typeof this.secret==="function"){return this.secret.call(this,secretAttempt).then(authorize)}return _promise2.default.resolve()}},{key:"authorizeConsumer",value:function authorizeConsumer(){document.documentElement.removeAttribute("hidden");this.emit("xfc.ready");this.JSONRPC.notification("authorized",[{url:window.location.href}]);if(typeof this.onReady==="function"){this.onReady.call(this)}}},{key:"emitError",value:function emitError(error){this.emit("xfc.error",error)}},{key:"unload",value:function unload(){var protocols=/^(tel|mailto|fax|sms|callto):/;var element=document.activeElement;if(!element||!(element.hasAttribute&&element.hasAttribute("download")||protocols.test(element.href))){this.JSONRPC.notification("unload");this.trigger("xfc.unload")}}}]);return Application}(_events.EventEmitter);exports.default=Application},function(module,exports,__webpack_require__){module.exports={default:__webpack_require__(151),__esModule:true}},function(module,exports,__webpack_require__){__webpack_require__(20);__webpack_require__(152);module.exports=__webpack_require__(0).Array.from},function(module,exports,__webpack_require__){"use strict";var ctx=__webpack_require__(12);var $export=__webpack_require__(3);var toObject=__webpack_require__(19);var call=__webpack_require__(55);var isArrayIter=__webpack_require__(56);var toLength=__webpack_require__(32);var createProperty=__webpack_require__(153);var getIterFn=__webpack_require__(38);$export($export.S+$export.F*!__webpack_require__(61)(function(iter){Array.from(iter)}),"Array",{from:function from(arrayLike){var O=toObject(arrayLike);var C=typeof this=="function"?this:Array;var aLen=arguments.length;var mapfn=aLen>1?arguments[1]:undefined;var mapping=mapfn!==undefined;var index=0;var iterFn=getIterFn(O);var length,result,step,iterator;if(mapping)mapfn=ctx(mapfn,aLen>2?arguments[2]:undefined,2);if(iterFn!=undefined&&!(C==Array&&isArrayIter(iterFn))){for(iterator=iterFn.call(O),result=new C;!(step=iterator.next()).done;index++){createProperty(result,index,mapping?call(iterator,mapfn,[step.value,index],true):step.value)}}else{length=toLength(O.length);for(result=new C(length);length>index;index++){createProperty(result,index,mapping?mapfn(O[index],index):O[index])}}result.length=index;return result}})},function(module,exports,__webpack_require__){"use strict";var $defineProperty=__webpack_require__(6);var createDesc=__webpack_require__(17);module.exports=function(object,index,value){if(index in object)$defineProperty.f(object,index,createDesc(0,value));else object[index]=value}},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});function fixedTimeCompare(v1,v2){var compare=function compare(value,current,index){return value|v1.charCodeAt(index)^v2.charCodeAt(index)};return v1.split("").reduce(compare,v1.length^v2.length)<1}exports.fixedTimeCompare=fixedTimeCompare},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var _toConsumableArray2=__webpack_require__(76);var _toConsumableArray3=_interopRequireDefault(_toConsumableArray2);exports.calculateHeight=calculateHeight;exports.calculateWidth=calculateWidth;exports.getOffsetToBody=getOffsetToBody;exports.getOffsetHeightToBody=getOffsetHeightToBody;var _logger=__webpack_require__(27);var _logger2=_interopRequireDefault(_logger);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function getComputedStyle(prop){var el=arguments.length>1&&arguments[1]!==undefined?arguments[1]:document.body;var result=null;if("getComputedStyle"in window){result=window.getComputedStyle(el,null)}else{result=document.defaultView.getComputedStyle(el,null)}return result!==null?parseInt(result[prop],10):0}function getAllMeasures(dimension){return[dimension.bodyOffset(),dimension.bodyScroll(),dimension.documentElementOffset(),dimension.documentElementScroll()]}var getHeight={bodyOffset:function bodyOffset(){return document.body.offsetHeight+getComputedStyle("marginTop")+getComputedStyle("marginBottom")},bodyScroll:function bodyScroll(){return document.body.scrollHeight},documentElementOffset:function documentElementOffset(){return document.documentElement.offsetHeight},documentElementScroll:function documentElementScroll(){return document.documentElement.scrollHeight},max:function max(){return Math.max.apply(Math,(0,_toConsumableArray3.default)(getAllMeasures(getHeight)))},min:function min(){return Math.min.apply(Math,(0,_toConsumableArray3.default)(getAllMeasures(getHeight)))}};var getWidth={bodyOffset:function bodyOffset(){return document.body.offsetWidth},bodyScroll:function bodyScroll(){return document.body.scrollWidth},documentElementOffset:function documentElementOffset(){return document.documentElement.offsetWidth},documentElementScroll:function documentElementScroll(){return document.documentElement.scrollWidth},scroll:function scroll(){return Math.max(getWidth.bodyScroll(),getWidth.documentElementScroll())},max:function max(){return Math.max.apply(Math,(0,_toConsumableArray3.default)(getAllMeasures(getWidth)))},min:function min(){return Math.min.apply(Math,(0,_toConsumableArray3.default)(getAllMeasures(getWidth)))}};function calculateHeight(){var calMethod=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"bodyOffset";if(!(calMethod in getHeight)){_logger2.default.error("'"+calMethod+"' is not a valid method name!")}return getHeight[calMethod]()}function calculateWidth(){var calMethod=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"scroll";if(!(calMethod in getWidth)){_logger2.default.error("'"+calMethod+"' is not a valid method name!")}return getWidth[calMethod]()}function getOffsetToBody(node){var offset=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;if(!node||node===window.document.body){return 0}var calculatedOffset=node.offsetTop+offset;var offsetParent=node.offsetParent;if(offsetParent===window.document.body){return calculatedOffset}return getOffsetToBody(offsetParent,calculatedOffset)}function getOffsetHeightToBody(node){return!node?0:getOffsetToBody(node)+node.scrollHeight}},function(module,exports,__webpack_require__){"use strict";var MutationObserver=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;var WeakMap=window.WeakMap;if(typeof WeakMap==="undefined"){var defineProperty=Object.defineProperty;var counter=Date.now()%1e9;WeakMap=function WeakMap(){this.name="__st"+(Math.random()*1e9>>>0)+(counter+++"__")};WeakMap.prototype={set:function set(key,value){var entry=key[this.name];if(entry&&entry[0]===key)entry[1]=value;else defineProperty(key,this.name,{value:[key,value],writable:true});return this},get:function get(key){var entry;return(entry=key[this.name])&&entry[0]===key?entry[1]:undefined},delete:function _delete(key){var entry=key[this.name];if(!entry)return false;var hasValue=entry[0]===key;entry[0]=entry[1]=undefined;return hasValue},has:function has(key){var entry=key[this.name];if(!entry)return false;return entry[0]===key}}}var registrationsTable=new WeakMap;var setImmediate=window.msSetImmediate;if(!setImmediate){var setImmediateQueue=[];var sentinel=String(Math.random());window.addEventListener("message",function(e){if(e.data===sentinel){var queue=setImmediateQueue;setImmediateQueue=[];queue.forEach(function(func){func()})}});setImmediate=function setImmediate(func){setImmediateQueue.push(func);window.postMessage(sentinel,"*")}}var isScheduled=false;var scheduledObservers=[];function scheduleCallback(observer){scheduledObservers.push(observer);if(!isScheduled){isScheduled=true;setImmediate(dispatchCallbacks)}}function wrapIfNeeded(node){return window.ShadowDOMPolyfill&&window.ShadowDOMPolyfill.wrapIfNeeded(node)||node}function dispatchCallbacks(){isScheduled=false;var observers=scheduledObservers;scheduledObservers=[];observers.sort(function(o1,o2){return o1.uid_-o2.uid_});var anyNonEmpty=false;observers.forEach(function(observer){var queue=observer.takeRecords();removeTransientObserversFor(observer);if(queue.length){observer.callback_(queue,observer);anyNonEmpty=true}});if(anyNonEmpty)dispatchCallbacks()}function removeTransientObserversFor(observer){observer.nodes_.forEach(function(node){var registrations=registrationsTable.get(node);if(!registrations)return;registrations.forEach(function(registration){if(registration.observer===observer)registration.removeTransientObservers()})})}function forEachAncestorAndObserverEnqueueRecord(target,callback){for(var node=target;node;node=node.parentNode){var registrations=registrationsTable.get(node);if(registrations){for(var j=0;j<registrations.length;j++){var registration=registrations[j];var options=registration.options;if(node!==target&&!options.subtree)continue;var record=callback(options);if(record)registration.enqueue(record)}}}}var uidCounter=0;function JsMutationObserver(callback){this.callback_=callback;this.nodes_=[];this.records_=[];this.uid_=++uidCounter}JsMutationObserver.prototype={observe:function observe(target,options){target=wrapIfNeeded(target);if(!options.childList&&!options.attributes&&!options.characterData||options.attributeOldValue&&!options.attributes||options.attributeFilter&&options.attributeFilter.length&&!options.attributes||options.characterDataOldValue&&!options.characterData){throw new SyntaxError}var registrations=registrationsTable.get(target);if(!registrations)registrationsTable.set(target,registrations=[]);var registration;for(var i=0;i<registrations.length;i++){if(registrations[i].observer===this){registration=registrations[i];registration.removeListeners();registration.options=options;break}}if(!registration){registration=new Registration(this,target,options);registrations.push(registration);this.nodes_.push(target)}registration.addListeners()},disconnect:function disconnect(){this.nodes_.forEach(function(node){var registrations=registrationsTable.get(node);for(var i=0;i<registrations.length;i++){var registration=registrations[i];if(registration.observer===this){registration.removeListeners();registrations.splice(i,1);break}}},this);this.records_=[]},takeRecords:function takeRecords(){var copyOfRecords=this.records_;this.records_=[];return copyOfRecords}};function MutationRecord(type,target){this.type=type;this.target=target;this.addedNodes=[];this.removedNodes=[];this.previousSibling=null;this.nextSibling=null;this.attributeName=null;this.attributeNamespace=null;this.oldValue=null}function copyMutationRecord(original){var record=new MutationRecord(original.type,original.target);record.addedNodes=original.addedNodes.slice();record.removedNodes=original.removedNodes.slice();record.previousSibling=original.previousSibling;record.nextSibling=original.nextSibling;record.attributeName=original.attributeName;record.attributeNamespace=original.attributeNamespace;record.oldValue=original.oldValue;return record}var currentRecord,recordWithOldValue;function getRecord(type,target){return currentRecord=new MutationRecord(type,target)}function getRecordWithOldValue(oldValue){if(recordWithOldValue)return recordWithOldValue;recordWithOldValue=copyMutationRecord(currentRecord);recordWithOldValue.oldValue=oldValue;return recordWithOldValue}function clearRecords(){currentRecord=recordWithOldValue=undefined}function recordRepresentsCurrentMutation(record){return record===recordWithOldValue||record===currentRecord}function selectRecord(lastRecord,newRecord){if(lastRecord===newRecord)return lastRecord;if(recordWithOldValue&&recordRepresentsCurrentMutation(lastRecord))return recordWithOldValue;return null}function Registration(observer,target,options){this.observer=observer;this.target=target;this.options=options;this.transientObservedNodes=[]}Registration.prototype={enqueue:function enqueue(record){var records=this.observer.records_;var length=records.length;if(records.length>0){var lastRecord=records[length-1];var recordToReplaceLast=selectRecord(lastRecord,record);if(recordToReplaceLast){records[length-1]=recordToReplaceLast;return}}else{scheduleCallback(this.observer)}records[length]=record},addListeners:function addListeners(){this.addListeners_(this.target)},addListeners_:function addListeners_(node){var options=this.options;if(options.attributes)node.addEventListener("DOMAttrModified",this,true);if(options.characterData)node.addEventListener("DOMCharacterDataModified",this,true);if(options.childList)node.addEventListener("DOMNodeInserted",this,true);if(options.childList||options.subtree)node.addEventListener("DOMNodeRemoved",this,true)},removeListeners:function removeListeners(){this.removeListeners_(this.target)},removeListeners_:function removeListeners_(node){var options=this.options;if(options.attributes)node.removeEventListener("DOMAttrModified",this,true);if(options.characterData)node.removeEventListener("DOMCharacterDataModified",this,true);if(options.childList)node.removeEventListener("DOMNodeInserted",this,true);if(options.childList||options.subtree)node.removeEventListener("DOMNodeRemoved",this,true)},addTransientObserver:function addTransientObserver(node){if(node===this.target)return;this.addListeners_(node);this.transientObservedNodes.push(node);var registrations=registrationsTable.get(node);if(!registrations)registrationsTable.set(node,registrations=[]);registrations.push(this)},removeTransientObservers:function removeTransientObservers(){var transientObservedNodes=this.transientObservedNodes;this.transientObservedNodes=[];transientObservedNodes.forEach(function(node){this.removeListeners_(node);var registrations=registrationsTable.get(node);for(var i=0;i<registrations.length;i++){if(registrations[i]===this){registrations.splice(i,1);break}}},this)},handleEvent:function handleEvent(e){e.stopImmediatePropagation();switch(e.type){case"DOMAttrModified":var name=e.attrName;var namespace=e.relatedNode.namespaceURI;var target=e.target;var record=new getRecord("attributes",target);record.attributeName=name;record.attributeNamespace=namespace;var oldValue=null;if(!(typeof MutationEvent!=="undefined"&&e.attrChange===MutationEvent.ADDITION))oldValue=e.prevValue;forEachAncestorAndObserverEnqueueRecord(target,function(options){if(!options.attributes)return;if(options.attributeFilter&&options.attributeFilter.length&&options.attributeFilter.indexOf(name)===-1&&options.attributeFilter.indexOf(namespace)===-1){return}if(options.attributeOldValue)return getRecordWithOldValue(oldValue);return record});break;case"DOMCharacterDataModified":var target=e.target;var record=getRecord("characterData",target);var oldValue=e.prevValue;forEachAncestorAndObserverEnqueueRecord(target,function(options){if(!options.characterData)return;if(options.characterDataOldValue)return getRecordWithOldValue(oldValue);return record});break;case"DOMNodeRemoved":this.addTransientObserver(e.target);case"DOMNodeInserted":var target=e.relatedNode;var changedNode=e.target;var addedNodes,removedNodes;if(e.type==="DOMNodeInserted"){addedNodes=[changedNode];removedNodes=[]}else{addedNodes=[];removedNodes=[changedNode]}var previousSibling=changedNode.previousSibling;var nextSibling=changedNode.nextSibling;var record=getRecord("childList",target);record.addedNodes=addedNodes;record.removedNodes=removedNodes;record.previousSibling=previousSibling;record.nextSibling=nextSibling;forEachAncestorAndObserverEnqueueRecord(target,function(options){if(!options.childList)return;return record})}clearRecords()}};if(!MutationObserver){MutationObserver=JsMutationObserver}module.exports=MutationObserver},function(module,exports,__webpack_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var ComOverrider={override:function override(cernerSmartEmbeddableLib){var isEdge=window.navigator.userAgent.indexOf("Edg")!==-1;if(window.self!==window.top&&isEdge){window.APPLINK=function(linkMode,launchObject,commandLineArgs){return cernerSmartEmbeddableLib.invokeAPI("APPLINK",{linkMode:linkMode,launchObject:launchObject,commandLineArgs:commandLineArgs})}}}};exports.default=ComOverrider}]);