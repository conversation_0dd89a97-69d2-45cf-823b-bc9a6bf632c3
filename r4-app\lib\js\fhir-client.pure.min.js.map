{"version": 3, "file": "fhir-client.pure.min.js", "mappings": ";yFAAA,MAAAA,EAAAC,EAAA,KAgBAC,EAAAD,EAAA,KACAE,EAAAF,EAAA,KAIAG,EAAAH,EAAA,MAIM,SAAEI,GAAsDC,OAGxDC,EAAQP,EAAAO,MAAOC,OAAO,UA8C5B,MAAqBC,UAAeL,EAAAM,QAiJhCC,WAAAA,CAAYC,EAAiCC,GAEzC,MAAMC,EAAyB,iBAATD,EAAoB,CAAEE,UAAWF,GAAUA,GAGjE,EAAAb,EAAAgB,QACIF,EAAOC,WAAaD,EAAOC,UAAUE,MAAM,iBAC3C,kEAGJC,MAAMJ,EAAOC,WAmpBjB,KAAAI,MAAQnB,EAAAmB,MAjpBJC,KAAKP,MAAQC,EACbM,KAAKR,YAAcA,EACnBQ,KAAKC,aAAe,KAEpB,MAAMC,EAASF,KAGfA,KAAKG,QAAU,CACX,MAAIC,GAAO,OAAOF,EAAOG,cAAgB,EACzCC,KAAOC,IACH,MAAMH,EAAKJ,KAAKG,QAAQC,GACxB,OAAOA,EACHJ,KAAKQ,QAAQ,IAAKD,EAAgBE,IAAK,WAAWL,MAClDM,QAAQC,OAAO,IAAIC,MAAM,4BAA4B,EAE7DJ,QAASA,CAACD,EAAgBM,EAAc,CAAC,IACjCb,KAAKG,QAAQC,GACN,WACH,MAAMU,QApN9BC,eACIR,EACAL,GAGA,MAAMc,GAAO,EAAApC,EAAAqC,UAAS,IAAKf,EAAOT,MAAME,WAExCoB,eAAeG,EAAcC,GACzB,MAAMC,EAAeD,EAAKE,SAASC,MAAM,KAAKC,OAC9C,EAAA3C,EAAAgB,QAAOwB,EAAc,gBAAgBD,OACrC,EAAAvC,EAAAgB,QAAOb,EAAAyC,mBAAmBC,QAAQL,IAAiB,EAAG,kBAAkBA,2BACxE,MAAMM,QAAoB,EAAA9C,EAAA+C,2BAA0BzB,EAAOT,MAAME,WAC3DiC,GAAc,EAAAhD,EAAAiD,iBAAgBH,EAAaN,GAEjD,OADAD,EAAKW,aAAaC,IAAIH,EAAa1B,EAAOC,QAAQC,IAC3Ce,EAAKa,IAChB,CAEA,MAA6B,iBAAlBzB,GAA8BA,aAA0B0B,IACxD,CAAExB,UAAWS,EAAc,IAAIe,IAAI1B,EAAiB,GAAIS,MAGnET,EAAeE,UAAYS,EAAc,IAAIe,IAAI1B,EAAeE,IAAM,GAAIO,IACnET,EACX,CA6L8C2B,CAAc3B,EAAgBP,MACpD,OAAOA,KAAKQ,QAAQM,EAASD,EAChC,EAHM,GAKAH,QAAQC,OAAO,IAAIC,MAAM,8BAM5CZ,KAAKmC,UAAY,CACb,MAAI/B,GAAO,OAAOF,EAAOkC,gBAAkB,EAC3C9B,KAAMC,IACF,MAAMH,EAAKJ,KAAKmC,UAAU/B,GAC1B,OAAOA,EACHJ,KAAKQ,QAAQ,IAAKD,EAAgBE,IAAK,aAAaL,MACpDM,QAAQC,OAAO,IAAIC,MAAM,8BAA8B,GAKnEZ,KAAKqC,KAAO,CACR,YAAIC,GAAa,OAAOpC,EAAOqC,aAAe,EAC9C,MAAInC,GAAO,OAAOF,EAAOsC,WAAa,EACtC,gBAAIpB,GAAiB,OAAOlB,EAAOuC,aAAe,EAClDnC,KAAMC,IACF,MAAM+B,EAAWtC,KAAKqC,KAAKC,SAC3B,OAAOA,EACHtC,KAAKQ,QAAQ,IAAKD,EAAgBE,IAAK6B,IACvC5B,QAAQC,OAAO,IAAIC,MAAM,yBAAyB,GAM9DZ,KAAK0C,QAASlD,EAA+BmD,KACjD,CAUAD,OAAAA,CAAQE,GAEJ,GAAqB,mBAAVA,EAAsB,CAC7B,MAAM9B,EAA+B,CACjC+B,QAAS7C,KAAKP,MAAME,UAAUmD,QAAQ,MAAO,KAG3CC,EAAc/C,KAAKgD,SAAS,8BAClC,GAAID,EACAjC,EAAQmC,KAAO,CAAEC,MAAOH,OAEvB,CACD,MAAM,SAAEI,EAAQ,SAAEC,GAAapD,KAAKP,MAChC0D,GAAYC,IACZtC,EAAQmC,KAAO,CACXZ,KAAMc,EACNE,KAAMD,IAIlBpD,KAAKsD,IAAMV,EAAO9B,GAElB,MAAMyC,EAAYvD,KAAKgD,SAAS,yBAC5BO,IACAvD,KAAKG,QAAQmD,IAAMV,EAAO,IACnB9B,EACHX,QAASoD,KAIrB,OAAOvD,IACX,CAMAK,YAAAA,GAEI,MAAMmD,EAAgBxD,KAAKP,MAAM+D,cACjC,OAAIA,EAGKA,EAAcrD,QAUZqD,EAAcrD,UATXH,KAAKP,MAAMgE,OAAS,IAAI5D,MAAM,0BAKhCV,EAAM,+FAJNA,EAAML,EAAAQ,QAAIoE,aAAc,UAAW,WAMhC,OAKX1D,KAAKP,MAAMkE,aACXxE,EAAML,EAAAQ,QAAIsE,WAAY,kCAGtBzE,EAAML,EAAAQ,QAAIuE,cAAe,oBAEtB,KACX,CAQAzB,cAAAA,GAEI,MAAMoB,EAAgBxD,KAAKP,MAAM+D,cACjC,OAAIA,EAGKA,EAAcrB,UAUZqB,EAAcrB,YATXnC,KAAKP,MAAMgE,OAAS,IAAI5D,MAAM,4BAKhCV,EAAM,4JAJNA,EAAML,EAAAQ,QAAIoE,aAAc,YAAa,aAMlC,OAKX1D,KAAKP,MAAMkE,aACXxE,EAAML,EAAAQ,QAAIsE,WAAY,oCAGtBzE,EAAML,EAAAQ,QAAIuE,cAAe,sBAEtB,KACX,CAOAC,UAAAA,GAEI,MAAMN,EAAgBxD,KAAKP,MAAM+D,cACjC,GAAIA,EAAe,CACf,MAAMO,EAAUP,EAAcQ,SACxBP,EAAQzD,KAAKP,MAAMgE,OAAS,GAIlC,IAAKM,EAAS,CACV,MAAME,EAAcR,EAAM5D,MAAM,cAC1BqE,EAAcT,EAAM5D,MAAM,eAC1BsE,EAAcV,EAAM5D,MAAM,gBAahC,OAFIV,EAVC8E,IAAeE,GAAeD,GAUzB,4EARF,gKAUD,KAEX,OAAO,EAAAtF,EAAAwF,WAAUL,EAAS/D,KAAKR,aAQnC,OANIQ,KAAKP,MAAMkE,aACXxE,EAAML,EAAAQ,QAAIsE,WAAY,gBAGtBzE,EAAML,EAAAQ,QAAIuE,cAAe,YAEtB,IACX,CAOAtB,WAAAA,GAEI,MAAMwB,EAAU/D,KAAK8D,aACrB,OAAIC,EAGIA,EAAQzB,SACDyB,EAAQzB,SAAShB,MAAM,KAAK+C,OAAO,GAAGC,KAAK,KAE/CP,EAAQQ,QAEZ,IACX,CAKA/B,SAAAA,GAEI,MAAM+B,EAAUvE,KAAKuC,cACrB,OAAIgC,EACOA,EAAQjD,MAAM,KAAK,GAEvB,IACX,CAMAmB,WAAAA,GAEI,MAAM8B,EAAUvE,KAAKuC,cACrB,OAAIgC,EACOA,EAAQjD,MAAM,KAAK,GAEvB,IACX,CAMAkD,sBAAAA,GAEI,MAAMzB,EAAc/C,KAAKgD,SAAS,8BAClC,GAAID,EACA,MAAO,UAAYA,EAEvB,MAAM,SAAEI,EAAQ,SAAEC,GAAapD,KAAKP,MACpC,OAAI0D,GAAYC,EACL,SAAWpD,KAAKR,YAAYiF,KAAKtB,EAAW,IAAMC,GAEtD,IACX,CAMQ,iBAAMsB,GACV,MAAMC,EAAU3E,KAAKR,YAAYoF,aAC3BC,QAAYF,EAAQG,IAAI/F,EAAAgG,WAC1BF,SACMF,EAAQK,MAAMH,SAElBF,EAAQK,MAAMjG,EAAAgG,WACpB/E,KAAKP,MAAM+D,cAAgB,CAAC,CAChC,CASA,aAAMhD,CACFD,EACAM,EAAsC,CAAC,EACvCoE,EAAuC,CAAC,SAGxC,MAAMC,EAAetG,EAAAO,MAAOC,OAAO,kBAInC,IAAIqB,GAHJ,EAAA7B,EAAAgB,QAAOW,EAAgB,0DAIM,iBAAlBA,GAA8BA,aAA0B0B,KAC/DxB,EAAM0E,OAAO5E,GACbA,EAAiB,CAAC,GAGlBE,EAAM0E,OAAO5E,EAAeE,KAGhCA,GAAM,EAAA7B,EAAAqC,UAASR,EAAKT,KAAKP,MAAME,WAE/B,MAAMmB,EAAU,CACZsE,OAA6B,IAAtBvE,EAAYuE,MACnBC,OAASxE,EAAYwE,KACrBC,UAAgC,QAArBC,EAAA1E,EAAYyE,iBAAS,IAAAC,EAAAA,EAAI,EACpCC,mBAAmB,EAAA5G,EAAA6G,WAAU5E,EAAY2E,mBAAqB,IAC9DE,iBAAiD,IAAhC7E,EAAY6E,gBAC7BC,OAAqC,mBAAtB9E,EAAY8E,OACvB9E,EAAY8E,YAGZC,GAGFC,EAAUtF,EAA+BsF,aAAUD,EAGrD9E,EAAQ4E,uBACF1F,KAAK8F,gBAAgB,CAAED,WAKjC,MAAME,EAAa/F,KAAKwE,yBAUxB,IAAIwB,EAEJ,OAXID,IACAxF,EAAe0F,QAAU,IAClB1F,EAAe0F,QAClBC,cAAeH,IAIvBb,EAAa,mCAAoCzE,EAAKF,EAAgBO,GAI/DhB,MAAMqG,YAAoC1F,EAAKF,GAAgB6F,MAAKC,GAClE9F,EAA6C+F,iBAC9CN,EAAYK,EAA0CL,SAC9CK,EAA0CE,MAE/CF,IAIVG,OAAMzF,UACH,GAAoB,KAAhB0F,EAAMC,OAAe,CAGrB,IAAK1G,KAAKgD,SAAS,8BAEf,MADAyD,EAAME,SAAW,yEACXF,EAKV,IAAK3F,EAAQ4E,gBAIT,MAHAR,EAAa,4GACPlF,KAAK0E,cACX+B,EAAME,SAAW,KAAO7H,EAAAQ,QAAIsH,QACtBH,EAYV,MAHAvB,EAAa,wDACPlF,KAAK0E,cACX+B,EAAME,SAAW,KAAO7H,EAAAQ,QAAIsH,QACtBH,EAEV,MAAMA,CAAK,IAIdD,OAAOC,IAIJ,MAHoB,KAAhBA,EAAMC,QACNxB,EAAa,kFAEXuB,CAAK,IAGdL,MAAKrF,UAKG8F,GAAuB,iBAARA,GAAoBA,aAAgB5H,EAC/CsB,EAA2C+F,gBACrC,CACHC,KAAMM,EACNb,YAGDa,SAIL7G,KAAK8G,gBACPD,EACA/F,EAAQ0E,kBACR1E,EAAQsE,MACRH,EACA1E,GAGGG,QAAQqG,QAAQF,GAGtBT,MAAKrF,UACF,GAAIiG,GAA+B,UAAtBA,EAAM5F,aAA0B,CACzC,MAAM6F,EAASD,EAAME,MAAQ,GAY7B,GAVIpG,EAAQuE,OACR2B,GAASA,EAAMG,OAAS,IAAIC,KACvBD,GAAuCA,EAAME,YAIlDvG,EAAQ6E,cACF7E,EAAQ6E,OAAOqB,EAAO,IAAK/B,MAG/BnE,EAAQwE,UAAW,CACrB,MAAMgC,EAAOL,EAAMM,MAAKC,GAAmB,QAAdA,EAAEC,WAE/B,GADAT,GAAQ,EAAApI,EAAA6G,WAAUuB,GACdM,GAAQA,EAAK7G,IAAK,CAClB,MAAMiH,QAAiB1H,KAAKQ,QACxB,CACIC,IAAK6G,EAAK7G,IAMVoF,UAEJ/E,EACAmE,GAGJ,OAAInE,EAAQ6E,OACD,KAGP7E,EAAQ0E,kBAAkBmC,QAC1BC,OAAOC,OAAO5C,EAAeyC,EAASI,YAC/Bd,EAAMe,QAAO,EAAAnJ,EAAA6G,WAAUiC,EAASb,MAAQa,KAE5CV,EAAMe,QAAO,EAAAnJ,EAAA6G,WAAUiC,MAI1C,OAAOV,CAAK,IAIfZ,MAAKY,IACF,GAAIlG,EAAQsE,MACRH,EAAgB,CAAC,OAEhB,IAAKnE,EAAQ6E,QAAU7E,EAAQ0E,kBAAkBmC,OAClD,MAAO,CACHd,KAAMG,EACNc,WAAY7C,GAGpB,OAAO+B,CAAK,IAEfZ,MAAKY,GACGzG,EAA2C+F,gBACrC,CACHC,KAAMS,EACNhB,YAGDgB,MAGnB,CAWAlB,eAAAA,CAAgBvF,EAA8B,CAAC,GAE3C,MAAMwC,EAAe/C,KAAKgD,SAAS,8BAC7BgF,EAAehI,KAAKgD,SAAS,+BAC7BiF,EAAejI,KAAKP,MAAMwI,WAAa,EAE7C,OAAIlF,GAAeiF,GAAgBC,EAAY,GAAKC,KAAKC,MAAQ,IACtDnI,KAAKoI,QAAQ7H,GAGjBG,QAAQqG,QAAQ/G,KAAKP,MAChC,CAeA2I,OAAAA,CAAQ7H,EAA8B,CAAC,WAEnC,MAAM8H,EAAezJ,EAAAO,MAAOC,OAAO,kBACnCiJ,EAAa,+CAEb,MAAML,EAAwC,QAAzBM,EAAU,QAAV/C,EAAAvF,KAAKP,aAAK,IAAA8F,OAAA,EAAAA,EAAE/B,qBAAa,IAAA8E,OAAA,EAAAA,EAAEC,eAChD,EAAA3J,EAAAgB,QAAOoI,EAAc,8CAErB,MAAMQ,EAAWxI,KAAKP,MAAM+I,UAC5B,EAAA5J,EAAAgB,QAAO4I,EAAU,yCAEjB,MAAMC,EAASzI,KAAKgD,SAAS,wBAA0B,GACjD0F,EAAmBD,EAAOE,OAAO,uBAAyB,EAC1DC,EAAkBH,EAAOE,OAAO,sBAAwB,EAO9D,IANA,EAAA/J,EAAAgB,QAAO8I,GAAoBE,EAAiB,uEAMvC5I,KAAKC,aAAc,CACpB,IAAIsG,EAAO,0CAA0CsC,mBAAmBb,KACpEhI,KAAKR,YAAYsB,QAAQgI,2BACzBvC,GAAQ,cAAcvG,KAAKP,MAAMsJ,YAErC,MAAMC,EAAwB,CAC1BC,YAAajJ,KAAKR,YAAYsB,QAAQoI,6BAA+B,iBAClE3I,EACH4I,OAAS,OACTC,KAAS,OACTnD,QAAS,IACD1F,EAAe0F,SAAW,CAAC,EAC/B,eAAgB,qCAEpBM,KAAMA,GAIV,KAAM,kBAAmByC,EAAsB/C,SAAU,CACrD,MAAM,aAAEoD,EAAY,SAAEN,GAAa/I,KAAKP,MACpC4J,IAEAL,EAAsB/C,QAAQC,cAAgB,SAAWlG,KAAKR,YAAYiF,KACtEsE,EAAW,IAAMM,IAK7BrJ,KAAKC,cAAe,EAAArB,EAAA4B,SAAkCgI,EAAUQ,GAC/D5C,MAAKS,KACF,EAAAjI,EAAAgB,QAAOiH,EAAKyC,aAAc,4BAC1BjB,EAAa,wCAAyCxB,GACtD7G,KAAKP,MAAM+D,cAAgB,IAAKxD,KAAKP,MAAM+D,iBAAkBqD,GAC7D7G,KAAKP,MAAMwI,WAAY,EAAArJ,EAAA2K,0BAAyB1C,EAAM7G,KAAKR,aACpDQ,KAAKP,SAEf+G,OAAOC,YAKJ,MAJ6B,QAAzB6B,EAAU,QAAV/C,EAAAvF,KAAKP,aAAK,IAAA8F,OAAA,EAAAA,EAAE/B,qBAAa,IAAA8E,OAAA,EAAAA,EAAEC,iBAC3BF,EAAa,yDACNrI,KAAKP,MAAM+D,cAAc+E,eAE9B9B,CAAK,IAEd+C,SAAQ,KACLxJ,KAAKC,aAAe,KACpB,MAAM4E,EAAM7E,KAAKP,MAAMoF,IACnBA,EACA7E,KAAKR,YAAYoF,aAAa7C,IAAI8C,EAAK7E,KAAKP,OAE5C4I,EAAa,kEAKzB,OAAOrI,KAAKC,YAChB,CAoBAwJ,MAAAA,CACIC,EACAC,GAGA,OAAO,EAAA/K,EAAA6K,QAAOC,EAAcC,EAChC,CAmBAC,OAAAA,CACIF,EACAC,GAGA,OAAO,EAAA/K,EAAAgL,SAAQF,EAAcC,EACjC,CAoBAE,OAAAA,CAAQC,EAA0BC,EAAO,IACrC,OAAO,EAAAnL,EAAAiL,SAAQC,EAAKC,EACxB,CAcA/G,QAAAA,CAAS+G,EAAO,IACZ,OAAO,EAAAnL,EAAAiL,SAAQ,IAAK7J,KAAKP,OAASsK,EACtC,EA/0BJC,EAAAA,QAAA3K,+ECzEA,MAAAN,EAAAF,EAAA,KAEAD,EAAAC,EAAA,KAaMM,EAAQP,EAAAO,MAAOC,OAAO,cA4B5B4K,EAAAA,QAAA,MAcIzK,WAAAA,CAAY0K,IAER,EAAArL,EAAAgB,QACIqK,GAAsC,iBAAhBA,GAA4BA,EAAYpK,MAAM,iBACpE,8EAEJG,KAAKiK,YAAcA,CACvB,CAUA,YAAMC,CACF7C,EACA9G,GAGA,OAAOP,KAAKmG,YAAYkB,EAASjG,aAAe,IACzCb,EACH4I,OAAQ,OACR5C,KAAM4D,KAAKC,UAAU/C,GACrBpB,QAAS,CACL,eAAgB,uBACZ1F,GAAkB,CAAC,GAAG0F,UAGtC,CAWA,YAAMoE,CACFhD,EACA9G,GAGA,OAAOP,KAAKmG,YAAY,GAAGkB,EAASjG,gBAAgBiG,EAASjH,KAAM,IAC5DG,EACH4I,OAAQ,MACR5C,KAAM4D,KAAKC,UAAU/C,GACrBpB,QAAS,CACL,eAAgB,uBACZ1F,GAAkB,CAAC,GAAG0F,UAGtC,CAWA,YAAMqE,CAAoB7J,EAAaF,EAA0C,CAAC,GAE9E,OAAOP,KAAKmG,YAAe1F,EAAK,IAAKF,EAAgB4I,OAAQ,UACjE,CAoBA,WAAMoB,CACF9J,EACA8J,EACAhK,EAA0C,CAAC,GAI3C,OADA,EAAA3B,EAAA4L,iBAAgBD,GACTvK,KAAKmG,YAAyB1F,EAAK,IACnCF,EACH4I,OAAQ,QACR5C,KAAM4D,KAAKC,UAAUG,GACrBtE,QAAS,CACL,OAAU,sBACV,eAAgB,gDACb1F,EAAe0F,UAG9B,CAEQ,gBAAMwE,CACVX,EACAC,EACA3E,EACAsF,EACAnK,EAAyD,CAAC,GAE1D,MAAMoK,GAAO,EAAA/L,EAAAiL,SAAQC,EAAKC,GAC1B,GAAIY,EAAM,CACN,MAAMC,EAAUC,MAAMD,QAAQD,GAC9B,OAAOjK,QAAQoK,KAAI,EAAAlM,EAAA6G,WAAUkF,GAAMI,OAAOC,SAAS5D,KAAI,CAAC6D,EAAMC,KAC1D,MAAMC,EAAMF,EAAKG,UACjB,GAAID,EACA,OAAOnL,KAAKmG,YAAYgF,EAAK,IAAK5K,EAAgB+F,iBAAiB,EAAO+E,SAAUX,IAAStE,MAAKkF,IAC1FlG,IACIwF,EACIb,EAAKtI,QAAQ,OAAS,GACtB,EAAA7C,EAAA2M,SAAQzB,EAAK,GAAGC,EAAKjH,QAAQ,KAAM,IAAIoI,QAASI,IAEhD,EAAA1M,EAAA2M,SAAQzB,EAAK,GAAGC,KAAQmB,IAAKI,IAGjC,EAAA1M,EAAA2M,SAAQzB,EAAKC,EAAMuB,OAG5B9E,OAAOgF,IACN,GAAmB,OAAfA,aAAE,EAAFA,EAAI9E,QAGJ,MAAM8E,EAFNC,QAAQC,KAAK,qBAAqBP,MAAQK,YAQlE,CAMA,uBAAMhG,CACF6B,EACAS,EACAvH,EAAyD,CAAC,SAEpDP,KAAK8G,gBAAgBO,EAAUS,GAAY,EAAM,CAAC,EAAGvH,EAC/D,CAEU,qBAAMuG,CACZO,EACAS,EACA1C,EACAsF,EAA6B,CAAC,EAC9BnK,EAAyD,CAAC,GAG1D,GAA6B,UAAzB8G,EAASjG,aAA0B,CACnC,IAAK,MAAM6J,KAAU5D,EAAoBF,OAAS,GAC1C8D,EAAK5D,gBACCrH,KAAK8G,gBAAgBmE,EAAK5D,SAAUS,EAAY1C,EAAOsF,EAAOnK,GAG5E,OAAOmK,EAIX,IAAIiB,EAAQ7D,EAAWV,KAAI2C,GAAQ5E,OAAO4E,GAAM6B,SAAQb,OAAOC,SAa/D,GAVAW,EAAQA,EAAME,QAAO,CAACC,EAAMC,KACpBD,EAAKE,SAASD,GACd5M,EAAM,iCAAoC4M,GAE1CD,EAAKG,KAAKF,GAEPD,IACR,KAGEH,EAAMhE,OACP,OAAOjH,QAAQqG,QAAQ2D,GAK3B,MAAMwB,EAA8B,CAAC,EACrCP,EAAMQ,SAAQpC,IACV,MAAMqC,EAAMrC,EAAKzI,MAAM,KAAKqG,OACvBuE,EAAOE,KACRF,EAAOE,GAAO,IAElBF,EAAOE,GAAKH,KAAKlC,EAAK,IAK1B,IAAIsC,EAAqB3L,QAAQqG,UAQjC,OAPAa,OAAO0E,KAAKJ,GAAQK,OAAOJ,SAAQC,IAC/B,MAAMI,EAAQN,EAAOE,GACrBC,EAAOA,EAAKjG,MAAK,IAAM1F,QAAQoK,IAAI0B,EAAMpF,KAAK2C,GACnC/J,KAAKyK,WAAWpD,EAAU0C,EAAM3E,EAAOsF,EAAOnK,OACrD,UAEF8L,EACC3B,CACX,CAKA,mBAAM+B,CACFpF,EACAS,EACAvH,EAAyD,CAAC,GAE1D,MAAMmM,QAAa1M,KAAK8G,gBAAgBO,EAAUS,GAAY,EAAO,CAAC,EAAGvH,GACnEoM,EAAW,CAAC,EAClB,IAAK,MAAM9H,KAAO6H,EACdC,EAAI9H,SAAa6H,EAAK7H,GAE1B,OAAO8H,CACX,CAOA,eAAOC,CAAUC,EAAoC/L,GACjD,IAAIgM,EAAQ,EACZ,UAAU,MAAMC,KAAQ/M,KAAKgN,MAAMH,EAAa/L,GAC5C,IAAK,MAAMqG,KAAU4F,EAAK5F,OAAS,GAAK,CACpC,IAAIrG,aAAO,EAAPA,EAASmM,UAAWH,EAAQhM,EAAQmM,MACpC,aAEE9F,EAAME,SAGxB,CAQA,WAAO2F,CACHH,EACAtM,WAEA,MAAM,MAAE0M,KAAUnM,GAAYP,GAAkB,CAAC,EAE3C2M,EAAazM,GAAsBT,KAAKmG,YAAY1F,EAAKK,GAE/D,IAAIiM,EAAsC,iBAAhBF,GAA4BA,aAAuB5K,UACnEiL,EAAUL,GAChBA,EAEAC,EAAQ,EAEZ,KAAOC,GAA8B,WAAtBA,EAAK3L,gBAA+B6L,KAAWH,GAASG,WAG7DF,IAGa,QAAfxH,EAAAzE,aAAO,EAAPA,EAAS+E,cAAM,IAAAN,OAAA,EAAAA,EAAE4H,WANsD,CAW3E,MAAMC,GAAqB,QAAT9E,EAAAyE,EAAK7F,YAAI,IAAAoB,EAAAA,EAAI,IAAIf,MAC9BC,GAAiC,SAAfA,EAAEC,UAAwC,iBAAVD,EAAE/G,MAGzD,IAAK2M,EACD,MAIJL,QAAaG,EAAUE,EAAS3M,KAExC,CAKA,iBAAM0F,CAAqBkH,EAAmBvM,EAA0B,CAAC,IAErE,EAAAlC,EAAAgB,QAAOkB,EAAS,gDAEhB,MAAMiJ,EAAOsD,EAAM,GACb5M,GAAO,EAAA7B,EAAAqC,UAAS8I,EAAM/J,KAAKiK,cAC3B,SAAEoB,GAAavK,EAErB,OAAIuK,GACMtB,KAAQsB,IACVA,EAAStB,IAAQ,EAAAnL,EAAA4B,SAAWC,EAAKK,GAChCsF,MAAKkH,IACFjC,EAAStB,GAAQuD,EACVA,KAEV9G,OAAMC,IAEH,aADO4E,EAAStB,GACVtD,CAAK,KAGZ4E,EAAStB,KAEb,EAAAnL,EAAA4B,SAAWC,EAAKK,EAC3B,CAMA,oBAAMyM,GAEF,OAAO,EAAA3O,EAAA+C,2BAA0B3B,KAAKiK,aACjC7D,MAAMoH,GAAaA,EAASC,aACrC,CASA,oBAAMC,GAEF,OAAO1N,KAAKuN,iBAAiBnH,MAAKuH,IAAI,IAAApI,EAAC,OAAwB,QAAxBA,EAACxG,EAAA6O,aAAqBD,UAAE,IAAApI,EAAAA,EAAI,CAAC,GACxE,8EC7YJ,MAAqBsI,UAAkBjN,MAwBnCrB,WAAAA,CAAYyG,GACRlG,MAAM,GAAGkG,EAASU,UAAUV,EAAS8H,oBAAoB9H,EAASvF,OAClET,KAAK+N,KAAa,YAClB/N,KAAKgG,SAAaA,EAClBhG,KAAKgO,WAAahI,EAASU,OAC3B1G,KAAK0G,OAAaV,EAASU,OAC3B1G,KAAK8N,WAAa9H,EAAS8H,UAC/B,CAEA,WAAMG,GAEF,IAAKjO,KAAKgG,SAASkI,SACf,IACI,MAAMC,EAAOnO,KAAKgG,SAASC,QAAQnB,IAAI,iBAAmB,aAC1D,GAAIqJ,EAAKtO,MAAM,aAAc,CACzB,IAAI0G,QAAavG,KAAKgG,SAASoI,OAC3B7H,EAAKE,OACLzG,KAAK2G,SAAW,KAAOJ,EAAKE,MACxBF,EAAK8H,oBACLrO,KAAK2G,SAAW,KAAOJ,EAAK8H,oBAIhCrO,KAAK2G,SAAW,OAASwD,KAAKC,UAAU7D,EAAM,KAAM,QAGvD,GAAI4H,EAAKtO,MAAM,YAAa,CAC7B,IAAI0G,QAAavG,KAAKgG,SAASsI,OAC3B/H,IACAvG,KAAK2G,SAAW,OAASJ,IAGnC,MACE,CAIR,OAAOvG,IACX,CAEAuO,MAAAA,GACI,MAAO,CACHR,KAAY/N,KAAK+N,KACjBC,WAAYhO,KAAKgO,WACjBtH,OAAY1G,KAAK0G,OACjBoH,WAAY9N,KAAK8N,WACjBnH,QAAY3G,KAAK2G,QAEzB,EAxEJqD,EAAAA,QAAA6D,+ECHA,MAAAW,EAAA3P,EAAA,KACA4P,EAAA5P,EAAA,KACA6P,EAAA7P,EAAA,KAEA8P,EAAA9P,EAAA,KACA+P,EAAA/P,EAAA,KAKAmL,EAAAA,QAAA,MAsBIzK,WAAAA,CAAYuB,EAA0C,CAAC,GAjB/C,KAAAK,KAAmB,KAKnB,KAAA0N,SAAsC,KAO9C,KAAAF,SAAWA,EAOP3O,KAAKc,QAAU,CAGXgO,uBAAuB,EAWvBC,2BAA2B,EAe3B7F,4BAA6B,iBAE1BpI,EAEX,CAKAkO,QAAAA,CAASjF,GAEL,OAAO,IAAI9H,IAAI8H,EAAM/J,KAAKiP,SAASjN,MAAMA,IAC7C,CAOA,QAAIW,GAGA,MAAuB,mBAATA,KAAsBA,KAAO,IAC/C,CAMAsM,MAAAA,GAKI,OAHKjP,KAAKmB,OACNnB,KAAKmB,KAAO,IAAIc,IAAIiN,SAAW,KAE5BlP,KAAKmB,IAChB,CAMAgO,QAAAA,CAASC,GAELF,SAASlN,KAAOoN,CACpB,CAMAxK,UAAAA,GAKI,OAHK5E,KAAK6O,WACN7O,KAAK6O,SAAW,IAAIH,EAAApP,SAEjBU,KAAK6O,QAChB,CAMAQ,kBAAAA,GAEI,OAAOC,eACX,CAKAC,IAAAA,CAAKC,GAED,OAAOtQ,OAAOqQ,KAAKC,EACvB,CAKA/K,IAAAA,CAAK+K,GAED,OAAOtQ,OAAOuF,KAAK+K,EACvB,CAEAC,eAAAA,CAAgBC,GAEZ,MAAoB,iBAATA,GACA,EAAAd,EAAAe,WAAUD,IAEd,EAAAd,EAAAgB,gBAAeF,GAAO,EACjC,CAEAG,eAAAA,CAAgBH,GAEZ,OAAO,EAAAd,EAAAkB,QAAOJ,EAClB,CASAK,WAAAA,GAEI,MAAO,CACHC,MAAWA,IAAIC,KAAgB,EAAAzB,EAAAwB,OAAMhQ,QAASiQ,GAC9CC,UAAWpP,IAAW,EAAA0N,EAAA0B,WAAUlQ,KAAMc,GACtCqP,KAAWrP,IAAW,EAAA0N,EAAA2B,MAAKnQ,KAAMc,GACjCZ,OAAYT,GAA2C,IAAIgP,EAAAnP,QAAOU,KAAMP,GACxEqB,QAAWd,KAAKc,QAChBsP,MAAO,CACHzB,YAGZ,+BC1KJ,MAAA0B,EAAAxR,EAAA,KACAG,EAAAH,EAAA,KAEMyR,EAAU,IAAID,EAAA/Q,SACd,MAAE0Q,EAAK,UAAEE,EAAS,KAAEC,EAAI,OAAEjQ,EAAM,QAAEY,EAAO,MAAEsP,GAAUE,EAAQP,cAoB7DQ,EAAO,CACTjB,gBAAiBpQ,OAAOoQ,gBAExBpP,SAMAsQ,WAAAxR,EAAAM,QAEA8Q,QACAK,OAAQ,CACJC,SAAU5P,EACVkP,QACAE,YACAC,SAIRQ,EAAA3G,QAASuG,+YC/CT,MAAAK,EAAA/R,EAAA,KACAE,EAAAF,EAAA,KAEMM,EAAQN,EAAQ,MAIhB,MAAEgS,GAAmD3R,OAGrD4R,EAAa3R,EAAM,QACN6K,EAAAA,MAAA8G,EAKnB,MAAMpG,EAA6B,CAAC,EAkCpC,SAASqG,GAAgB,MAAEC,EAAK,KAAEC,IAC9B,GAAqB,iBAAVD,EACP,MAAM,IAAIpQ,MAAM,+BAAiCoQ,EAAQ,IAAMC,EAEvE,CAKOlQ,eAAemQ,EAAcC,GAChC,IAAKA,EAAKC,GAAI,CACV,MAAM3K,EAAQ,IAAImK,EAAAtR,QAAU6R,GAE5B,YADM1K,EAAMwH,QACNxH,EAEV,OAAO0K,CACX,CAOA,SAAgBE,EAAeF,GAC3B,OAAOA,EAAK7C,OAAOlI,MAAKkI,GAAQA,EAAK3G,OAASwC,KAAK8D,MAAMK,GAAQ,IACrE,CAEA,SAAgBgD,EAAwDxH,GAGpE,IAAKA,EACD,OAAOA,EAIX,GAAIe,MAAMD,QAAQd,GACd,OAAOA,EAAI1C,KAAIuG,GAAKA,GAAkB,iBAANA,EAAiB2D,EAAa3D,GAAKA,IAIvE,IAAIhB,EAA2B,CAAC,EAMhC,OALA/E,OAAO0E,KAAKxC,GAAKqC,SAAQtH,IACrB,MAAM0M,EAAW1M,EAAI2M,cACf7D,EAAK7D,EAA4BjF,GACvC8H,EAAI4E,GAAY5D,GAAiB,iBAALA,EAAgB2D,EAAa3D,GAAKA,CAAC,IAE5DhB,CACX,CAYA,SAAgBnM,EACZC,EACAF,EAA0C,CAAC,GAG3C,MAAM,gBAAE+F,KAAoBxF,GAAYP,EACxC,OAAOsQ,EAAMpQ,EAAK,CACd2I,KAAM,UACHtI,EACHmF,QAAS,CACLwL,OAAQ,sBACLH,EAAaxQ,EAAQmF,YAG/BG,KAAK8K,GACL9K,MAAMkH,IACH,MAAMa,EAAOb,EAAIrH,QAAQnB,IAAI,gBAAkB,GAC/C,OAAIqJ,EAAKtO,MAAM,aACJwR,EAAe/D,GAAKlH,MAAKG,IAAQ,CAAG+G,MAAK/G,WAEhD4H,EAAKtO,MAAM,YACJyN,EAAIgB,OAAOlI,MAAKG,IAAQ,CAAG+G,MAAK/G,WAEpC,CAAE+G,MAAK,IAEjBlH,MAAK,EAAEkH,MAAK/G,WAKT,IAAKA,GAAsB,KAAd+G,EAAI5G,OAAe,CAC5B,MAAMwI,EAAW5B,EAAIrH,QAAQnB,IAAI,YACjC,GAAIoK,EACA,OAAO1O,EAAQ0O,EAAU,IAAKpO,EAASqI,OAAQ,MAAO5C,KAAM,KAAMD,oBAI1E,OAAIA,EACO,CAAEC,OAAMP,SAAUsH,QAMhB1H,IAATW,EACO+G,EAIJ/G,CAAI,GAEnB,CAUA,SAAgBmL,EAAYjR,EAAaF,EAA8BoR,GAAiBC,GACpF,OAAID,IAAUjH,EAAMjK,IAChBiK,EAAMjK,GAAOD,EAAQC,EAAKF,GACnBmK,EAAMjK,IAEVC,QAAQqG,QAAQ2D,EAAMjK,GACjC,CA6BA,SAAgBoJ,EAAQC,EAA0BC,EAAO,IAErD,KADAA,EAAOA,EAAK6B,QAER,OAAO9B,EAGX,IAAI+H,EAAW9H,EAAKzI,MAAM,KACtB+E,EAASyD,EAEb,KAAOzD,GAAUwL,EAASlK,QAAQ,CAC9B,MAAM9C,EAAMgN,EAASC,QACrB,IAAKjN,GAAOgG,MAAMD,QAAQvE,GACtB,OAAOA,EAAOe,KAAI2K,GAAKlI,EAAQkI,EAAGF,EAASvN,KAAK,QAEhD+B,EAASA,EAAOxB,GAIxB,OAAOwB,CACX,CAkCA,SAAgBZ,EAAmBuM,GAC/B,OAAInH,MAAMD,QAAQoH,GACPA,EAEJ,CAACA,EACZ,CA0CA,SAAgB5N,EAAUlB,EAAe+O,GAErC,MAAMC,EAAUhP,EAAM5B,MAAM,KAAK,GACjC,OAAO4Q,EAAU/H,KAAK8D,MAAMgE,EAAI1C,KAAK2C,IAAY,IACrD,CAoDA,SAAgBzI,EACZC,EACAC,GAGA,MAAMwI,EAAiC,CAAC,EAExC,SAASC,EAAsBC,EAA0CC,GACjED,GAAWxH,MAAMD,QAAQyH,EAAQE,SACjCF,EAAQE,OAAOpG,SAAQ,EAAG8E,WAClBA,IACAkB,EAAIlB,GAAQkB,EAAIlB,IAAS,GACzBkB,EAAIlB,GAAMhF,KAAKqG,MAI/B,CAYA,OAVA7M,EAAUiE,GAAcyC,SAAQ4F,IACL,gBAAnBA,EAAE3Q,cAAkC2Q,EAAEpI,KAClCkB,MAAMD,QAAQmH,EAAEpI,IAChBoI,EAAEpI,GAAUwC,SAASkG,GAA6CD,EAAsBC,EAASN,KAEjGK,EAAsBL,EAAEpI,GAAWoI,OAKxCI,CACX,CAsKA,SAAgBvS,EAAO4S,EAAgB7L,GACnC,IAAM6L,EACF,MAAM,IAAI5R,MAAM+F,EAExB,CA3hBaqD,EAAAA,MAAQ,CACjByI,EAAAA,EAAG,KAAExB,EAAI,MAAED,IAEP,GADAD,EAAgB,CAAEE,OAAMD,UACZ,MAARC,EAAmB,OAAOD,EAC9B,GAAY,KAARC,EAAmB,OAAiB,IAAVD,EAC9B,GAAY,MAARC,EAAmB,OAAgB,KAATD,EAC9B,GAAY,WAARC,EAAmB,OAAgB,KAATD,EAC9B,GAAY,UAARC,EAAmB,OAAgB,KAATD,EAC9B,GAAY,MAARC,EAAmB,OAAe,MAARD,EAC9B,GAAY,WAARC,EAAmB,OAAe,MAARD,EAC9B,MAAM,IAAIpQ,MAAM,6BAA+BqQ,EACnD,EACAyB,EAAAA,EAAG,KAAEzB,EAAI,MAAED,IAEP,GADAD,EAAgB,CAAEE,OAAMD,UACZ,MAARC,EAAkB,OAAOD,EAC7B,GAAY,KAARC,EAAkB,OAAOD,EAAQ,IACrC,GAAIC,EAAKpR,MAAM,MAAO,OAAOmR,EAAQ,QACrC,GAAIC,EAAKpR,MAAM,MAAO,OAAOmR,EAAQ,OACrC,MAAM,IAAIpQ,MAAM,6BAA+BqQ,EACnD,EACA0B,IAAIC,IACA7B,EAAgB6B,GACTA,EAAG5B,QAgBlBhH,EAAAA,cAAAkH,EAcAlH,EAAAA,eAAAqH,EAIArH,EAAAA,aAAAsH,EAgCAtH,EAAAA,QAAAxJ,EA6DAwJ,EAAAA,YAAA0H,EAeA1H,EAAAA,0BAAA,SAA0CnH,EAAU,IAAKtC,GAErD,MAAME,EAAM0E,OAAOtC,GAASC,QAAQ,OAAQ,KAAO,WACnD,OAAO4O,EAAYjR,EAAKF,GAAgBiG,OAAOgF,IAC3C,MAAM,IAAI5K,MACN,mDAAmDH,OAAS+K,IAC/D,GAET,EAYAxB,EAAAA,QAAAH,EA6BAG,EAAAA,QAAA,SAAwBF,EAA0BC,EAAciH,EAAY6B,GAAc,GAetF,OAdA9I,EAAK6B,OAAOtK,MAAM,KAAKuK,QACnB,CAACc,EAAK9H,EAAKiO,EAAKC,KACZ,IAAIpG,GAAOmG,IAAQC,EAAIpL,OAAS,EAO5B,OAHIgF,QAAoB/G,IAAb+G,EAAI9H,IAAsBgO,IACjClG,EAAI9H,GAAOkO,EAAID,EAAM,GAAGjT,MAAM,YAAc,GAAK,CAAC,GAE/C8M,EAAMA,EAAI9H,QAAOe,EANxB+G,EAAI9H,GAAOmM,IASnBlH,GAEGA,CACX,EAQAE,EAAAA,UAAAvE,EAaAuE,EAAAA,SAAA,SAAyBD,EAAclH,GAEnC,OAAIkH,EAAKlK,MAAM,UACXkK,EAAKlK,MAAM,QADiBkK,EAEzB5E,OAAOtC,GAAW,IAAIC,QAAQ,OAAQ,IAAM,IAAMiH,EAAKjH,QAAQ,OAAQ,GAClF,EAUAkH,EAAAA,aAAA,SACIgJ,EAAY,EACZC,EAAU,kEAGV,MAAM5M,EAAS,GACT+F,EAAM6G,EAAQtL,OACpB,KAAOqL,KACH3M,EAAO4F,KAAKgH,EAAQC,OAAOC,KAAKC,MAAMD,KAAKE,SAAWjH,KAE1D,OAAO/F,EAAO/B,KAAK,GACvB,EAQA0F,EAAAA,UAAA5F,EAYA4F,EAAAA,gBAAA,SAAgCsJ,EAAuB,IAAKC,GACxD,OAAOJ,KAAKC,QAAQG,GAAQ,IAAIrL,MAAU,IAAOoL,EACrD,EASAtJ,EAAAA,yBAAA,SAAyCxG,EAAyCyO,GAE9E,MAAM9J,EAAMgL,KAAKC,MAAMlL,KAAKC,MAAQ,KAGpC,GAAI3E,EAAcgQ,WACd,OAAOrL,EAAM3E,EAAcgQ,WAI/B,GAAIhQ,EAAc8F,aAAc,CAC5B,IAAImK,EAAYrP,EAAUZ,EAAc8F,aAAc2I,GACtD,GAAIwB,GAAaA,EAAUC,IACvB,OAAOD,EAAUC,IAKzB,OAAOvL,EAAM,GACjB,EAcA6B,EAAAA,OAAAP,EA4CAO,EAAAA,QAAA,SACIN,EACAC,GAGA,MAAMgK,EAAOlK,EAAOC,EAAcC,GAClC,MAAO,IAAIiK,IAAUA,EAChB7I,QAAOkG,GAASA,EAAO,KAAO0C,IAC9B9H,QACG,CAACC,EAAMmF,IAASnF,EAAK/D,OAAO4L,EAAK1C,EAAO,MACxC,GAEZ,EAMAjH,EAAAA,gBAAA,SAAgCtI,EAAkDN,GAG9E,MAGMyS,GAHYhK,EAAQnI,EAAa,oBAAsB,IAGtC6F,MAAMuM,GAAWA,EAAE3F,OAAS/M,IACnD,IAAKyS,EACD,MAAM,IAAIjT,MAAM,aAAaQ,2CAIjC,IAAKyJ,MAAMD,QAAQiJ,EAAKjS,aACpB,MAAM,IAAIhB,MAAM,uCAAuCQ,0BAI3D,GAAoB,WAAhBA,GAA6ByS,EAAKjS,YAAY2F,MAAMwM,GAAqB,OAAVA,EAAEhG,OACjE,MAAO,MAIX,MAAMpB,EAAM5N,EAAAiV,cAAczM,MAAK0M,GAAKJ,EAAKjS,YAAY2F,MAAMwM,GAAWA,EAAEhG,MAAQkG,MAGhF,IAAKtH,EACD,MAAM,IAAI/L,MAAM,sCAAwCQ,GAG5D,OAAOuL,CACX,EASA3C,EAAAA,gBAAOjJ,eAA+BmT,EAAiCC,EAAgB,IAAKC,EAAiB,KAUzG,GALqB,mBAAVF,IACPA,QAAeA,KAIfA,GAA2B,iBAAVA,EACjB,OAAOA,EAIX,GAAqB,iBAAVA,EAEP,OADApD,EAAO,4DAA6DoD,GAC7DG,KAIX,GAAc,SAAVH,EACA,OAAOG,KAIX,GAAc,WAAVH,EACA,OAAOI,OAIX,GAAc,QAAVJ,EACA,OAAOK,KAAOF,KAIlB,GAAc,UAAVH,EAAoB,CACpB,IAAIzN,EAAO+N,EAA8B,KACzC,IAEI,GADAA,EAAetV,OAAOuV,KAAK,GAAI,mBAC1BD,EACD,MAAM,IAAI5T,MAAM,mCAEtB,MAAO8T,GACLjO,EAAQiO,EAGZ,OAAKF,IACD1D,EAAO,kDAAmDrK,GACnD4N,MAOf,GAAc,SAAVH,EAAmB,CACnB,IAAIzN,EAAO+N,EAA8B,KAEzC,IAUI,GATAA,EAAetV,OAAOuV,KAAK,GAAI,iBAAkB,CAC7C,UAAYL,EACZ,SAAWD,EACX,YACA,cACA,WACA,QAAUQ,OAAOP,OAASA,GAAU,EACpC,SAAWO,OAAOR,MAAQA,GAAS,GACrC7P,KAAK,OACFkQ,EACD,MAAM,IAAI5T,MAAM,wCAEtB,MAAO8T,GACLjO,EAAQiO,EAGZ,OAAKF,IACD1D,EAAO,kDAAmDrK,GACnD4N,MAQf,OAD2BO,OAAOV,KAKlCpD,EAAO,gDAAiDoD,GACjDG,KACX,EAEArK,EAAAA,OAAApK,EAMAoK,EAAAA,gBAAA,SAAgCO,GAC5B3K,EAAOiL,MAAMD,QAAQL,GAAQ,mCAC7B3K,EAAO2K,EAAM5C,OAAS,EAAG,4CACzB4C,EAAM4B,SAAS0I,IACXjV,EACI,CAAC,MAAO,UAAW,OAAQ,OAAQ,OAAQ,UAAU6B,QAAQoT,EAAUC,KAAO,EAC9E,4HAEJlV,EAAOiV,EAAU9K,aAAe8K,EAAU9K,KAAM,YAAY8K,EAAUC,0CAElD,OAAhBD,EAAUC,IAA+B,WAAhBD,EAAUC,IAAmC,QAAhBD,EAAUC,IAChElV,EAAO,UAAWiV,EAAW,YAAYA,EAAUC,2CACnDlV,EAAwC,GAAjCgI,OAAO0E,KAAKuI,GAAWlN,OAAa,YAAYkN,EAAUC,+CAG5C,QAAhBD,EAAUC,IAAgC,QAAhBD,EAAUC,IACzClV,EAAgC,iBAAlBiV,EAAUtB,KAAkB,YAAYsB,EAAUC,oDAChElV,EAAwC,GAAjCgI,OAAO0E,KAAKuI,GAAWlN,OAAa,YAAYkN,EAAUC,+CAIjElV,EAAwC,GAAjCgI,OAAO0E,KAAKuI,GAAWlN,OAAa,YAAYkN,EAAUC,gDAG7E,wKC/kBA,MAAAlG,EAAA/P,EAAA,KAIMkW,EAAuC,iBAAfC,YAA2BA,WAAWD,OAChEC,WAAWD,OACXlW,EAAAA,KAAAA,QAEEoW,EAASA,KACX,IAAKF,EAAOE,OAAQ,CAChB,IAAKD,WAAWE,gBACZ,MAAM,IAAItU,MACN,2NAMR,MAAM,IAAIA,MACN,mHAIR,OAAOmU,EAAOE,MAAM,EASlBE,EAAO,CACTC,MAAO,CACHrH,KAAM,QACNsH,WAAY,SAEhBC,MAAO,CACHvH,KAAM,oBACNwH,cAAe,KACfC,eAAgB,IAAIC,WAAW,CAAC,EAAG,EAAG,IACtCC,KAAM,CACF3H,KAAM,aAKlB,SAAgB4H,EAAY7I,GACxB,OAAOiI,EAAOa,gBAAgB,IAAIH,WAAW3I,GACjD,CAEO/L,eAAe8U,EAAa3D,GAC/B,MAAM4D,GAAW,IAAIC,aAAcC,OAAO9D,GACpCwD,QAAaT,IAASgB,OAAO,UAAWH,GAC9C,OAAO,IAAIL,WAAWC,EAC1B,CARA1L,EAAAA,YAAA2L,EAIA3L,EAAAA,aAAA6L,EAMa7L,EAAAA,sBAAwBjJ,MAAOmV,EAAU,MAClD,MAAMC,EAAgBR,EAAYO,GAC5BE,GAAgB,EAAAxH,EAAAgB,gBAAeuG,GAAY,GAEjD,MAAO,CAAEE,eADa,EAAAzH,EAAAgB,sBAAqBiG,EAAaO,IAAe,GAC/CA,eAAc,EAG1CpM,EAAAA,UAAOjJ,eAAyBuV,GAE5B,IAAKA,EAAIC,IACL,MAAM,IAAI3V,MAAM,mEAYpB,GALKiK,MAAMD,QAAQ0L,EAAIE,WACnBF,EAAIE,QAAU,CAAC,UAIdF,EAAIE,QAAQxK,SAAS,QACtB,MAAM,IAAIpL,MAAM,6DAGpB,IACI,aAAaqU,IAASwB,UAClB,MACAH,EACAnB,EAAKmB,EAAIC,MACG,IAAZD,EAAII,IACJJ,EAAIE,SAEV,MAAO9B,GACL,MAAM,IAAI9T,MAAM,OAAO0V,EAAIC,yCAAyC7B,KAE5E,EAEA1K,EAAAA,eAAOjJ,eAA8BwV,EAAwBI,EAAuBC,EAAa1E,GAE7F,MAAM2E,EAAa1M,KAAKC,UAAU,IAAKwM,EAAQL,QACzCO,EAAa3M,KAAKC,UAAU8H,GAC5B6E,EAA0B,IAAG,EAAAnI,EAAAe,WAAUkH,OAAc,EAAAjI,EAAAe,WAAUmH,KAE/DE,QAAkB/B,IAASgC,KAC7B,IAAKN,EAAWO,UAAWxB,KAAM,WACjCiB,GACA,IAAIZ,aAAcC,OAAOe,IAG7B,MAAO,GAAGA,MAA2B,EAAAnI,EAAAgB,gBAAe,IAAI6F,WAAWuB,IAAY,IACnF,oJC1GahN,EAAAA,mBAAqB,CAC9B,UACA,eACA,qBACA,cACA,sBACA,aACA,QACA,WACA,gBACA,WACA,WACA,aACA,QACA,gBACA,qBACA,gBACA,uBACA,cACA,YACA,UACA,WACA,6BACA,8BACA,gBACA,gBACA,mBACA,qBACA,kBACA,mBACA,mBACA,oBACA,qBACA,YACA,oBACA,gBACA,uBACA,sBACA,OACA,OACA,QACA,kBACA,yBACA,eACA,eACA,yBACA,6BACA,UACA,OACA,gBACA,QACA,2BACA,qBACA,kBACA,oBACA,sBACA,oBACA,iBACA,cACA,QACA,UACA,SACA,YACA,mBACA,aACA,wBACA,kBACA,gBACA,eACA,kBACA,iBACA,WACA,iBACA,WACA,iBACA,gBACA,sBAMSA,EAAAA,aAAe,CACxB,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,GAOAA,EAAAA,cAAgB,CACzB,UACA,UACA,YACA,SACA,QACA,eAMSA,EAAAA,UAAY,sOCtHzB,MAAApL,EAAAC,EAAA,KAYA4P,EAAA5P,EAAA,KACAE,EAAAF,EAAA,KAKsB+I,OAAAA,eAAAA,EAAAA,MAAAA,CAAAuP,YAAA,EAAArS,IAAA,kBALb/F,EAAAgG,SAAS,IAGlB,MAAM5F,EAAQP,EAAAO,MAAOC,OAAO,UAI5B,SAASgY,IACL,MAAyB,iBAAXlY,MAClB,CAQA,SAAgBmY,EAAmBxU,EAAU,IAAKtC,GAE9C,MAAME,EAAM0E,OAAOtC,GAASC,QAAQ,OAAQ,KAAO,kCACnD,OAAO,EAAAlE,EAAA8S,aAAYjR,EAAKF,GAAgBiG,OAAOgF,IAC3C,MAAM,IAAI5K,MAAM,wCAAwCH,OAAS+K,EAAG7E,UAAU,GAEtF,CAgEA,SAAgB2Q,EAAsBzU,EAAU,KAE5C,OA7DJ,SAAgDA,EAAU,KAEtD,OAAOwU,EAAmBxU,OAFiCtC,GAER6F,MAAKyN,IACpD,IAAKA,EAAK0D,yBAA2B1D,EAAK2D,eACtC,MAAM,IAAI5W,MAAM,yBAEpB,MAAO,CACH6W,gBAAsB5D,EAAK6D,uBAA0B,GACrD/T,aAAsBkQ,EAAK0D,uBAC3B/O,SAAsBqL,EAAK2D,eAC3BG,qBAAsB9D,EAAK+D,kCAAoC,GAClE,GAET,CAgDWC,CAAuChV,GACzC2D,OAAM,IA5Cf,SAAuD3D,EAAU,IAAKtC,GAElE,OAAO,EAAA3B,EAAA+C,2BAA0BkB,EAAStC,GAAgB6F,MAAKyN,IAC3D,MACMiE,IAAe,EAAAlZ,EAAAiL,SAAQgK,GAAQ,CAAC,EAAG,8BAAgC,IACpE9I,QAAO2J,GAFE,0EAEGA,EAAEjU,MACd2G,KAAI2K,GAAKA,EAAEgG,YAAW,GAErBpL,EAAyC,CAC3C8K,gBAAsB,GACtB9T,aAAsB,GACtB6E,SAAsB,GACtBmP,qBAAsB,IAiB1B,OAdIG,GACAA,EAAW3L,SAAQuK,IACC,aAAZA,EAAIjW,MACJkM,EAAI8K,gBAAkBf,EAAIsB,UAEd,cAAZtB,EAAIjW,MACJkM,EAAIhJ,aAAe+S,EAAIsB,UAEX,UAAZtB,EAAIjW,MACJkM,EAAInE,SAAWkO,EAAIsB,aAKxBrL,CAAG,GAElB,CAaqBsL,CAA8CpV,IACnE,CAYO9B,eAAemP,EAClB+B,EACAiG,EAAoE,CAAC,GAGrE,MAAMzX,EAAMwR,EAAIhD,SAGhB,GAAIpE,MAAMD,QAAQsN,GAAS,CACvB,MAAMC,EAAS1X,EAAIqB,aAAagD,IAAI,QAAUrE,EAAIqB,aAAagD,IAAI,kBACnE,IAAKqT,EACD,MAAM,IAAIvX,MACN,2FAKR,MAAMwX,EAAMF,EAAO3Q,MAAKwM,IACpB,GAAIA,EAAEsE,SAAU,CACZ,GAA0B,mBAAftE,EAAEsE,SACT,QAAStE,EAAEsE,SAASF,GAExB,GAA0B,iBAAfpE,EAAEsE,SACT,OAAOtE,EAAEsE,WAAaF,EAE1B,GAAIpE,EAAEsE,oBAAoBC,OACtB,OAAOvE,EAAEsE,SAASE,KAAKJ,GAG/B,OAAO,CAAK,IAGhB,OADA,EAAAvZ,EAAAgB,QAAOwY,EAAK,gEAAgED,YAC/DjI,EAAU+B,EAAKmG,GAKhC,MAAM,aACF/O,EAAY,kBACZmP,EAAiB,YACjBC,EAAW,OACXvE,EAAM,MACNC,EAAK,OACLC,EAAM,SACNsE,EAAQ,sBACRC,EAAqB,aAErBC,EAAY,UACZC,GACAX,EAEJ,IAAI,IACAY,EAAG,OACHC,EAAM,UACNxV,EAAS,eACTyV,EAAc,YACdC,EAAW,WACXC,EAAU,MACVzV,EAAQ,GAAE,SACVsF,EAAQ,iBACRoQ,EAAgB,iBAChBC,EAAgB,SAChBC,GACAnB,EAEJ,MAAMvT,EAAUsN,EAAIrN,aAGpBkU,EAAiBrY,EAAIqB,aAAagD,IAAI,QAAqBgU,EAC3DE,EAAiBvY,EAAIqB,aAAagD,IAAI,mBAAqBkU,EAC3DD,EAAiBtY,EAAIqB,aAAagD,IAAI,WAAqBiU,EAC3DxV,EAAiB9C,EAAIqB,aAAagD,IAAI,cAAqBvB,EAC3DwF,EAAiBtI,EAAIqB,aAAagD,IAAI,aAAqBiE,EAGtDA,IACDA,EAAW8P,GAEVI,IACDA,EAAcL,GAGbK,EAEOA,EAAYpZ,MAAM,mBAC1BoZ,EAAchH,EAAIjD,SAASiK,IAF3BA,EAAchH,EAAIjD,SAAS,KAK/B,MAAMrP,EAAYwF,OAAO2T,GAAOE,GAAkB,IAGlD,IAAKrZ,EACD,MAAM,IAAIiB,MACN,uFAcR,GATIkY,GACA3Z,EAAM,sBAAuB4Z,EAAS,MAAQ,cAI9CA,IAAWtV,EAAM5D,MAAM,YACvB4D,GAAS,WAGT2T,IAAa,CACb,MAAMkC,EAAUC,IACVC,EAAUC,KAEXH,GAAWE,KAAiC,IAArBL,IAAkD,IAArBA,IAMrDA,EAAmBG,EAInB7N,QAAQC,KACJ,2TAWZ,MAAMgO,QAAe/U,EAAQG,IAAI/F,EAAAgG,iBAC3BJ,EAAQK,MAAM0U,GAEpBL,EAAWA,QAAAA,GAAY,EAAAza,EAAA+a,cAAa,IAGpC,MAAMla,EAAgC,CAClCsJ,WACAtF,QACAwV,cACAtZ,YACA0J,eACA+P,mBACA5V,cAAe,CAAC,EAChBqB,IAAKwU,EACLF,mBACAR,2BAG8BvB,MAC9B,EAAAxY,EAAAiL,SAAQoI,EAAK,6CAIPtN,EAAQ5C,IAAIhD,EAAAgG,UAAWsU,GAI7Bb,GACA5Q,OAAOC,OAAOpI,EAAM+D,cAAgBgV,GAIpCjV,GACAqE,OAAOC,OAAOpI,EAAM+D,cAAgB,CAAErD,QAASoD,IAI/CkV,GACA7Q,OAAOC,OAAOpI,EAAM+D,cAAgB,CAAErB,UAAWsW,IAGrD,IAAImB,EAAcX,EAAc,UAAYpQ,mBAAmBwQ,GAG/D,GAAIL,IAAmBF,EAGnB,OAFA3Z,EAAM,+BACAwF,EAAQ5C,IAAIsX,EAAU5Z,GACxByZ,EACOU,QAEE3H,EAAI9C,SAASyK,GAI9B,MAAM9B,QAAmBR,EAAsB3X,GAK/C,GAJAiI,OAAOC,OAAOpI,EAAOqY,SACfnT,EAAQ5C,IAAIsX,EAAU5Z,IAGvBA,EAAMkE,aACP,OAAIuV,EACOU,QAEE3H,EAAI9C,SAASyK,GAI9B,MAAMC,EAAiB,CACnB,qBACA,aAAkBhR,mBAAmBE,GAAY,IACjD,SAAkBF,mBAAmBpF,GACrC,gBAAkBoF,mBAAmBoQ,GACrC,OAAkBpQ,mBAAmBlJ,GACrC,SAAkBkJ,mBAAmBwQ,IAQzC,GAJIN,GACAc,EAAe5N,KAAK,UAAYpD,mBAAmBkQ,IAqD3D,SAAgCe,EAAwBpB,GACpD,GAAiB,aAAbA,EACA,OAAO,EAEX,GAAiB,aAAbA,EACA,OAAO,EAEX,GAAiB,aAAbA,EAAyB,CACzB,IAAKoB,EACD,MAAM,IAAIlZ,MAAM,gHAEpB,OAAO,EAEX,OAAOkZ,CACX,CAhEQC,CAAuBjC,EAAWH,qBAAqB3L,SAAS,QAAS0M,GAAW,CACpF,IAAI9E,QAAc3B,EAAItD,SAASqL,wBAC/BpS,OAAOC,OAAOpI,EAAOmU,SACfjP,EAAQ5C,IAAIsX,EAAU5Z,GAC5Boa,EAAe5N,KAAK,kBAAoBxM,EAAM4W,eAC9CwD,EAAe5N,KAAK,8BAKxB,GAFA2N,EAAcna,EAAMkE,aAAe,IAAMkW,EAAevV,KAAK,KAEzD4U,EACA,OAAOU,EAGX,IAAI1F,IAAUkD,IAgCV,aAAanF,EAAI9C,SAASyK,GAhC9B,CACI,IAAIK,EAIJ,GAFAA,QAAY,EAAArb,EAAAsb,iBAAgBhG,EAAQC,EAAOC,GAEvC6F,IAAQ5F,KACR,IAGI4F,EAAIE,eAAeC,WAAWV,GAC9BO,EAAIE,eAAeE,QAAQhB,EAAUlP,KAAKC,UAAU3K,IACtD,MAAO+L,IACL,EAAA5M,EAAAO,OAAO,4GAA6GqM,GACpHyO,EAAM5F,KAId,GAAI4F,IAAQ5F,KACR,IACI4F,EAAI/K,SAASlN,KAAO4X,EACpBvF,KAAKiG,iBAAiB,UAAWC,GACnC,MAAO/O,IACL,EAAA5M,EAAAO,OAAO,sGAAuGqM,GAC9G6I,KAAKnF,SAASlN,KAAO4X,OAGzBvF,KAAKnF,SAASlN,KAAO4X,EAQjC,CAwBA,SAAgBL,IACZ,IACI,OAAOlF,OAASE,KAAOD,SAAWD,KACpC,MAAOK,GACL,OAAO,EAEf,CASA,SAAgB+E,IACZ,IACI,OAAOpF,OAASE,OACPiG,QACFA,SAAWnG,QACTnV,OAAO6O,KAClB,MAAO2G,GACL,OAAO,EAEf,CAOA,SAAgB6F,EAAU7F,GACH,gBAAfA,EAAE7N,KAAKsH,MAA0BuG,EAAE+F,SAAW,IAAIxY,IAAIoS,KAAKnF,SAASlN,MAAMyY,SAC1Evb,OAAOwb,oBAAoB,UAAWH,GACtCrb,OAAOgQ,SAASlN,KAAO0S,EAAE7N,KAAKpG,IAEtC,CAQOM,eAAeiP,EAAMiC,EAAyBnR,EAAmC,CAAC,WAErF,MAAML,EAAMwR,EAAIhD,SACV0L,EAAU1I,EAAIrN,aACdsT,EAASzX,EAAIqB,aAEnB,IAAI+C,EAAyBqT,EAAOpT,IAAI,UAAYhE,EAAQuY,SAC5D,MAAMpI,EAAuBiH,EAAOpT,IAAI,SAAYhE,EAAQmQ,KACtD2J,EAAuB1C,EAAOpT,IAAI,SAClC+V,EAAuB3C,EAAOpT,IAAI,qBAexC,GAbKD,IACDA,QAAY8V,EAAQ7V,IAAI/F,EAAAgG,YAYxB6V,GAAaC,EACb,MAAM,IAAIja,MAAM,CACZga,EACAC,GACF9P,OAAOC,SAAS1G,KAAK,OAG3BnF,EAAM,oBAAqB0F,EAAKoM,IAGhC,EAAArS,EAAAgB,QAAOiF,EAAK,0DAGZ,IAAIpF,QAAekb,EAAQ7V,IAAID,GAE/B,MAAMkK,GAA4BqI,MAC9B,EAAAxY,EAAAiL,SAAQoI,EAAK,qCAKjB,GAAImF,KAAe3X,IAAUA,EAAM0Z,iBAAkB,CAEjD,MAAMG,EAAUC,IACVC,EAAUC,IAQhB,IAAKH,GAAWE,KAAa/Y,EAAIqB,aAAagD,IAAI,YAAa,CAC3DrE,EAAIqB,aAAaC,IAAI,WAAY,KACjC,MAAM,KAAEC,EAAI,OAAEyY,GAAWha,EASzB,OARI6Y,GACAhF,OAAOwG,YAAY,CAAE3M,KAAM,eAAgB1N,IAAKuB,GAAQyY,GAExDjB,IACAgB,OAAOM,YAAY,CAAE3M,KAAM,eAAgB1N,IAAKuB,GAAQyY,GACxDvb,OAAO6b,SAGJ,IAAIra,SAAQ,UAI3BD,EAAIqB,aAAawI,OAAO,YAGxB,MAAM0Q,KAAW9C,EAAO+C,IAAI,WAAYna,EAAQuY,UA0ChD,GAxCIjC,MAAe,EAAAxY,EAAAiL,SAAQoI,EAAK,mCAAqChB,GAAQ+J,KAIrE/J,IACAiH,EAAO5N,OAAO,QACdnL,EAAM,yCASN6b,GAAYjM,IACZmJ,EAAO5N,OAAO,SACdnL,EAAM,0CASND,OAAOgc,QAAQC,cACfjc,OAAOgc,QAAQC,aAAa,CAAC,EAAG,GAAI1a,EAAIuB,QAKhD,EAAApD,EAAAgB,QAAOH,EAAO,8CAIMwR,KAA2B,QAAnB1L,EAAA9F,EAAM+D,qBAAa,IAAA+B,OAAA,EAAAA,EAAE+D,eAI9B7J,EAAM+I,SAAU,EAE/B,EAAA5J,EAAAgB,QAAOqR,EAAM,oCAEb9R,EAAM,sDACN,MAAMoB,QAAuB6a,EAAkBnJ,EAAK,CAChDhB,OACAxR,QACAkZ,sBAAuB7X,EAAQ6X,sBAC/BhC,WAAY7V,EAAQ6V,YAAclX,EAAM2Z,mBAE5Cja,EAAM,4BAA6BoB,GAKnC,MAAMiD,QAAsB,EAAA5E,EAAA4B,SAAkCf,EAAM+I,SAAUjI,GAC9EpB,EAAM,qBAAsBqE,IAC5B,EAAA5E,EAAAgB,QAAO4D,EAAc8F,aAAc,kCAGnC7J,EAAMwI,WAAY,EAAArJ,EAAA2K,0BAAyB/F,EAAeyO,GAI1DxS,EAAQ,IAAKA,EAAO+D,uBACdmX,EAAQ5Y,IAAI8C,EAAKpF,GACvBN,EAAM,kCAGNA,GAAyB,QAAnBmJ,EAAA7I,EAAM+D,qBAAa,IAAA8E,OAAA,EAAAA,EAAEgB,cACvB,qBACA,2BAIJyF,SACM4L,EAAQ5Y,IAAIhD,EAAAgG,UAAWF,GAGjC,MAAM3E,EAAS,IAAIuO,EAAAnP,QAAO2S,EAAKxS,GAE/B,OADAN,EAAM,8BAA+Be,GAC9BA,CACX,CAMOa,eAAeqa,EAClBnJ,GACA,KACIhB,EAAI,MACJxR,EAAK,sBACLkZ,EAAqB,WACrBhC,IA8BJ,MAAM,YAAEsC,EAAW,aAAE5P,EAAY,SAAEb,EAAQ,SAAEO,EAAQ,aAAEqN,GAAiB3W,GAExE,EAAAb,EAAAgB,QAAOqZ,EAAa,8BACpB,EAAAra,EAAAgB,QAAO4I,EAAU,2BACjB,EAAA5J,EAAAgB,QAAOmJ,EAAU,0BAEjB,MAAMxI,EAAsC,CACxC4I,OAAQ,OACRlD,QAAS,CAAE,eAAgB,qCAC3BM,KAAM,QAAQ0K,gDACVpI,mBAAmBoQ,MAU3B,GAAI5P,EACA9I,EAAe0F,QAAQC,cAAgB,SAAW+L,EAAIxN,KAClDsE,EAAW,IAAMM,GAErBlK,EACI,qEACAoB,EAAe0F,QAAQC,oBAK1B,GAAIyQ,EAAY,CAEjB,MAAM0E,EAAK,QAAS1E,EAChBA,EAAW9R,UACLoN,EAAItD,SAAS2M,UAAU3E,GAE3B4E,EAAa,CACfC,IAAK,MACLC,IAAK9E,EAAW8E,IAChBC,IAAK/C,GAAyBlZ,EAAMkZ,uBAGlCgD,EAAY,CACd7C,IAAK/P,EACLuC,IAAKvC,EACL6S,IAAKpT,EACLqT,IAAK5J,EAAIxC,gBAAgBwC,EAAItD,SAASgH,YAAY,KAClDjC,KAAK,EAAA9U,EAAAkd,iBAAgB,MAGnBC,QAAwB9J,EAAItD,SAASqN,eAAerF,EAAWJ,IAAK8E,EAAIE,EAAYI,GAC1Fpb,EAAegG,MAAQ,0BAA0BsC,mBAAmB,4DACpEtI,EAAegG,MAAQ,qBAAqBsC,mBAAmBkT,KAC/D5c,EAAM,gFAKNA,EAAM,kEACNoB,EAAegG,MAAQ,cAAcsC,mBAAmBE,KAS5D,OANIqN,IACFjX,EAAM,qDAENoB,EAAegG,MAAQ,kBAAoB6P,GAGtC7V,CACX,CAhrBAyJ,EAAAA,mBAAAqN,EAsEArN,EAAAA,sBAAAsN,EAgBAtN,EAAAA,UAAAkG,EA+RAlG,EAAAA,UAAAuP,EAeAvP,EAAAA,UAAAyP,EAgBAzP,EAAAA,UAAAuQ,EAaAvQ,EAAAA,MAAAgG,EAqKAhG,EAAAA,kBAAAoR,EA0IApR,EAAAA,KAAOjJ,eACHkR,EACAgK,EACAC,GAGA,MAAMzb,EAAQwR,EAAIhD,SACZgC,EAAQxQ,EAAIqB,aAAagD,IAAI,QAC7BrF,EAAQgB,EAAIqB,aAAagD,IAAI,SAGnC,GAAImM,GAAQxR,EACR,OAAOuQ,EAAMiC,EAAKiK,GAMtB,MAAMvX,EAAUsN,EAAIrN,aACdC,EAAUpF,SAAekF,EAAQG,IAAI/F,EAAAgG,WACrCoX,QAAgBxX,EAAQG,IAAID,GAClC,OAAIsX,EACO,IAAI1N,EAAAnP,QAAO2S,EAAKkK,GAIpBjM,EAAU+B,EAAKgK,GAAkB7V,MAAK,IAQlC,IAAI1F,SAAQ,UAE3B,6ECnxBAsJ,EAAAA,QAAA,MAMI,SAAMlF,CAAID,GAEN,MAAMmM,EAAQmJ,eAAetV,GAC7B,OAAImM,EACO7G,KAAK8D,MAAM+C,GAEf,IACX,CAMA,SAAMjP,CAAI8C,EAAamM,GAGnB,OADAmJ,eAAetV,GAAOsF,KAAKC,UAAU4G,GAC9BA,CACX,CAOA,WAAMhM,CAAMH,GAER,OAAIA,KAAOsV,wBACAA,eAAetV,IACf,EAGf,8ECpCJmF,EAAAA,QAAe,CACXpD,QAAe,4CACflD,aAAe,qHACfE,WAAe,8DACfC,cAAe,6GCCnBmG,EAAQoS,WA6IR,SAAoBnM,GAQnB,GAPAA,EAAK,IAAMjQ,KAAKqc,UAAY,KAAO,IAClCrc,KAAKsc,WACJtc,KAAKqc,UAAY,MAAQ,KAC1BpM,EAAK,IACJjQ,KAAKqc,UAAY,MAAQ,KAC1B,IAAM1L,EAAO3G,QAAQuS,SAASvc,KAAKwc,OAE/Bxc,KAAKqc,UACT,OAGD,MAAMI,EAAI,UAAYzc,KAAK0c,MAC3BzM,EAAK0M,OAAO,EAAG,EAAGF,EAAG,kBAKrB,IAAIG,EAAQ,EACRC,EAAQ,EACZ5M,EAAK,GAAGnN,QAAQ,eAAejD,IAChB,OAAVA,IAGJ+c,IACc,OAAV/c,IAGHgd,EAAQD,GACT,IAGD3M,EAAK0M,OAAOE,EAAO,EAAGJ,EACvB,EA7KAzS,EAAQ8S,KA+LR,SAAcC,GACb,IACKA,EACH/S,EAAQrF,QAAQ0V,QAAQ,QAAS0C,GAEjC/S,EAAQrF,QAAQyV,WAAW,QAE7B,CAAE,MAAO3T,GAGT,CACD,EAzMAuD,EAAQgT,KAiNR,WACC,IAAIlJ,EACJ,IACCA,EAAI9J,EAAQrF,QAAQsY,QAAQ,QAC7B,CAAE,MAAOxW,GAGT,CAOA,OAJKqN,GAAwB,oBAAZlC,SAA2B,QAASA,UACpDkC,EAAIlC,QAAQK,IAAIiL,OAGVpJ,CACR,EA/NA9J,EAAQqS,UAyGR,WAIC,GAAsB,oBAAXnd,QAA0BA,OAAO0S,UAAoC,aAAxB1S,OAAO0S,QAAQzD,MAAuBjP,OAAO0S,QAAQuL,QAC5G,OAAO,EAIR,GAAyB,oBAAdC,WAA6BA,UAAUC,WAAaD,UAAUC,UAAU7L,cAAc3R,MAAM,yBACtG,OAAO,EAGR,IAAIyd,EAIJ,MAA4B,oBAAbC,UAA4BA,SAASC,iBAAmBD,SAASC,gBAAgBC,OAASF,SAASC,gBAAgBC,MAAMC,kBAEpH,oBAAXxe,QAA0BA,OAAOuM,UAAYvM,OAAOuM,QAAQkS,SAAYze,OAAOuM,QAAQmS,WAAa1e,OAAOuM,QAAQoS,QAGrG,oBAAdT,WAA6BA,UAAUC,YAAcC,EAAIF,UAAUC,UAAU7L,cAAc3R,MAAM,oBAAsBie,SAASR,EAAE,GAAI,KAAO,IAE/H,oBAAdF,WAA6BA,UAAUC,WAAaD,UAAUC,UAAU7L,cAAc3R,MAAM,qBACtG,EAjIAmK,EAAQrF,QA2OR,WACC,IAGC,OAAOoZ,YACR,CAAE,MAAOtX,GAGT,CACD,CApPkBuX,GAClBhU,EAAQiU,QAAU,MACjB,IAAIC,GAAS,EAEb,MAAO,KACDA,IACJA,GAAS,EACTzS,QAAQC,KAAK,yIACd,CAED,EATiB,GAelB1B,EAAQmU,OAAS,CAChB,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAwFDnU,EAAQoU,IAAM3S,QAAQtM,OAASsM,QAAQ2S,KAAO,MAAS,GAkEvDzN,EAAO3G,QAAU,EAAQ,IAAR,CAAoBA,GAErC,MAAM,WAACqU,GAAc1N,EAAO3G,QAM5BqU,EAAWC,EAAI,SAAU3Q,GACxB,IACC,OAAOxD,KAAKC,UAAUuD,EACvB,CAAE,MAAOlH,GACR,MAAO,+BAAiCA,EAAME,OAC/C,CACD,iBCGAgK,EAAO3G,QA3QP,SAAeiI,GAqDd,SAASsM,EAAYjC,GACpB,IAAIkC,EAEAC,EACAC,EAFAC,EAAiB,KAIrB,SAASxf,KAAS8Q,GAEjB,IAAK9Q,EAAMyf,QACV,OAGD,MAAMvK,EAAOlV,EAGP0f,EAAOC,OAAO,IAAI5W,MAClB6W,EAAKF,GAAQL,GAAYK,GAC/BxK,EAAKmI,KAAOuC,EACZ1K,EAAKvI,KAAO0S,EACZnK,EAAKwK,KAAOA,EACZL,EAAWK,EAEX5O,EAAK,GAAKsO,EAAYS,OAAO/O,EAAK,IAEX,iBAAZA,EAAK,IAEfA,EAAKgP,QAAQ,MAId,IAAIrC,EAAQ,EACZ3M,EAAK,GAAKA,EAAK,GAAGnN,QAAQ,iBAAiB,CAACjD,EAAOqf,KAElD,GAAc,OAAVrf,EACH,MAAO,IAER+c,IACA,MAAMuC,EAAYZ,EAAYF,WAAWa,GACzC,GAAyB,mBAAdC,EAA0B,CACpC,MAAMC,EAAMnP,EAAK2M,GACjB/c,EAAQsf,EAAUE,KAAKhL,EAAM+K,GAG7BnP,EAAK0M,OAAOC,EAAO,GACnBA,GACD,CACA,OAAO/c,CAAK,IAIb0e,EAAYnC,WAAWiD,KAAKhL,EAAMpE,IAEpBoE,EAAK+J,KAAOG,EAAYH,KAChCkB,MAAMjL,EAAMpE,EACnB,CAgCA,OA9BA9Q,EAAMmd,UAAYA,EAClBnd,EAAMkd,UAAYkC,EAAYlC,YAC9Bld,EAAMud,MAAQ6B,EAAYgB,YAAYjD,GACtCnd,EAAMC,OAASA,EACfD,EAAM8e,QAAUM,EAAYN,QAE5BrW,OAAO4X,eAAergB,EAAO,UAAW,CACvCgY,YAAY,EACZsI,cAAc,EACd3a,IAAK,IACmB,OAAnB6Z,EACIA,GAEJF,IAAoBF,EAAYxB,aACnC0B,EAAkBF,EAAYxB,WAC9B2B,EAAeH,EAAYK,QAAQtC,IAG7BoC,GAER3c,IAAK4L,IACJgR,EAAiBhR,CAAC,IAKY,mBAArB4Q,EAAYpO,MACtBoO,EAAYpO,KAAKhR,GAGXA,CACR,CAEA,SAASC,EAAOkd,EAAWoD,GAC1B,MAAMC,EAAWpB,EAAYve,KAAKsc,gBAAkC,IAAdoD,EAA4B,IAAMA,GAAapD,GAErG,OADAqD,EAASvB,IAAMpe,KAAKoe,IACbuB,CACR,CAwFA,SAASC,EAAYC,GACpB,OAAOA,EAAOC,WACZC,UAAU,EAAGF,EAAOC,WAAWnY,OAAS,GACxC7E,QAAQ,UAAW,IACtB,CA0BA,OAvQAyb,EAAYpf,MAAQof,EACpBA,EAAYjf,QAAUif,EACtBA,EAAYS,OAoPZ,SAAgBI,GACf,OAAIA,aAAexe,MACXwe,EAAIY,OAASZ,EAAIzY,QAElByY,CACR,EAxPAb,EAAY0B,QAwLZ,WACC,MAAMlD,EAAa,IACfwB,EAAY2B,MAAM9Y,IAAIwY,MACtBrB,EAAY4B,MAAM/Y,IAAIwY,GAAaxY,KAAIkV,GAAa,IAAMA,KAC5DhY,KAAK,KAEP,OADAia,EAAY6B,OAAO,IACZrD,CACR,EA9LAwB,EAAY6B,OAsJZ,SAAgBrD,GAOf,IAAI7R,EANJqT,EAAYzB,KAAKC,GACjBwB,EAAYxB,WAAaA,EAEzBwB,EAAY2B,MAAQ,GACpB3B,EAAY4B,MAAQ,GAGpB,MAAM7e,GAA+B,iBAAfyb,EAA0BA,EAAa,IAAIzb,MAAM,UACjE8K,EAAM9K,EAAMqG,OAElB,IAAKuD,EAAI,EAAGA,EAAIkB,EAAKlB,IACf5J,EAAM4J,KAOW,OAFtB6R,EAAazb,EAAM4J,GAAGpI,QAAQ,MAAO,QAEtB,GACdyb,EAAY4B,MAAMlU,KAAK,IAAIqM,OAAO,IAAMyE,EAAW1Y,MAAM,GAAK,MAE9Dka,EAAY2B,MAAMjU,KAAK,IAAIqM,OAAO,IAAMyE,EAAa,MAGxD,EA9KAwB,EAAYK,QAsMZ,SAAiB7Q,GAChB,GAA8B,MAA1BA,EAAKA,EAAKpG,OAAS,GACtB,OAAO,EAGR,IAAIuD,EACAkB,EAEJ,IAAKlB,EAAI,EAAGkB,EAAMmS,EAAY4B,MAAMxY,OAAQuD,EAAIkB,EAAKlB,IACpD,GAAIqT,EAAY4B,MAAMjV,GAAGqN,KAAKxK,GAC7B,OAAO,EAIT,IAAK7C,EAAI,EAAGkB,EAAMmS,EAAY2B,MAAMvY,OAAQuD,EAAIkB,EAAKlB,IACpD,GAAIqT,EAAY2B,MAAMhV,GAAGqN,KAAKxK,GAC7B,OAAO,EAIT,OAAO,CACR,EA1NAwQ,EAAYhC,SAAW,EAAQ,KAC/BgC,EAAYN,QA0PZ,WACCxS,QAAQC,KAAK,wIACd,EA1PA9D,OAAO0E,KAAK2F,GAAK9F,SAAQtH,IACxB0Z,EAAY1Z,GAAOoN,EAAIpN,EAAI,IAO5B0Z,EAAY2B,MAAQ,GACpB3B,EAAY4B,MAAQ,GAOpB5B,EAAYF,WAAa,CAAC,EAkB1BE,EAAYgB,YAVZ,SAAqBjD,GACpB,IAAI5G,EAAO,EAEX,IAAK,IAAIxK,EAAI,EAAGA,EAAIoR,EAAU3U,OAAQuD,IACrCwK,GAASA,GAAQ,GAAKA,EAAQ4G,EAAU+D,WAAWnV,GACnDwK,GAAQ,EAGT,OAAO6I,EAAYJ,OAAOhL,KAAKmN,IAAI5K,GAAQ6I,EAAYJ,OAAOxW,OAC/D,EA2NA4W,EAAY6B,OAAO7B,EAAYvB,QAExBuB,CACR,uBCxPmB,oBAATlK,KAAuBA,KACT,oBAAXnV,OAAyBA,YACV,IAAX,EAAAqhB,GAAyB,EAAAA,EAnBhC5P,EAAO3G,QAoBI,WACjB,aAYA,IAWQwW,EAXJC,EAAU,QAIVC,EAAUD,EACVE,EAA+B,mBAAXC,OACpBC,EAA6B,mBAAhBC,YAA6B,IAAIA,iBAAgBlb,EAC9Dmb,EAA6B,mBAAhBhL,YAA6B,IAAIA,iBAAgBnQ,EAE9Dob,EAASnW,MAAMoW,UAAU5c,MAAMgb,KADvB,qEAER6B,GACIV,EAAM,CAAC,EAGZQ,EAFG7U,SAAQ,SAAUsQ,EAAGvR,GAAK,OAAOsV,EAAI/D,GAAKvR,CAAG,IACxCsV,GAEPW,EAAQ,0EACRC,EAAUjc,OAAOkc,aAAaC,KAAKnc,QACnCoc,EAAsC,mBAApB9L,WAAWlC,KAC3BkC,WAAWlC,KAAK+N,KAAK7L,YACrB,SAAU+L,GAAM,OAAO,IAAI/L,WAAW5K,MAAMoW,UAAU5c,MAAMgb,KAAKmC,EAAI,GAAK,EAC5EC,EAAa,SAAUC,GAAO,OAAOA,EACpC5e,QAAQ,KAAM,IAAIA,QAAQ,UAAU,SAAU6e,GAAM,MAAa,KAANA,EAAY,IAAM,GAAK,GAAI,EACvFC,EAAW,SAAUC,GAAK,OAAOA,EAAE/e,QAAQ,oBAAqB,GAAK,EAIrEgf,EAAe,SAAUC,GAIzB,IAFA,IAAIC,EAAKC,EAAIC,EAAIC,EAAIC,EAAM,GACvBC,EAAMN,EAAIpa,OAAS,EACduD,EAAI,EAAGA,EAAI6W,EAAIpa,QAAS,CAC7B,IAAKsa,EAAKF,EAAI1B,WAAWnV,MAAQ,MAC5BgX,EAAKH,EAAI1B,WAAWnV,MAAQ,MAC5BiX,EAAKJ,EAAI1B,WAAWnV,MAAQ,IAC7B,MAAM,IAAIoX,UAAU,2BAExBF,GAAOpB,GADPgB,EAAOC,GAAM,GAAOC,GAAM,EAAKC,IACV,GAAK,IACpBnB,EAAOgB,GAAO,GAAK,IACnBhB,EAAOgB,GAAO,EAAI,IAClBhB,EAAa,GAANgB,EACjB,CACA,OAAOK,EAAMD,EAAI/d,MAAM,EAAGge,EAAM,GAAK,MAAMtC,UAAUsC,GAAOD,CAChE,EAMIG,EAAwB,mBAAT9d,KAAsB,SAAUsd,GAAO,OAAOtd,KAAKsd,EAAM,EACtEpB,EAAa,SAAUoB,GAAO,OAAOnB,OAAOrN,KAAKwO,EAAK,UAAUjC,SAAS,SAAW,EAChFgC,EACNU,EAAkB7B,EAChB,SAAU8B,GAAO,OAAO7B,OAAOrN,KAAKkP,GAAK3C,SAAS,SAAW,EAC7D,SAAU2C,GAIR,IAFA,IACIC,EAAO,GACFxX,EAAI,EAAG1D,EAAIib,EAAI9a,OAAQuD,EAAI1D,EAAG0D,GAFzB,KAGVwX,EAAKzW,KAAKmV,EAAQ9B,MAAM,KAAMmD,EAAIE,SAASzX,EAAGA,EAHpC,QAKd,OAAOqX,EAAMG,EAAKpe,KAAK,IAC3B,EAMAsL,EAAiB,SAAU6S,EAAKG,GAEhC,YADgB,IAAZA,IAAsBA,GAAU,GAC7BA,EAAUnB,EAAWe,EAAgBC,IAAQD,EAAgBC,EACxE,EAIII,EAAU,SAAUpG,GACpB,GAAIA,EAAE9U,OAAS,EAEX,OADImb,EAAKrG,EAAE4D,WAAW,IACV,IAAO5D,EACbqG,EAAK,KAAS1B,EAAQ,IAAQ0B,IAAO,GACjC1B,EAAQ,IAAa,GAAL0B,GACf1B,EAAQ,IAAS0B,IAAO,GAAM,IAC3B1B,EAAQ,IAAS0B,IAAO,EAAK,IAC7B1B,EAAQ,IAAa,GAAL0B,GAG9B,IAAIA,EAAK,MAC0B,MAA5BrG,EAAE4D,WAAW,GAAK,QAClB5D,EAAE4D,WAAW,GAAK,OACzB,OAAQe,EAAQ,IAAS0B,IAAO,GAAM,GAChC1B,EAAQ,IAAS0B,IAAO,GAAM,IAC9B1B,EAAQ,IAAS0B,IAAO,EAAK,IAC7B1B,EAAQ,IAAa,GAAL0B,EAE9B,EACIC,EAAU,gDAMVC,EAAO,SAAUC,GAAK,OAAOA,EAAEngB,QAAQigB,EAASF,EAAU,EAE1DK,EAAUvC,EACR,SAAUkB,GAAK,OAAOjB,OAAOrN,KAAKsO,EAAG,QAAQ/B,SAAS,SAAW,EACjEiB,EACI,SAAUc,GAAK,OAAOW,EAAgBzB,EAAI/K,OAAO6L,GAAK,EACtD,SAAUA,GAAK,OAAOU,EAAMS,EAAKnB,GAAK,EAM5C7L,EAAS,SAAU0L,EAAKkB,GAExB,YADgB,IAAZA,IAAsBA,GAAU,GAC7BA,EACDnB,EAAWyB,EAAQxB,IACnBwB,EAAQxB,EAClB,EAKIyB,EAAY,SAAUzB,GAAO,OAAO1L,EAAO0L,GAAK,EAAO,EAIvD0B,EAAU,8EACVC,EAAU,SAAUC,GACpB,OAAQA,EAAK3b,QACT,KAAK,EACD,IAGmC4b,IAHxB,EAAOD,EAAKjD,WAAW,KAAO,IACjC,GAAOiD,EAAKjD,WAAW,KAAO,IAC9B,GAAOiD,EAAKjD,WAAW,KAAO,EAC/B,GAAOiD,EAAKjD,WAAW,IAAmB,MACjD,OAAQe,EAA0B,OAAjBmC,IAAW,KACtBnC,EAA2B,OAAT,KAATmC,IACnB,KAAK,EACD,OAAOnC,GAAU,GAAOkC,EAAKjD,WAAW,KAAO,IACvC,GAAOiD,EAAKjD,WAAW,KAAO,EAC/B,GAAOiD,EAAKjD,WAAW,IAClC,QACI,OAAOe,GAAU,GAAOkC,EAAKjD,WAAW,KAAO,EACxC,GAAOiD,EAAKjD,WAAW,IAE1C,EAMImD,EAAO,SAAUC,GAAK,OAAOA,EAAE3gB,QAAQsgB,EAASC,EAAU,EAI1DK,EAAe,SAAUtB,GAGzB,GADAA,EAAMA,EAAItf,QAAQ,OAAQ,KACrBqe,EAAM5I,KAAK6J,GACZ,MAAM,IAAIE,UAAU,qBACxBF,GAAO,KAAK/d,MAAM,GAAkB,EAAb+d,EAAIza,SAE3B,IADA,IAAIgc,EAAeC,EAAIC,EAAd9B,EAAM,GACN7W,EAAI,EAAGA,EAAIkX,EAAIza,QACpBgc,EAAMzC,EAAOkB,EAAIlP,OAAOhI,OAAS,GAC3BgW,EAAOkB,EAAIlP,OAAOhI,OAAS,IAC1B0Y,EAAK1C,EAAOkB,EAAIlP,OAAOhI,QAAU,GACjC2Y,EAAK3C,EAAOkB,EAAIlP,OAAOhI,OAC9B6W,GAAc,KAAP6B,EAAYxC,EAAQuC,GAAO,GAAK,KAC1B,KAAPE,EAAYzC,EAAQuC,GAAO,GAAK,IAAKA,GAAO,EAAI,KAC5CvC,EAAQuC,GAAO,GAAK,IAAKA,GAAO,EAAI,IAAW,IAANA,GAEvD,OAAO5B,CACX,EAMI+B,EAAwB,mBAATvU,KAAsB,SAAU6S,GAAO,OAAO7S,KAAKqS,EAASQ,GAAO,EAChFzB,EAAa,SAAUyB,GAAO,OAAOxB,OAAOrN,KAAK6O,EAAK,UAAUtC,SAAS,SAAW,EAChF4D,EAENK,EAAgBpD,EACd,SAAUqD,GAAK,OAAOzC,EAASX,OAAOrN,KAAKyQ,EAAG,UAAY,EAC1D,SAAUA,GAAK,OAAOzC,EAASuC,EAAME,GAAG1iB,MAAM,IAAI8F,KAAI,SAAUqV,GAAK,OAAOA,EAAE4D,WAAW,EAAI,IAAK,EAIpG4D,EAAe,SAAUD,GAAK,OAAOD,EAAcG,EAAOF,GAAK,EAE/DG,EAAUxD,EACR,SAAUqD,GAAK,OAAOpD,OAAOrN,KAAKyQ,EAAG,UAAUlE,SAAS,OAAS,EACjEe,EACI,SAAUmD,GAAK,OAAOnD,EAAI/Q,OAAOiU,EAAcC,GAAK,EACpD,SAAUA,GAAK,OAAOR,EAAKM,EAAME,GAAK,EAC5CE,EAAS,SAAUF,GAAK,OAAOpC,EAASoC,EAAElhB,QAAQ,SAAS,SAAU6e,GAAM,MAAa,KAANA,EAAY,IAAM,GAAK,IAAK,EAM9G7R,EAAS,SAAU4R,GAAO,OAAOyC,EAAQD,EAAOxC,GAAO,EAYvD0C,EAAU,SAAUzW,GACpB,MAAO,CACHqD,MAAOrD,EAAGwJ,YAAY,EAAOkN,UAAU,EAAM5E,cAAc,EAEnE,EAII6E,EAAe,WACf,IAAIC,EAAO,SAAUxW,EAAMxH,GAAQ,OAAOqB,OAAO4X,eAAera,OAAO8b,UAAWlT,EAAMqW,EAAQ7d,GAAQ,EACxGge,EAAK,cAAc,WAAc,OAAOzU,EAAO9P,KAAO,IACtDukB,EAAK,YAAY,SAAU3B,GAAW,OAAO5M,EAAOhW,KAAM4iB,EAAU,IACpE2B,EAAK,eAAe,WAAc,OAAOvO,EAAOhW,MAAM,EAAO,IAC7DukB,EAAK,eAAe,WAAc,OAAOvO,EAAOhW,MAAM,EAAO,IAC7DukB,EAAK,gBAAgB,WAAc,OAAON,EAAajkB,KAAO,GAClE,EAIIwkB,EAAmB,WACnB,IAAID,EAAO,SAAUxW,EAAMxH,GAAQ,OAAOqB,OAAO4X,eAAe/J,WAAWwL,UAAWlT,EAAMqW,EAAQ7d,GAAQ,EAC5Gge,EAAK,YAAY,SAAU3B,GAAW,OAAOhT,EAAe5P,KAAM4iB,EAAU,IAC5E2B,EAAK,eAAe,WAAc,OAAO3U,EAAe5P,MAAM,EAAO,IACrEukB,EAAK,eAAe,WAAc,OAAO3U,EAAe5P,MAAM,EAAO,GACzE,EAQIykB,EAAU,CACVhE,QAASA,EACTC,QAASA,EACTnR,KAAMuU,EACNJ,aAAcA,EACdjf,KAAM8d,EACNT,aAAcA,EACd4C,WAAY5U,EACZ6U,SAAU3O,EACVA,OAAQA,EACRmN,UAAWA,EACXxT,UAAWwT,EACXH,KAAMA,EACNQ,KAAMA,EACN1T,OAAQA,EACR8U,QAtDU,SAAUlD,GACpB,GAAmB,iBAARA,EACP,OAAO,EACX,IAAIG,EAAIH,EAAI5e,QAAQ,OAAQ,IAAIA,QAAQ,UAAW,IACnD,OAAQ,oBAAoByV,KAAKsJ,KAAO,oBAAoBtJ,KAAKsJ,EACrE,EAkDIjS,eAAgBA,EAChBqU,aAAcA,EACdK,aAAcA,EACdE,iBAAkBA,EAClBK,eAxBiB,WACjBP,IACAE,GACJ,EA4BAC,OAAiB,CAAC,GAElB,OADA7c,OAAO0E,KAAKmY,GAAStY,SAAQ,SAAU2Y,GAAK,OAAOL,EAAQM,OAAOD,GAAKL,EAAQK,EAAI,IAC5EL,CACX,CAnT2BO,YCF3B,IAAInD,EAAI,IACJvE,EAAQ,GAAJuE,EACJoD,EAAQ,GAAJ3H,EACJ4H,EAAQ,GAAJD,EACJE,EAAQ,EAAJD,EAsJR,SAASE,EAAOrG,EAAIsG,EAAOC,EAAGvX,GAC5B,IAAIwX,EAAWF,GAAa,IAAJC,EACxB,OAAOnS,KAAKqS,MAAMzG,EAAKuG,GAAK,IAAMvX,GAAQwX,EAAW,IAAM,GAC7D,CAxIA5U,EAAO3G,QAAU,SAASoV,EAAKte,GAC7BA,EAAUA,GAAW,CAAC,EACtB,IA8Geie,EACXsG,EA/GAlX,SAAciR,EAClB,GAAa,WAATjR,GAAqBiR,EAAIzX,OAAS,EACpC,OAkBJ,SAAe6H,GAEb,MADAA,EAAMrK,OAAOqK,IACL7H,OAAS,KAAjB,CAGA,IAAI9H,EAAQ,mIAAmI4lB,KAC7IjW,GAEF,GAAK3P,EAAL,CAGA,IAAIylB,EAAII,WAAW7lB,EAAM,IAEzB,QADYA,EAAM,IAAM,MAAM2R,eAE5B,IAAK,QACL,IAAK,OACL,IAAK,MACL,IAAK,KACL,IAAK,IACH,OAzDE0T,SAyDKI,EACT,IAAK,QACL,IAAK,OACL,IAAK,IACH,OAAOA,EAAIH,EACb,IAAK,OACL,IAAK,MACL,IAAK,IACH,OAAOG,EAAIJ,EACb,IAAK,QACL,IAAK,OACL,IAAK,MACL,IAAK,KACL,IAAK,IACH,OAAOI,EAAIL,EACb,IAAK,UACL,IAAK,SACL,IAAK,OACL,IAAK,MACL,IAAK,IACH,OAAOK,EAAIhI,EACb,IAAK,UACL,IAAK,SACL,IAAK,OACL,IAAK,MACL,IAAK,IACH,OAAOgI,EAAIzD,EACb,IAAK,eACL,IAAK,cACL,IAAK,QACL,IAAK,OACL,IAAK,KACH,OAAOyD,EACT,QACE,OA3CJ,CANA,CAmDF,CAzEWrX,CAAMmR,GACR,GAAa,WAATjR,GAAqBwX,SAASvG,GACvC,OAAOte,EAAQ8kB,MA0GF7G,EA1GiBK,GA2G5BiG,EAAQlS,KAAKmN,IAAIvB,KACRmG,EACJE,EAAOrG,EAAIsG,EAAOH,EAAG,OAE1BG,GAASJ,EACJG,EAAOrG,EAAIsG,EAAOJ,EAAG,QAE1BI,GAAS/H,EACJ8H,EAAOrG,EAAIsG,EAAO/H,EAAG,UAE1B+H,GAASxD,EACJuD,EAAOrG,EAAIsG,EAAOxD,EAAG,UAEvB9C,EAAK,OAvCd,SAAkBA,GAChB,IAAIsG,EAAQlS,KAAKmN,IAAIvB,GACrB,OAAIsG,GAASH,EACJ/R,KAAKqS,MAAMzG,EAAKmG,GAAK,IAE1BG,GAASJ,EACJ9R,KAAKqS,MAAMzG,EAAKkG,GAAK,IAE1BI,GAAS/H,EACJnK,KAAKqS,MAAMzG,EAAKzB,GAAK,IAE1B+H,GAASxD,EACJ1O,KAAKqS,MAAMzG,EAAK8C,GAAK,IAEvB9C,EAAK,IACd,CAhGyC8G,CAASzG,GAEhD,MAAM,IAAIxe,MACR,wDACEuJ,KAAKC,UAAUgV,GAErB,4DCpCA,QAAelgB,OAAa,8BCI3B,IAAU4mB,EAAQd,EAARc,EAYO,oBAATzR,KAAuBA,UAAO,EAZpB2Q,EAY0B,SAAUc,GAGnD,GAAwB,mBAAZplB,QACR,KAAM,2BAEV,IAAIqlB,EAAUD,EAAO/Q,QAAU+Q,EAAOE,SACtC,GAAMD,EAAN,CAEA,IAAIE,EAAUF,EAAQ9Q,QAAU8Q,EAAQG,aACxC,GAAMD,EAAN,CAEA,IAAIE,EAAcL,EAAOM,QAAUL,EAAQxmB,aAAeqI,OACtDye,EAAgBP,EAAOQ,cAAgBL,EAAQ1mB,aAAeqI,OAG9D2e,GAFcT,EAAOU,WAAaV,EAAOW,IAEhCX,EAAO1I,UAAUC,UAAU5b,QAAQ,UAAY,GACxDilB,IAAYZ,EAAOE,WAAaO,EAChCI,GAAYZ,EAAQ9Q,UAAY8Q,EAAQG,aAC5C,GAAMQ,GAASC,EAAf,CAgKA,IAAIC,EAAU,CAAE,aAAgB,wBAC5BC,EAAU,CAAE,uBAAwB,gBAqXxC,GAhPA,CAAE,cAAe,YAAa,aACzB1a,SAAS,SAAWmR,GACjB,IAAIwJ,EAAMb,EAAQ3I,GAElB2I,EAAQ3I,GAAK,SAAW0G,EAAGP,EAAGhH,GAC1B,IACIsK,EAAIC,EAAIC,EA9LhB3Q,EAiQQxB,EApEA7E,EAAO,GAAG5L,MAAMgb,KAAK6H,WAGzB,OAAS5J,GACL,IAAK,cACDyJ,EAAKxQ,EAAIyN,GAAIgD,EAAKvD,EAAGwD,EAAKxK,EAC1B,MACJ,IAAK,YACDsK,EAAKxQ,EAAIkG,GAAIuK,EAAK/W,EAAK,GAAIgX,EAAKhX,EAAK,GAC1B,QAAN+T,KACDP,EAAI0D,EAAM1D,IACFlN,MAAMkN,EAAElN,IAAM6Q,EAAOL,IACvBtD,EAAEjN,UAAUiN,EAAEjN,QAAsB,QAAViN,EAAE4D,IAAoB,MAAO5D,EAAMwD,EAAGlc,OAAOuc,GAAeL,EAAGlc,OAAOwc,GAAeN,EAAG5iB,SACxH4L,EAAK,IA1MrBqG,EAAM6Q,EA0M0B1D,GAzM/BiD,IAAOpQ,EAAiB,YAAIA,EAAII,WAAYJ,EAAII,KAC9C8Q,EAAKC,SAAU5e,mBAAoBsB,KAAKC,UAAUkM,MAAWoR,SA0MpD,MACJ,IAAK,YACDX,EAAK9W,EAAK,GAAI+W,EAAK/W,EAAK,GAAIgX,EAAKhX,EAAK,GACtCA,EAAK,GAAKwM,EAAEkL,KAIpB,GAAW,gBAANrK,GAAmC,SAAZyJ,EAAGhZ,MAAmBgZ,EAAGrR,KAEjD,OADAqR,EAAGpf,OAASof,EAAGpf,QAAU,CAAE,QAAS,IAAK,UAAW,IAAK,UAAW,KAAM,UAAW,MAAOof,EAAGrR,KAAK3H,MAC7FkY,EAAQxP,UAAW,MAAOsP,EAAQnQ,gBAAiB,IAAIH,WAAasR,EAAGpf,OAAO,GAAI,IAAOof,EAAIC,EAAIC,GAG5G,GAAKN,GAAkB,gBAANrJ,GAAmC,sBAAZyJ,EAAGhZ,QAAmCgZ,EAAGxR,eAAiBwR,EAAGxR,eAAiB,MAElH,OADAyO,EAAIzN,EAAIyN,IAAMjW,KAAO,0BAA2BiW,EAAEtO,KAC3CuQ,EAAQ2B,YAAa5D,GAAG,EAAM,CAAE,UAAW,YAC7C5d,MAAM,SAAW0e,GACd,OAAOpkB,QAAQoK,IAAI,CACfmb,EAAQ4B,UAAW,MAAO/C,EAAEgD,WAC5B7B,EAAQ4B,UAAW,MAAO/C,EAAEnO,aAEpC,IACCvQ,MAAM,SAAWkG,GAGd,OAFAA,EAAK,GAAGiK,IAAMjK,EAAK,GAAGiK,IAAM6Q,EAAOL,GACnCza,EAAK,GAAGkK,QAAUyQ,EAAGlc,OAAOwc,GAAcjb,EAAK,GAAGkK,QAAUyQ,EAAGlc,OAAOuc,GAC/D5mB,QAAQoK,IAAI,CACfmb,EAAQxP,UAAW,MAAOnK,EAAK,GAAIya,GAAI,EAAMza,EAAK,GAAGkK,SACrDyP,EAAQxP,UAAW,MAAOnK,EAAK,GAAIya,EAAIC,EAAI1a,EAAK,GAAGkK,UAE3D,IACCpQ,MAAM,SAAWkG,GACd,MAAO,CACHwb,UAAWxb,EAAK,GAChBqK,WAAYrK,EAAK,GAEzB,IAGR,IAAOqa,GAAcD,GAAmC,WAAzBK,EAAGrR,MAAQ,CAAC,GAAI3H,OAC9B,cAANuP,GAA2B,QAAN0G,GAA2B,SAAZ+C,EAAGhZ,MAA6B,QAAV0V,EAAE4D,IACnE,OAAOpB,EAAQxP,UAAW,MAAO+Q,EAAKO,EAAItE,EAAEqB,IAAMrI,EAAGxM,EAAK,GAAIA,EAAK,IAGvE,GAAK0W,GAAkB,cAANrJ,IAA6B,SAAN0G,GAAsB,UAANA,GACpD,OAAOiC,EAAQxP,UAAW,MAlP1C,SAAoBqO,GAChB,IAAIkD,EAAOC,EAAMnD,GAAIoD,GAAM,EACtBF,EAAKrgB,OAAS,IAAIugB,GAAM,EAAMF,EAAKlW,SACxC,IAAIwE,EAAM,CAAE,KAAO,GACnB,GACS,yBADA0R,EAAK,GAAG,GAYT,MAAM,IAAI1F,UAAU,wBAVpB,IAAI6F,EAAU,CAAE,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,MACjDC,EAAUH,EAAOD,EAAK,IACrBE,GAAME,EAAOtW,QAClB,IAAM,IAAI5G,EAAI,EAAGA,EAAIkd,EAAOzgB,OAAQuD,IAC1Bkd,EAAOld,GAAG,KAAKkd,EAAOld,GAAKkd,EAAOld,GAAGyX,SAAS,IACpDrM,EAAK6R,EAAQjd,IAAOmd,EAAKC,EAAKF,EAAOld,KAOjD,OALQoL,EAAS,IAAI,MAKdA,CACX,CA+NiDiS,CAAS9E,GAAIhH,EAAGxM,EAAK,GAAIA,EAAK,IAGnE,GAAKyW,GAAc,cAANpJ,EACT,OAAO2I,EAAQuC,QAASvY,EAAK,GAAIwM,EAAGgH,GAC/Brd,MAAM,SAAW0e,GACd,OAAOmB,EAAQxP,UAAWuN,EAAGc,EAAG7U,EAAK,GAAIA,EAAK,GAAIA,EAAK,GAC3D,IAIR,IACI6E,EAAKgS,EAAIxH,MAAO2G,EAAShW,EAC7B,CACA,MAAQyE,GACJ,OAAOhU,QAAQC,OAAO+T,EAC1B,CA8BA,OA5BKgS,IACD5R,EAAK,IAAIpU,SAAS,SAAW4M,EAAKmb,GAC9B3T,EAAG4T,QACH5T,EAAG6T,QAAa,SAAWjU,GAAM+T,EAAI/T,EAAiB,EACtDI,EAAG8T,WAAa,SAAW9U,GAAMxG,EAAIwG,EAAEI,OAAO7N,OAAQ,CAC1D,KAGJyO,EAAKA,EAAG1O,MAAM,SAAW0e,GAiBrB,MAhBiB,SAAZiC,EAAGhZ,OACEgZ,EAAGpf,SAASof,EAAGpf,OAAS,EAAImd,EAAE5N,UAAUvP,SAEpB,GAAzBof,EAAGhZ,KAAKpF,OAAO,SACVoe,EAAGxR,gBAAgBwR,EAAGxR,eAAiBuP,EAAEgD,WAAahD,GAAG5N,UAAU3B,eACnEwR,EAAGvR,iBAAiBuR,EAAGvR,gBAAkBsP,EAAEgD,WAAahD,GAAG5N,UAAU1B,iBAE1EsP,EAAEgD,WAAahD,EAAEnO,WACd,CACAmR,UAAW,IAAItB,EAAW1B,EAAEgD,UAAWf,EAAIC,EAAIC,EAAGlc,OAAOwc,IACzD5Q,WAAY,IAAI6P,EAAW1B,EAAEnO,WAAYoQ,EAAIC,EAAIC,EAAGlc,OAAOuc,KAI3D,IAAId,EAAW1B,EAAGiC,EAAIC,EAAIC,EAGtC,GAGJ,CACJ,IAEJ,CAAE,YAAa,WACV9a,SAAS,SAAWmR,GACjB,IAAIwJ,EAAMb,EAAQ3I,GAElB2I,EAAQ3I,GAAK,SAAW0G,EAAGP,EAAGhH,GAC1B,IA4BI3H,EA5BA7E,EAAO,GAAG5L,MAAMgb,KAAK6H,WAEzB,OAAS5J,GACL,IAAK,YACDrN,EAAK,GAAKwT,EAAEkE,KACZ,MACJ,IAAK,UACD1X,EAAK,GAAKwT,EAAEkE,KAAM1X,EAAK,GAAKwM,EAAEkL,KAatC,IATOhB,GAAcD,GAA4C,WAAlCjD,EAAEvM,UAAUxB,MAAQ,CAAC,GAAI3H,OACvC,cAANuP,GAA2B,QAAN0G,GAAoC,SAArBP,EAAEvM,UAAUnJ,OACvDkC,EAAK,GAAK,QAGT0W,GAAkB,cAANrJ,GAA6B,SAAN0G,GAAsB,UAANA,IACpD/T,EAAK,GAAK,OAGTyW,GAAc,YAANpJ,EACT,OAAO2I,EAAQ4B,UAAW7D,EAAGP,GACxBrd,MAAM,SAAW0e,GAEd,MADW,QAANd,IAAcc,EAAI0C,EAAKC,SAAU5e,mBAAoBsB,KAAKC,UAAW+c,EAAMrC,QACxEmB,EAAQ4C,QAAS5Y,EAAK,GAAIwM,EAAGqI,EACzC,IAIR,IACIhQ,EAAKgS,EAAIxH,MAAO2G,EAAShW,EAC7B,CACA,MAAQyE,GACJ,OAAOhU,QAAQC,OAAO+T,EAC1B,CA8BA,OA5BKgS,IACD5R,EAAK,IAAIpU,SAAS,SAAW4M,EAAKmb,GAC9B3T,EAAG4T,QACH5T,EAAG6T,QAAa,SAAWjU,GAAM+T,EAAI/T,EAAiB,EACtDI,EAAG8T,WAAa,SAAW9U,GAAMxG,EAAIwG,EAAEI,OAAO7N,OAAQ,CAC1D,KAGO,cAANiX,GAA2B,QAAN0G,IACtBlP,EAAKA,EAAG1O,MAAM,SAAW0e,GACrB,OAAO6B,GAAcD,GAA4C,WAAlCjD,EAAEvM,UAAUxB,MAAQ,CAAC,GAAI3H,OACxB,SAArB0V,EAAEvM,UAAUnJ,KACZ,CAAE,IAAO,MAAO,IAAOqZ,EAAO3D,EAAEvM,WAAY,QAAWuM,EAAEqF,OAAOzkB,QAAS,KAAO,EAAM,EAAKgkB,EAAKC,EAAIxD,OAE/GA,EAAIqC,EAAMrC,IACFvO,MAAMuO,EAAO,IAAIsC,EAAO3D,EAAEvM,YAC5B4N,EAAEtO,UAAUsO,EAAW,QAAiB,WAAXrB,EAAEtV,KAAsBsV,EAAEqF,OAAO/d,OAAOwc,GAA4B,YAAX9D,EAAEtV,KAAuBsV,EAAEqF,OAAO/d,OAAOuc,GAAe7D,EAAEqF,OAAOzkB,SACtJygB,EACX,MAGC6B,GAAkB,cAANrJ,GAA6B,SAAN0G,GAAsB,UAANA,IACpDlP,EAAKA,EAAG1O,MAAM,SAAW0e,GAErB,OADAA,EA/UpB,SAAoBA,GAChB,IAAIjgB,EAAKmjB,EAAO,CAAE,CAAE,GAAI,OAAUE,GAAM,EACxC,GACS,QADApD,EAAEuC,IAcH,MAAM,IAAI/E,UAAU,wBAVpB,IAFA,IAAI6F,EAAU,CAAE,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,MACjDC,EAAS,GACHld,EAAI,EAAGA,EAAIid,EAAQxgB,QACjBwgB,EAAQjd,KAAM4Z,EADW5Z,IAAM,CAEvC,IAAIuY,EAAI2E,EAAOld,GAAKsc,EAAKO,EAAKjD,EAAGqD,EAAQjd,MAC7B,IAAPuY,EAAE,KAAY2E,EAAOld,GAAK,IAAIuK,WAAWgO,EAAE9b,OAAS,GAAIygB,EAAOld,GAAGnJ,IAAK0hB,EAAG,GACnF,CAWR,OAVa2E,EAAOzgB,OAAS,IAAIugB,GAAM,EAAME,EAAOnJ,QAAS,IAAIxJ,WAAW,CAAC,MACrEuS,EAAK,GAAG,GAAK,uBACbnjB,EAAMujB,EAKdJ,EAAK/b,KAAM,IAAIwJ,WAAYsT,EAAMlkB,IAAO6iB,QAClCQ,EACDF,EAAK/I,QAAS,IAAIxJ,WAAW,CAAC,KADvBuS,EAAK,GAAK,CAAE,IAAO,EAAM,MAASA,EAAK,IAE5C,IAAIvS,WAAYsT,EAAMf,IAAQN,MACzC,CAyTwBsB,CAAU7B,EAAMrC,IACbA,CACX,KAGGhQ,CACX,CACJ,IAEJ,CAAE,UAAW,UAAW,OAAQ,UAC3B3I,SAAS,SAAWmR,GACjB,IAAIwJ,EAAMb,EAAQ3I,GAElB2I,EAAQ3I,GAAK,SAAW0G,EAAGP,EAAGhH,EAAGyI,GAC7B,GAAKwB,KAAWjK,EAAEwM,YAAgB/D,IAAMA,EAAE+D,YACtC,MAAM,IAAIroB,MAAM,6BAEpB,IAWIkU,EAXA7E,EAAO,GAAG5L,MAAMgb,KAAK6H,WACrBH,EAAKxQ,EAAIyN,GAEb,GAAK0C,GAAc,YAANpJ,GAA+B,YAAZyJ,EAAGhZ,KAAqB,CACpD,IAAImb,EAAKlF,EAAEmF,WAAa,EACxBlZ,EAAK,IAAMwM,EAAEiL,QAAUjL,GAAGpY,MAAO,EAAGoY,EAAEwM,WAAaC,GACnDlF,EAAEoF,KAAO3M,EAAEiL,QAAUjL,GAAGpY,MAAOoY,EAAEwM,WAAaC,EAClD,CAEAjZ,EAAK,GAAKwT,EAAEkE,KAGZ,IACI7S,EAAKgS,EAAIxH,MAAO2G,EAAShW,EAC7B,CACA,MAAQyE,GACJ,OAAOhU,QAAQC,OAAO+T,EAC1B,CAyBA,OAvBKgS,IACD5R,EAAK,IAAIpU,SAAS,SAAW4M,EAAKmb,GAC9B3T,EAAG4T,QACH5T,EAAG6T,QAAU,SAAWjU,GACpB+T,EAAI/T,EACR,EAEAI,EAAG8T,WAAa,SAAW9U,GAGvB,GAFIA,EAAIA,EAAEI,OAAO7N,OAEN,YAANiX,GAAmBxJ,aAAauV,oBAAsB,CACvD,IAAI5M,EAAI3I,EAAEwV,WAAYC,EAAIzV,EAAEsV,KAC5BtV,EAAI,IAAI2B,WAAYgH,EAAEwM,WAAaM,EAAEN,aACnClnB,IAAK,IAAI0T,WAAWgH,GAAI,GAC1B3I,EAAE/R,IAAK,IAAI0T,WAAW8T,GAAI9M,EAAEwM,YAC5BnV,EAAIA,EAAE4T,MACV,CAEApa,EAAIwG,EACR,CACJ,KAGGgB,CACX,CACJ,IAEC4R,EAAO,CACR,IAAI8C,EAAUvD,EAAQhQ,OAEtBgQ,EAAgB,OAAI,SAAWjC,EAAGP,GAC9B,IAAMA,EAAEwF,WACJ,MAAM,IAAIroB,MAAM,6BAEpB,IAAIkU,EACJ,IACIA,EAAK0U,EAAQnK,KAAM4G,EAASjC,EAAGP,EACnC,CACA,MAAQ/O,GACJ,OAAOhU,QAAQC,OAAO+T,EAC1B,CAQA,OANAI,EAAK,IAAIpU,SAAS,SAAW4M,EAAKmb,GAC9B3T,EAAG4T,QACH5T,EAAG6T,QAAa,SAAWjU,GAAM+T,EAAI/T,EAAiB,EACtDI,EAAG8T,WAAa,SAAW9U,GAAMxG,EAAIwG,EAAEI,OAAO7N,OAAQ,CAC1D,GAGJ,EAEAyf,EAAO/Q,OAASnN,OAAOsC,OAAQ6b,EAAS,CACpCnQ,gBAAiB,CAAE5E,MAAO,SAAWgT,GAAM,OAAO+B,EAAQnQ,gBAAgBoO,EAAG,GAC7E/O,OAAiB,CAAEjE,MAAOiV,KAG9BH,EAAOU,UAAYA,CACvB,CAEKG,IACDZ,EAAQ9Q,OAASgR,EAEjBH,EAAOM,OAASD,EAChBL,EAAOQ,aAAeD,EACtBP,EAAOU,UAAYA,EA3jBS,CATV,CAHA,CActB,SAAS6B,EAAMxG,GACX,OAAOpd,KAAKod,GAAG/e,QAAQ,OAAQ,IAAIA,QAAQ,MAAO,KAAKA,QAAQ,MAAO,IAC1E,CAEA,SAASilB,EAAMlG,GAEX,OADYA,GAAZA,GAAK,OAAaxd,MAAO,GAAIwd,EAAEla,OAAS,GACjC4H,KAAMsS,EAAE/e,QAAQ,KAAM,KAAKA,QAAQ,KAAM,KACpD,CAEA,SAAS0kB,EAAM3F,GAEX,IADA,IAAI4B,EAAI,IAAIhO,WAAWoM,EAAEla,QACfuD,EAAI,EAAGA,EAAI2W,EAAEla,OAAQuD,IAAMuY,EAAEvY,GAAK2W,EAAExB,WAAWnV,GACzD,OAAOuY,CACX,CAEA,SAAS6E,EAAM7E,GAEX,OADKA,aAAagG,cAAchG,EAAI,IAAIhO,WAAWgO,IAC5Cte,OAAOkc,aAAa/B,MAAOna,OAAQse,EAC9C,CAEA,SAASlN,EAAMyN,GACX,IAAIlQ,EAAI,CAAE,MAASkQ,EAAEjW,MAAQiW,GAAK,IAAI0F,cAAc5mB,QAAQ,IAAI,MAChE,OAASgR,EAAE/F,MACP,IAAK,QACL,IAAK,UACL,IAAK,UACL,IAAK,UACD,MACJ,IAAK,UACL,IAAK,UACL,IAAK,SACIiW,EAAErc,SAASmM,EAAU,OAAIkQ,EAAErc,QAChC,MACJ,IAAK,OACIqc,EAAEtO,OAAO5B,EAAQ,KAAIyC,EAAIyN,EAAEtO,OAC3BsO,EAAErc,SAASmM,EAAU,OAAIkQ,EAAErc,QAChC,MACJ,IAAK,mBACIqc,EAAExO,iBAAiB1B,EAAkB,eAAI,IAAI2B,WAAWuO,EAAExO,iBAC1DwO,EAAEzO,gBAAgBzB,EAAiB,cAAIkQ,EAAEzO,eAC9C,MACJ,IAAK,oBACL,IAAK,WACIyO,EAAEtO,OAAO5B,EAAQ,KAAIyC,EAAIyN,EAAEtO,OAC3BsO,EAAExO,iBAAiB1B,EAAkB,eAAI,IAAI2B,WAAWuO,EAAExO,iBAC1DwO,EAAEzO,gBAAgBzB,EAAiB,cAAIkQ,EAAEzO,eAC9C,MACJ,QACI,MAAM,IAAIoU,YAAY,sBAE9B,OAAO7V,CACX,CAEA,SAASsT,EAASpD,GACd,MAAO,CACH,KAAQ,CACJ,QAAS,MACT,UAAW,QACX,UAAW,QACX,UAAW,SAEf,oBAAqB,CACjB,QAAS,MACT,UAAW,QACX,UAAW,QACX,UAAW,SAEf,mBAAoB,CAChB,GAAI,UAER,WAAY,CACR,QAAS,WACT,UAAW,gBAEf,SAAU,CACN,IAAO,SACP,IAAO,SACP,IAAO,UAEX,UAAW,CACP,IAAO,UACP,IAAO,UACP,IAAO,WAEX,UAAW,CACP,IAAO,UACP,IAAO,UACP,IAAO,YAEbA,EAAEjW,OAASiW,EAAEtO,MAAQ,CAAC,GAAI3H,MAAQiW,EAAErc,QAAU,GACpD,CAEA,SAASwf,EAAQrC,IACRA,aAAa2E,aAAe3E,aAAarP,cAAaqP,EAAI3a,KAAK8D,MAAO2b,mBAAoBC,OAAQvB,EAAIxD,OAC3G,IAAIxO,EAAM,CAAE,IAAOwO,EAAEuC,IAAK,IAAOvC,EAAEvO,IAAK,IAAOuO,EAAEpO,KAAOoO,EAAEgF,aAC1D,OAASxT,EAAI+Q,KACT,IAAK,MACD/Q,EAAIwO,EAAIA,EAAEA,EACd,IAAK,MACD,CAAE,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,OAAQ3Y,SAAS,SAAW4H,GAAWA,KAAK+Q,IAAIxO,EAAIvC,GAAK+Q,EAAE/Q,GAAG,IAC3G,MACJ,QACI,MAAM,IAAIuO,UAAU,wBAE5B,OAAOhM,CACX,CAwDA,SAAS2R,EAAQ8B,EAAKC,GAIlB,GAHKD,aAAeN,cAAcM,EAAM,IAAItU,WAAWsU,IACjDC,IAAMA,EAAM,CAAEC,IAAK,EAAGC,IAAKH,EAAIpiB,SAEhCqiB,EAAIE,IAAMF,EAAIC,IAAM,GAAKD,EAAIE,IAAMH,EAAIpiB,OAAS,MAAM,IAAIwiB,WAAW,iBAE1E,IAYIC,EAZAhB,EAAMW,EAAIC,EAAIC,OACd7d,EAAM2d,EAAIC,EAAIC,OAElB,GAAK7d,GAAO,IAAO,CAEf,GADAA,GAAO,IACF4d,EAAIE,IAAMF,EAAIC,IAAM7d,EAAM,MAAM,IAAI+d,WAAW,iBACpD,IAAM,IAAIE,EAAO,EAAGje,KAASie,IAAS,EAAGA,GAAQN,EAAIC,EAAIC,OACzD7d,EAAMie,CACV,CAEA,GAAKL,EAAIE,IAAMF,EAAIC,IAAM7d,EAAM,MAAM,IAAI+d,WAAW,iBAIpD,OAASf,GACL,KAAK,EACDgB,EAAKL,EAAIpH,SAAUqH,EAAIC,IAAKD,EAAIC,KAAO7d,GACvC,MACJ,KAAK,EACD,GAAK2d,EAAIC,EAAIC,OAAS,MAAM,IAAIrpB,MAAO,0BACvCwL,IACJ,KAAK,EACDge,EAAK,IAAI3U,WAAYsU,EAAIpH,SAAUqH,EAAIC,IAAKD,EAAIC,KAAO7d,IAAQsb,OAC/D,MACJ,KAAK,EACD0C,EAAK,KACL,MACJ,KAAK,EACD,IAAIE,EAAM7lB,KAAM6jB,EAAKyB,EAAIpH,SAAUqH,EAAIC,IAAKD,EAAIC,KAAO7d,KACvD,KAAQke,KAAO1D,GAAY,MAAM,IAAIhmB,MAAO,yBAA2B0pB,GACvEF,EAAKxD,EAAQ0D,GACb,MACJ,KAAK,GACDF,EAAK,GACL,IAAM,IAAIF,EAAMF,EAAIC,IAAM7d,EAAK4d,EAAIC,IAAMC,GAAOE,EAAGne,KAAMgc,EAAO8B,EAAKC,IACrE,MACJ,QACI,MAAM,IAAIppB,MAAO,yBAA2BwoB,EAAItJ,SAAS,KAGjE,OAAOsK,CACX,CAEA,SAASrB,EAAQ3J,EAAK2K,GACZA,IAAMA,EAAM,IAElB,IAAIX,EAAM,EAAGhd,EAAM,EACf6d,EAAMF,EAAIpiB,OAAS,EAIvB,GAFAoiB,EAAI9d,KAAM,EAAG,GAERmT,aAAe3J,WAAa,CAC7B2T,EAAM,EAAMhd,EAAMgT,EAAIzX,OACtB,IAAM,IAAIuD,EAAI,EAAGA,EAAIkB,EAAKlB,IAAM6e,EAAI9d,KAAMmT,EAAIlU,GAClD,MACK,GAAKkU,aAAeqK,YAErB,IADAL,EAAM,EAAMhd,EAAMgT,EAAI6J,WAAY7J,EAAM,IAAI3J,WAAW2J,GAC7ClU,EAAI,EAAGA,EAAIkB,EAAKlB,IAAM6e,EAAI9d,KAAMmT,EAAIlU,SAE7C,GAAa,OAARkU,EACNgK,EAAM,EAAMhd,EAAM,OAEjB,GAAoB,iBAARgT,GAAoBA,KAAOyH,EAAU,CAClD,IAAIyD,EAAM9C,EAAKjY,KAAMsX,EAAQzH,KAE7B,IADAgK,EAAM,EAAMhd,EAAMke,EAAI3iB,OACZuD,EAAI,EAAGA,EAAIkB,EAAKlB,IAAM6e,EAAI9d,KAAMqe,EAAIpf,GAClD,MACK,GAAKkU,aAAevU,MAAQ,CAC7B,IAAUK,EAAI,EAAGA,EAAIkU,EAAIzX,OAAQuD,IAAM6d,EAAO3J,EAAIlU,GAAI6e,GACtDX,EAAM,GAAMhd,EAAM2d,EAAIpiB,OAASsiB,CACnC,KACK,MAAoB,iBAAR7K,GAAgC,IAAZA,EAAIgK,KAAgBhK,EAAIpO,iBAAiByY,aAM1E,MAAM,IAAI7oB,MAAO,yBAA2Bwe,GAJ/B,IADoBgK,EAAM,EAAMhd,GAA7CgT,EAAM,IAAI3J,WAAW2J,EAAIpO,QAA8BiY,WACvDc,EAAI9d,KAAK,GAAcf,EAAI,EAAGA,EAAIkB,EAAKlB,IAAM6e,EAAI9d,KAAMmT,EAAIlU,IAC3DkB,GAIJ,CAEA,GAAKA,GAAO,IAAO,CACf,IAAIie,EAAOje,EAEX,IAFgBA,EAAM,EACtB2d,EAAIpN,OAAQsN,EAAK,EAAII,GAAQ,GAAM,IAAOA,GAAQ,GAAM,IAAOA,GAAQ,EAAK,IAAa,IAAPA,GAC1Eje,EAAM,KAAOie,GAAQ,KAAMA,IAAS,EAAGje,IAC1CA,EAAM,GAAI2d,EAAIpN,OAAQsN,EAAK,EAAI7d,GACpCA,GAAO,GACX,CAIA,OAFA2d,EAAIpN,OAAQsN,EAAM,EAAG,EAAGb,EAAKhd,GAEtB2d,CACX,CAEA,SAASvD,EAAY3hB,EAAK0R,EAAKG,EAAK6T,GAChC3iB,OAAO4iB,iBAAkBxqB,KAAM,CAC3B2nB,KAAM,CACF3W,MAAOnM,GAEXsJ,KAAM,CACF6C,MAAOnM,EAAIsJ,KACXgJ,YAAY,GAEhB2S,YAAa,CACT9Y,WAAgBpL,IAAR8Q,EAAqB7R,EAAIilB,YAAcpT,EAC/CS,YAAY,GAEhBD,UAAW,CACPlG,WAAgBpL,IAAR2Q,EAAqB1R,EAAIqS,UAAYX,EAC7CY,YAAY,GAEhB2R,OAAQ,CACJ9X,WAAgBpL,IAAR2kB,EAAqB1lB,EAAIikB,OAASyB,EAC1CpT,YAAY,IAGxB,CAEA,SAASoQ,EAActE,GACnB,MAAa,WAANA,GAAwB,YAANA,GAAyB,YAANA,CAChD,CAEA,SAASqE,EAAcrE,GACnB,MAAa,SAANA,GAAsB,YAANA,GAAyB,cAANA,CAC9C,CAyRJ,EA3lB0B,mBAAXwH,QAAyBA,OAAOC,IAEvCD,OAAO,IAAI,WACP,OAAOzF,EAAQc,EACnB,IACyB,iBAAXnV,QAAuBA,OAAO3G,QAE5C2G,OAAO3G,QAAUgb,EAAQc,GAEzBd,EAAQc,KCdZ6E,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBjlB,IAAjBklB,EACH,OAAOA,EAAa9gB,QAGrB,IAAI2G,EAASga,EAAyBE,GAAY,CAGjD7gB,QAAS,CAAC,GAOX,OAHA+gB,EAAoBF,GAAUxL,KAAK1O,EAAO3G,QAAS2G,EAAQA,EAAO3G,QAAS4gB,GAGpEja,EAAO3G,OACf,CCrBA4gB,EAAoB1F,EAAI,CAAClb,EAASghB,KACjC,IAAI,IAAInmB,KAAOmmB,EACXJ,EAAoB7Y,EAAEiZ,EAAYnmB,KAAS+lB,EAAoB7Y,EAAE/H,EAASnF,IAC5E+C,OAAO4X,eAAexV,EAASnF,EAAK,CAAEsS,YAAY,EAAMrS,IAAKkmB,EAAWnmB,IAE1E,ECND+lB,EAAoBrK,EAAI,WACvB,GAA0B,iBAAfvL,WAAyB,OAAOA,WAC3C,IACC,OAAOhV,MAAQ,IAAIirB,SAAS,cAAb,EAChB,CAAE,MAAOvW,GACR,GAAsB,iBAAXxV,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxB0rB,EAAoB7Y,EAAI,CAACjI,EAAKohB,IAAUtjB,OAAOqZ,UAAUkK,eAAe9L,KAAKvV,EAAKohB,GCGlF,IAAIE,EAAsBR,EAAoB", "sources": ["webpack://FHIR/./src/Client.ts", "webpack://FHIR/./src/FhirClient.ts", "webpack://FHIR/./src/HttpError.ts", "webpack://FHIR/./src/adapters/BrowserAdapter.ts", "webpack://FHIR/./src/entry/browser.ts", "webpack://FHIR/./src/lib.ts", "webpack://FHIR/./src/security/browser.ts", "webpack://FHIR/./src/settings.ts", "webpack://FHIR/./src/smart.ts", "webpack://FHIR/./src/storage/BrowserStorage.ts", "webpack://FHIR/./src/strings.ts", "webpack://FHIR/./node_modules/debug/src/browser.js", "webpack://FHIR/./node_modules/debug/src/common.js", "webpack://FHIR/./node_modules/js-base64/base64.js", "webpack://FHIR/./node_modules/ms/index.js", "webpack://FHIR/./node_modules/isomorphic-webcrypto/src/browser.mjs", "webpack://FHIR/./node_modules/isomorphic-webcrypto/src/webcrypto-shim.mjs", "webpack://FHIR/webpack/bootstrap", "webpack://FHIR/webpack/runtime/define property getters", "webpack://FHIR/webpack/runtime/global", "webpack://FHIR/webpack/runtime/hasOwnProperty shorthand", "webpack://FHIR/webpack/startup"], "sourcesContent": ["import {\n    absolute,\n    debug as _debug,\n    getPath,\n    jwtDecode,\n    makeArray,\n    request,\n    byCode,\n    byCodes,\n    units,\n    getPatientParam,\n    fetchConformanceStatement,\n    getAccessTokenExpiration,\n    assert\n} from \"./lib\";\n\nimport str from \"./strings\";\nimport { SMART_KEY, patientCompartment } from \"./settings\";\nimport HttpError from \"./HttpError\";\nimport BrowserAdapter from \"./adapters/BrowserAdapter\";\nimport { fhirclient } from \"./types\";\nimport FhirClient from \"./FhirClient\";\n\n// $lab:coverage:off$\n// @ts-ignore\nconst { Response } = typeof FHIRCLIENT_PURE !== \"undefined\" ? window : require(\"cross-fetch\");\n// $lab:coverage:on$\n\nconst debug = _debug.extend(\"client\");\n\n/**\n * Adds patient context to requestOptions object to be used with [[Client.request]]\n * @param requestOptions Can be a string URL (relative to the serviceUrl), or an\n * object which will be passed to fetch()\n * @param client Current FHIR client object containing patient context\n * @return requestOptions object contextualized to current patient\n */\nasync function contextualize(\n    requestOptions: string | URL | fhirclient.RequestOptions,\n    client: Client\n): Promise<fhirclient.RequestOptions>\n{\n    const base = absolute(\"/\", client.state.serverUrl);\n\n    async function contextualURL(_url: URL) {\n        const resourceType = _url.pathname.split(\"/\").pop();\n        assert(resourceType, `Invalid url \"${_url}\"`);\n        assert(patientCompartment.indexOf(resourceType) > -1, `Cannot filter \"${resourceType}\" resources by patient`);\n        const conformance = await fetchConformanceStatement(client.state.serverUrl);\n        const searchParam = getPatientParam(conformance, resourceType);\n        _url.searchParams.set(searchParam, client.patient.id as string);\n        return _url.href;\n    }\n\n    if (typeof requestOptions == \"string\" || requestOptions instanceof URL) {\n        return { url: await contextualURL(new URL(requestOptions + \"\", base)) };\n    }\n\n    requestOptions.url = await contextualURL(new URL(requestOptions.url + \"\", base));\n    return requestOptions;\n}\n\n/**\n * This is a FHIR client that is returned to you from the `ready()` call of the\n * **SMART API**. You can also create it yourself if needed:\n *\n * ```js\n * // BROWSER\n * const client = FHIR.client(\"https://r4.smarthealthit.org\");\n *\n * // SERVER\n * const client = smart(req, res).client(\"https://r4.smarthealthit.org\");\n * ```\n */\nexport default class Client extends FhirClient\n{\n    /**\n     * The state of the client instance is an object with various properties.\n     * It contains some details about how the client has been authorized and\n     * determines the behavior of the client instance. This state is persisted\n     * in `SessionStorage` in browsers or in request session on the servers.\n     */\n    readonly state: fhirclient.ClientState;\n\n    /**\n     * The adapter to use to connect to the current environment. Currently we have:\n     * - BrowserAdapter - for browsers\n     * - NodeAdapter - for Express or vanilla NodeJS servers\n     * - HapiAdapter - for HAPI NodeJS servers\n     */\n    readonly environment: fhirclient.Adapter;\n\n    /**\n     * A SMART app is typically associated with a patient. This is a namespace\n     * for the patient-related functionality of the client.\n     */\n    readonly patient: {\n\n        /**\n         * The ID of the current patient or `null` if there is no current patient\n         */\n        id: string | null\n\n        /**\n         * A method to fetch the current patient resource from the FHIR server.\n         * If there is no patient context, it will reject with an error.\n         * @param {fhirclient.FetchOptions} [requestOptions] Any options to pass to the `fetch` call.\n         * @category Request\n         */\n        read: fhirclient.RequestFunction<fhirclient.FHIR.Patient>\n        \n        /**\n         * This is similar to [[request]] but it makes requests in the\n         * context of the current patient. For example, instead of doing\n         * ```js\n         * client.request(\"Observation?patient=\" + client.patient.id)\n         * ```\n         * you can do\n         * ```js\n         * client.patient.request(\"Observation\")\n         * ```\n         * The return type depends on the arguments. Typically it will be the\n         * response payload JSON object. Can also be a string or the `Response`\n         * object itself if we have received a non-json result, which allows us\n         * to handle even binary responses. Can also be a [[CombinedFetchResult]]\n         * object if the `requestOptions.includeResponse`s has been set to true.\n         * @category Request\n         */\n        request: <R = fhirclient.FetchResult>(\n            requestOptions: string|URL|fhirclient.RequestOptions,\n            fhirOptions?: fhirclient.FhirOptions\n        ) => Promise<R>\n\n        /**\n         * This is the FhirJS Patient API. It will ONLY exist if the `Client`\n         * instance is \"connected\" to FhirJS.\n         */\n        api?: Record<string, any>\n    };\n\n    /**\n     * The client may be associated with a specific encounter, if the scopes\n     * permit that and if the back-end server supports that. This is a namespace\n     * for encounter-related functionality.\n     */\n    readonly encounter: {\n\n        /**\n         * The ID of the current encounter or `null` if there is no current\n         * encounter\n         */\n        id: string | null\n\n        /**\n         * A method to fetch the current encounter resource from the FHIR server.\n         * If there is no encounter context, it will reject with an error.\n         * @param [requestOptions] Any options to pass to the `fetch` call.\n         * @category Request\n         */\n        read: fhirclient.RequestFunction<fhirclient.FHIR.Encounter>\n    };\n\n    /**\n     * The client may be associated with a specific user, if the scopes\n     * permit that. This is a namespace for user-related functionality.\n     */\n    readonly user: {\n\n        /**\n         * The ID of the current user or `null` if there is no current user\n         */\n        id: string | null\n\n        /**\n         * A method to fetch the current user resource from the FHIR server.\n         * If there is no user context, it will reject with an error.\n         * @param [requestOptions] Any options to pass to the `fetch` call.\n         * @category Request\n         */\n        read: fhirclient.RequestFunction<\n            fhirclient.FHIR.Patient |\n            fhirclient.FHIR.Practitioner |\n            fhirclient.FHIR.RelatedPerson\n        >\n\n        /**\n         * Returns the profile of the logged_in user (if any), or null if the\n         * user is not available. This is a string having the shape\n         * `{user type}/{user id}`. For example `Practitioner/abc` or\n         * `Patient/xyz`.\n         * @alias client.getFhirUser()\n         */\n        fhirUser: string | null\n\n        /**\n         * Returns the type of the logged-in user or null. The result can be\n         * `Practitioner`, `Patient` or `RelatedPerson`.\n         * @alias client.getUserType()\n         */\n        resourceType: string | null\n    };\n\n    /**\n     * The [FhirJS](https://github.com/FHIR/fhir.js/blob/master/README.md) API.\n     * **NOTE:** This will only be available if `fhir.js` is used. Otherwise it\n     * will be `undefined`.\n     */\n    api: Record<string, any> | undefined;\n\n    /**\n     * Refers to the refresh task while it is being performed.\n     * @see [[refresh]]\n     */\n    private _refreshTask: Promise<any> | null;\n\n    /**\n     * Validates the parameters, creates an instance and tries to connect it to\n     * FhirJS, if one is available globally.\n     */\n    constructor(environment: fhirclient.Adapter, state: fhirclient.ClientState | string)\n    {\n        const _state = typeof state == \"string\" ? { serverUrl: state } : state;\n        \n        // Valid serverUrl is required!\n        assert(\n            _state.serverUrl && _state.serverUrl.match(/https?:\\/\\/.+/),\n            \"A \\\"serverUrl\\\" option is required and must begin with \\\"http(s)\\\"\"\n        );\n        \n        super(_state.serverUrl)\n\n        this.state = _state;\n        this.environment = environment;\n        this._refreshTask = null;\n\n        const client = this;\n\n        // patient api ---------------------------------------------------------\n        this.patient = {\n            get id() { return client.getPatientId(); },\n            read: (requestOptions) => {\n                const id = this.patient.id;\n                return id ?\n                    this.request({ ...requestOptions, url: `Patient/${id}` }) :\n                    Promise.reject(new Error(\"Patient is not available\"));\n            },\n            request: (requestOptions, fhirOptions = {}) => {\n                if (this.patient.id) {\n                    return (async () => {\n                        const options = await contextualize(requestOptions, this);\n                        return this.request(options, fhirOptions);\n                    })();\n                } else {\n                    return Promise.reject(new Error(\"Patient is not available\"));\n                }\n            }\n        };\n\n        // encounter api -------------------------------------------------------\n        this.encounter = {\n            get id() { return client.getEncounterId(); },\n            read: requestOptions => {\n                const id = this.encounter.id;\n                return id ?\n                    this.request({ ...requestOptions, url: `Encounter/${id}` }) :\n                    Promise.reject(new Error(\"Encounter is not available\"));\n            }\n        };\n\n        // user api ------------------------------------------------------------\n        this.user = {\n            get fhirUser() { return client.getFhirUser(); },\n            get id() { return client.getUserId(); },\n            get resourceType() { return client.getUserType(); },\n            read: requestOptions => {\n                const fhirUser = this.user.fhirUser;\n                return fhirUser ?\n                    this.request({ ...requestOptions, url: fhirUser }) :\n                    Promise.reject(new Error(\"User is not available\"));\n            }\n        };\n\n        // fhir.js api (attached automatically in browser)\n        // ---------------------------------------------------------------------\n        this.connect((environment as BrowserAdapter).fhir);\n    }\n\n    /**\n     * This method is used to make the \"link\" between the `fhirclient` and the\n     * `fhir.js`, if one is available.\n     * **Note:** This is called by the constructor. If fhir.js is available in\n     * the global scope as `fhir`, it will automatically be linked to any [[Client]]\n     * instance. You should only use this method to connect to `fhir.js` which\n     * is not global.\n     */\n    connect(fhirJs?: (options: Record<string, any>) => Record<string, any>): Client\n    {\n        if (typeof fhirJs == \"function\") {\n            const options: Record<string, any> = {\n                baseUrl: this.state.serverUrl.replace(/\\/$/, \"\")\n            };\n\n            const accessToken = this.getState(\"tokenResponse.access_token\");\n            if (accessToken) {\n                options.auth = { token: accessToken };\n            }\n            else {\n                const { username, password } = this.state;\n                if (username && password) {\n                    options.auth = {\n                        user: username,\n                        pass: password\n                    };\n                }\n            }\n            this.api = fhirJs(options);\n\n            const patientId = this.getState(\"tokenResponse.patient\");\n            if (patientId) {\n                this.patient.api = fhirJs({\n                    ...options,\n                    patient: patientId\n                });\n            }\n        }\n        return this;\n    }\n\n    /**\n     * Returns the ID of the selected patient or null. You should have requested\n     * \"launch/patient\" scope. Otherwise this will return null.\n     */\n    getPatientId(): string | null\n    {\n        const tokenResponse = this.state.tokenResponse;\n        if (tokenResponse) {\n            // We have been authorized against this server but we don't know\n            // the patient. This should be a scope issue.\n            if (!tokenResponse.patient) {\n                if (!(this.state.scope || \"\").match(/\\blaunch(\\/patient)?\\b/)) {\n                    debug(str.noScopeForId, \"patient\", \"patient\");\n                }\n                else {\n                    // The server should have returned the patient!\n                    debug(\"The ID of the selected patient is not available. Please check if your server supports that.\");\n                }\n                return null;\n            }\n            return tokenResponse.patient;\n        }\n\n        if (this.state.authorizeUri) {\n            debug(str.noIfNoAuth, \"the ID of the selected patient\");\n        }\n        else {\n            debug(str.noFreeContext, \"selected patient\");\n        }\n        return null;\n    }\n\n    /**\n     * Returns the ID of the selected encounter or null. You should have\n     * requested \"launch/encounter\" scope. Otherwise this will return null.\n     * Note that not all servers support the \"launch/encounter\" scope so this\n     * will be null if they don't.\n     */\n    getEncounterId(): string | null\n    {\n        const tokenResponse = this.state.tokenResponse;\n        if (tokenResponse) {\n            // We have been authorized against this server but we don't know\n            // the encounter. This should be a scope issue.\n            if (!tokenResponse.encounter) {\n                if (!(this.state.scope || \"\").match(/\\blaunch(\\/encounter)?\\b/)) {\n                    debug(str.noScopeForId, \"encounter\", \"encounter\");\n                }\n                else {\n                    // The server should have returned the encounter!\n                    debug(\"The ID of the selected encounter is not available. Please check if your server supports that, and that the selected patient has any recorded encounters.\");\n                }\n                return null;\n            }\n            return tokenResponse.encounter;\n        }\n\n        if (this.state.authorizeUri) {\n            debug(str.noIfNoAuth, \"the ID of the selected encounter\");\n        }\n        else {\n            debug(str.noFreeContext, \"selected encounter\");\n        }\n        return null;\n    }\n\n    /**\n     * Returns the (decoded) id_token if any. You need to request \"openid\" and\n     * \"profile\" scopes if you need to receive an id_token (if you need to know\n     * who the logged-in user is).\n     */\n    getIdToken(): fhirclient.IDToken | null\n    {\n        const tokenResponse = this.state.tokenResponse;\n        if (tokenResponse) {\n            const idToken = tokenResponse.id_token;\n            const scope = this.state.scope || \"\";\n\n            // We have been authorized against this server but we don't have\n            // the id_token. This should be a scope issue.\n            if (!idToken) {\n                const hasOpenid   = scope.match(/\\bopenid\\b/);\n                const hasProfile  = scope.match(/\\bprofile\\b/);\n                const hasFhirUser = scope.match(/\\bfhirUser\\b/);\n                if (!hasOpenid || !(hasFhirUser || hasProfile)) {\n                    debug(\n                        \"You are trying to get the id_token but you are not \" +\n                        \"using the right scopes. Please add 'openid' and \" +\n                        \"'fhirUser' or 'profile' to the scopes you are \" +\n                        \"requesting.\"\n                    );\n                }\n                else {\n                    // The server should have returned the id_token!\n                    debug(\"The id_token is not available. Please check if your server supports that.\");\n                }\n                return null;\n            }\n            return jwtDecode(idToken, this.environment) as fhirclient.IDToken;\n        }\n        if (this.state.authorizeUri) {\n            debug(str.noIfNoAuth, \"the id_token\");\n        }\n        else {\n            debug(str.noFreeContext, \"id_token\");\n        }\n        return null;\n    }\n\n    /**\n     * Returns the profile of the logged_in user (if any). This is a string\n     * having the following shape `\"{user type}/{user id}\"`. For example:\n     * `\"Practitioner/abc\"` or `\"Patient/xyz\"`.\n     */\n    getFhirUser(): string | null\n    {\n        const idToken = this.getIdToken();\n        if (idToken) {\n            // Epic may return a full url\n            // @see https://github.com/smart-on-fhir/client-js/issues/105\n            if (idToken.fhirUser) {\n                return idToken.fhirUser.split(\"/\").slice(-2).join(\"/\");\n            }\n            return idToken.profile\n        }\n        return null;\n    }\n\n    /**\n     * Returns the user ID or null.\n     */\n    getUserId(): string | null\n    {\n        const profile = this.getFhirUser();\n        if (profile) {\n            return profile.split(\"/\")[1];\n        }\n        return null;\n    }\n\n    /**\n     * Returns the type of the logged-in user or null. The result can be\n     * \"Practitioner\", \"Patient\" or \"RelatedPerson\".\n     */\n    getUserType(): string | null\n    {\n        const profile = this.getFhirUser();\n        if (profile) {\n            return profile.split(\"/\")[0];\n        }\n        return null;\n    }\n\n    /**\n     * Builds and returns the value of the `Authorization` header that can be\n     * sent to the FHIR server\n     */\n    getAuthorizationHeader(): string | null\n    {\n        const accessToken = this.getState(\"tokenResponse.access_token\");\n        if (accessToken) {\n            return \"Bearer \" + accessToken;\n        }\n        const { username, password } = this.state;\n        if (username && password) {\n            return \"Basic \" + this.environment.btoa(username + \":\" + password);\n        }\n        return null;\n    }\n\n    /**\n     * Used internally to clear the state of the instance and the state in the\n     * associated storage.\n     */\n    private async _clearState() {\n        const storage = this.environment.getStorage();\n        const key = await storage.get(SMART_KEY);\n        if (key) {\n            await storage.unset(key);\n        }\n        await storage.unset(SMART_KEY);\n        this.state.tokenResponse = {};\n    }\n\n    /**\n     * @param requestOptions Can be a string URL (relative to the serviceUrl),\n     * or an object which will be passed to fetch()\n     * @param fhirOptions Additional options to control the behavior\n     * @param _resolvedRefs DO NOT USE! Used internally.\n     * @category Request\n     */\n    async request<T = any>(\n        requestOptions: string|URL|fhirclient.RequestOptions,\n        fhirOptions: fhirclient.FhirOptions = {},\n        _resolvedRefs: fhirclient.JsonObject = {}\n    ): Promise<T>\n    {\n        const debugRequest = _debug.extend(\"client:request\");\n        assert(requestOptions, \"request requires an url or request options as argument\");\n\n        // url -----------------------------------------------------------------\n        let url: string;\n        if (typeof requestOptions == \"string\" || requestOptions instanceof URL) {\n            url = String(requestOptions);\n            requestOptions = {} as fhirclient.RequestOptions;\n        }\n        else {\n            url = String(requestOptions.url);\n        }\n\n        url = absolute(url, this.state.serverUrl);\n\n        const options = {\n            graph: fhirOptions.graph !== false,\n            flat : !!fhirOptions.flat,\n            pageLimit: fhirOptions.pageLimit ?? 1,\n            resolveReferences: makeArray(fhirOptions.resolveReferences || []) as string[],\n            useRefreshToken: fhirOptions.useRefreshToken !== false,\n            onPage: typeof fhirOptions.onPage == \"function\" ?\n                fhirOptions.onPage as (\n                    data: fhirclient.JsonObject | fhirclient.JsonObject[],\n                    references?: fhirclient.JsonObject | undefined) => any :\n                undefined\n        };\n\n        const signal = (requestOptions as RequestInit).signal || undefined;\n\n        // Refresh the access token if needed\n        if (options.useRefreshToken) {\n            await this.refreshIfNeeded({ signal })\n        }\n\n        // Add the Authorization header now, after the access token might\n        // have been updated\n        const authHeader = this.getAuthorizationHeader();\n        if (authHeader) {\n            requestOptions.headers = {\n                ...requestOptions.headers,\n                authorization: authHeader\n            };\n        }\n\n        debugRequest(\"%s, options: %O, fhirOptions: %O\", url, requestOptions, options);\n\n        let response: Response | undefined;\n\n        return super.fhirRequest<fhirclient.FetchResult>(url, requestOptions).then(result => {\n            if ((requestOptions as fhirclient.RequestOptions).includeResponse) {\n                response = (result as fhirclient.CombinedFetchResult).response;\n                return (result as fhirclient.CombinedFetchResult).body;\n            }\n            return result;\n        })\n\n        // Handle 401 ----------------------------------------------------------\n        .catch(async (error: HttpError) => {\n            if (error.status == 401) {\n\n                // !accessToken -> not authorized -> No session. Need to launch.\n                if (!this.getState(\"tokenResponse.access_token\")) {\n                    error.message += \"\\nThis app cannot be accessed directly. Please launch it as SMART app!\";\n                    throw error;\n                }\n\n                // auto-refresh not enabled and Session expired.\n                // Need to re-launch. Clear state to start over!\n                if (!options.useRefreshToken) {\n                    debugRequest(\"Your session has expired and the useRefreshToken option is set to false. Please re-launch the app.\");\n                    await this._clearState();\n                    error.message += \"\\n\" + str.expired;\n                    throw error;\n                }\n\n                // In rare cases we may have a valid access token and a refresh\n                // token and the request might still fail with 401 just because\n                // the access token has just been revoked.\n\n                // otherwise -> auto-refresh failed. Session expired.\n                // Need to re-launch. Clear state to start over!\n                debugRequest(\"Auto-refresh failed! Please re-launch the app.\");\n                await this._clearState();\n                error.message += \"\\n\" + str.expired;\n                throw error;\n            }\n            throw error;\n        })\n\n        // Handle 403 ----------------------------------------------------------\n        .catch((error: HttpError) => {\n            if (error.status == 403) {\n                debugRequest(\"Permission denied! Please make sure that you have requested the proper scopes.\");\n            }\n            throw error;\n        })\n\n        .then(async (data: any) => {\n\n            // At this point we don't know what `data` actually is!\n            // We might get an empty or falsy result. If so return it as is\n            // Also handle raw responses\n            if (!data || typeof data == \"string\" || data instanceof Response) {\n                if ((requestOptions as fhirclient.FetchOptions).includeResponse) {\n                    return {\n                        body: data,\n                        response\n                    }\n                }\n                return data;\n            }\n            \n            // Resolve References ----------------------------------------------\n            await this.fetchReferences(\n                data as any,\n                options.resolveReferences,\n                options.graph,\n                _resolvedRefs,\n                requestOptions as fhirclient.RequestOptions\n            );\n\n            return Promise.resolve(data)\n\n            // Pagination ------------------------------------------------------\n            .then(async _data => {\n                if (_data && _data.resourceType == \"Bundle\") {\n                    const links = (_data.link || []) as fhirclient.FHIR.BundleLink[];\n\n                    if (options.flat) {\n                        _data = (_data.entry || []).map(\n                            (entry: fhirclient.FHIR.BundleEntry) => entry.resource\n                        );\n                    }\n\n                    if (options.onPage) {\n                        await options.onPage(_data, { ..._resolvedRefs });\n                    }\n\n                    if (--options.pageLimit) {\n                        const next = links.find(l => l.relation == \"next\");\n                        _data = makeArray(_data);\n                        if (next && next.url) {\n                            const nextPage = await this.request(\n                                {\n                                    url: next.url,\n\n                                    // Aborting the main request (even after it is complete)\n                                    // must propagate to any child requests and abort them!\n                                    // To do so, just pass the same AbortSignal if one is\n                                    // provided.\n                                    signal\n                                },\n                                options,\n                                _resolvedRefs\n                            );\n\n                            if (options.onPage) {\n                                return null;\n                            }\n\n                            if (options.resolveReferences.length) {\n                                Object.assign(_resolvedRefs, nextPage.references);\n                                return _data.concat(makeArray(nextPage.data || nextPage));\n                            }\n                            return _data.concat(makeArray(nextPage));\n                        }\n                    }\n                }\n                return _data;\n            })\n\n            // Finalize --------------------------------------------------------\n            .then(_data => {\n                if (options.graph) {\n                    _resolvedRefs = {};\n                }\n                else if (!options.onPage && options.resolveReferences.length) {\n                    return {\n                        data: _data,\n                        references: _resolvedRefs\n                    };\n                }\n                return _data;\n            })\n            .then(_data => {\n                if ((requestOptions as fhirclient.FetchOptions).includeResponse) {\n                    return {\n                        body: _data,\n                        response\n                    }\n                }\n                return _data;\n            });\n        });\n    }\n\n    /**\n     * Checks if access token and refresh token are present. If they are, and if\n     * the access token is expired or is about to expire in the next 10 seconds,\n     * calls `this.refresh()` to obtain new access token.\n     * @param requestOptions Any options to pass to the fetch call. Most of them\n     * will be overridden, bit it might still be useful for passing additional\n     * request options or an abort signal.\n     * @category Request\n     */\n    refreshIfNeeded(requestOptions: RequestInit = {}): Promise<fhirclient.ClientState>\n    {\n        const accessToken  = this.getState(\"tokenResponse.access_token\");\n        const refreshToken = this.getState(\"tokenResponse.refresh_token\");\n        const expiresAt    = this.state.expiresAt || 0;\n\n        if (accessToken && refreshToken && expiresAt - 10 < Date.now() / 1000) {\n            return this.refresh(requestOptions);\n        }\n\n        return Promise.resolve(this.state);\n    }\n\n    /**\n     * Use the refresh token to obtain new access token. If the refresh token is\n     * expired (or this fails for any other reason) it will be deleted from the\n     * state, so that we don't enter into loops trying to re-authorize.\n     *\n     * This method is typically called internally from [[request]] if\n     * certain request fails with 401.\n     *\n     * @param requestOptions Any options to pass to the fetch call. Most of them\n     * will be overridden, bit it might still be useful for passing additional\n     * request options or an abort signal.\n     * @category Request\n     */\n    refresh(requestOptions: RequestInit = {}): Promise<fhirclient.ClientState>\n    {\n        const debugRefresh = _debug.extend(\"client:refresh\");\n        debugRefresh(\"Attempting to refresh with refresh_token...\");\n\n        const refreshToken = this.state?.tokenResponse?.refresh_token;\n        assert(refreshToken, \"Unable to refresh. No refresh_token found.\");\n\n        const tokenUri = this.state.tokenUri;\n        assert(tokenUri, \"Unable to refresh. No tokenUri found.\");\n\n        const scopes = this.getState(\"tokenResponse.scope\") || \"\";\n        const hasOfflineAccess = scopes.search(/\\boffline_access\\b/) > -1;\n        const hasOnlineAccess = scopes.search(/\\bonline_access\\b/) > -1;\n        assert(hasOfflineAccess || hasOnlineAccess, \"Unable to refresh. No offline_access or online_access scope found.\");\n\n        // This method is typically called internally from `request` if certain\n        // request fails with 401. However, clients will often run multiple\n        // requests in parallel which may result in multiple refresh calls.\n        // To avoid that, we keep a reference to the current refresh task (if any).\n        if (!this._refreshTask) {\n            let body = `grant_type=refresh_token&refresh_token=${encodeURIComponent(refreshToken)}`;\n            if (this.environment.options.refreshTokenWithClientId) {\n                body += `&client_id=${this.state.clientId}`;\n            }\n            const refreshRequestOptions = {\n                credentials: this.environment.options.refreshTokenWithCredentials || \"same-origin\",\n                ...requestOptions,\n                method : \"POST\",\n                mode   : \"cors\" as RequestMode,\n                headers: {\n                    ...(requestOptions.headers || {}),\n                    \"content-type\": \"application/x-www-form-urlencoded\"\n                },\n                body: body\n            };\n\n            // custom authorization header can be passed on manual calls\n            if (!(\"authorization\" in refreshRequestOptions.headers)) {\n                const { clientSecret, clientId } = this.state;\n                if (clientSecret) {\n                    // @ts-ignore\n                    refreshRequestOptions.headers.authorization = \"Basic \" + this.environment.btoa(\n                        clientId + \":\" + clientSecret\n                    );\n                }\n            }\n\n            this._refreshTask = request<fhirclient.TokenResponse>(tokenUri, refreshRequestOptions)\n            .then(data => {\n                assert(data.access_token, \"No access token received\");\n                debugRefresh(\"Received new access token response %O\", data);\n                this.state.tokenResponse = { ...this.state.tokenResponse, ...data };\n                this.state.expiresAt = getAccessTokenExpiration(data, this.environment);\n                return this.state;\n            })\n            .catch((error: Error) => {\n                if (this.state?.tokenResponse?.refresh_token) {\n                    debugRefresh(\"Deleting the expired or invalid refresh token.\");\n                    delete this.state.tokenResponse.refresh_token;\n                }\n                throw error;\n            })\n            .finally(() => {\n                this._refreshTask = null;\n                const key = this.state.key;\n                if (key) {\n                    this.environment.getStorage().set(key, this.state);\n                } else {\n                    debugRefresh(\"No 'key' found in Clint.state. Cannot persist the instance.\");\n                }\n            });\n        }\n\n        return this._refreshTask;\n    }\n\n    // utils -------------------------------------------------------------------\n\n    /**\n     * Groups the observations by code. Returns a map that will look like:\n     * ```js\n     * const map = client.byCodes(observations, \"code\");\n     * // map = {\n     * //     \"55284-4\": [ observation1, observation2 ],\n     * //     \"6082-2\": [ observation3 ]\n     * // }\n     * ```\n     * @param observations Array of observations\n     * @param property The name of a CodeableConcept property to group by\n     * @todo This should be deprecated and moved elsewhere. One should not have\n     * to obtain an instance of [[Client]] just to use utility functions like this.\n     * @deprecated\n     * @category Utility\n     */\n    byCode(\n        observations: fhirclient.FHIR.Observation | fhirclient.FHIR.Observation[],\n        property: string\n    ): fhirclient.ObservationMap\n    {\n        return byCode(observations, property);\n    }\n\n    /**\n     * First groups the observations by code using `byCode`. Then returns a function\n     * that accepts codes as arguments and will return a flat array of observations\n     * having that codes. Example:\n     * ```js\n     * const filter = client.byCodes(observations, \"category\");\n     * filter(\"laboratory\") // => [ observation1, observation2 ]\n     * filter(\"vital-signs\") // => [ observation3 ]\n     * filter(\"laboratory\", \"vital-signs\") // => [ observation1, observation2, observation3 ]\n     * ```\n     * @param observations Array of observations\n     * @param property The name of a CodeableConcept property to group by\n     * @todo This should be deprecated and moved elsewhere. One should not have\n     * to obtain an instance of [[Client]] just to use utility functions like this.\n     * @deprecated\n     * @category Utility\n     */\n    byCodes(\n        observations: fhirclient.FHIR.Observation | fhirclient.FHIR.Observation[],\n        property: string\n    ): (...codes: string[]) => any[]\n    {\n        return byCodes(observations, property);\n    }\n\n    /**\n     * @category Utility\n     */\n    units = units;\n\n    /**\n     * Walks through an object (or array) and returns the value found at the\n     * provided path. This function is very simple so it intentionally does not\n     * support any argument polymorphism, meaning that the path can only be a\n     * dot-separated string. If the path is invalid returns undefined.\n     * @param obj The object (or Array) to walk through\n     * @param path The path (eg. \"a.b.4.c\")\n     * @returns {*} Whatever is found in the path or undefined\n     * @todo This should be deprecated and moved elsewhere. One should not have\n     * to obtain an instance of [[Client]] just to use utility functions like this.\n     * @deprecated\n     * @category Utility\n     */\n    getPath(obj: Record<string, any>, path = \"\"): any {\n        return getPath(obj, path);\n    }\n\n    /**\n     * Returns a copy of the client state. Accepts a dot-separated path argument\n     * (same as for `getPath`) to allow for selecting specific properties.\n     * Examples:\n     * ```js\n     * client.getState(); // -> the entire state object\n     * client.getState(\"serverUrl\"); // -> the URL we are connected to\n     * client.getState(\"tokenResponse.patient\"); // -> The selected patient ID (if any)\n     * ```\n     * @param path The path (eg. \"a.b.4.c\")\n     * @returns {*} Whatever is found in the path or undefined\n     */\n    getState(path = \"\") {\n        return getPath({ ...this.state }, path);\n    }\n\n}\n", "import type { B<PERSON>le, BundleLink, Resource } from 'fhir/r4'\nimport { fhirVersions }                      from './settings'\nimport { fhirclient }                        from './types'\nimport {\n    absolute,\n    debug as _debug,\n    getPath,\n    setPath,\n    makeArray,\n    request,\n    fetchConformanceStatement,\n    assertJsonPatch,\n    assert\n} from \"./lib\";\n\n\nconst debug = _debug.extend(\"FhirClient\");\n\ninterface RequestOptions extends RequestInit {\n    /**\n     * If the `includeResponse` option is `true` we can expect a\n     * `CombinedFetchResult` where the `response` property is the `Response`\n     * object and the `body` property is the parsed body.\n     */\n    includeResponse?: boolean;\n\n    /**\n     * Sets a limit if applicable. For example, we can control how many pages to\n     * or resources to fetch\n     */\n    limit?: number\n\n    /**\n     * An object where keys are URLs (or other unique strings) and values are\n     * the request results. If provided, it will be used as in-memory cache.\n     * Otherwise no cache will be used, but you can still use the cache option\n     * for fetch requests if using this in browsers.\n     */\n    cacheMap?: Record<string, any>\n}\n\n/**\n * This is a basic FHIR client for making basic FHIR API calls\n */\nexport default class FhirClient\n{\n    /**\n     * The state of the client instance is an object with various properties.\n     * It contains some details about how the client has been authorized and\n     * determines the behavior of the client instance. This state is persisted\n     * in `SessionStorage` in browsers or in request session on the servers.\n     */\n    readonly fhirBaseUrl: string;\n\n    /**\n     * Validates the parameters, creates an instance and tries to connect it to\n     * FhirJS, if one is available globally.\n     */\n    constructor(fhirBaseUrl: string)\n    {\n        assert(\n            fhirBaseUrl && typeof fhirBaseUrl === \"string\" && fhirBaseUrl.match(/https?:\\/\\/.+/),\n            \"A \\\"fhirBaseUrl\\\" string parameter is required and must begin with \\\"http(s)\\\"\"\n        );\n        this.fhirBaseUrl = fhirBaseUrl;\n    }\n\n    /**\n     * Creates a new resource in a server-assigned location\n     * @see http://hl7.org/fhir/http.html#create\n     * @param resource A FHIR resource to be created\n     * @param [requestOptions] Any options to be passed to the fetch call.\n     * Note that `method` and `body` will be ignored.\n     * @category Request\n     */\n    async create<R = fhirclient.FHIR.Resource, O extends fhirclient.FetchOptions = {}>(\n        resource: fhirclient.FHIR.Resource,\n        requestOptions?: O\n    ): Promise<O[\"includeResponse\"] extends true ? fhirclient.CombinedFetchResult<R> : R>\n    {\n        return this.fhirRequest(resource.resourceType!, {\n            ...requestOptions,\n            method: \"POST\",\n            body: JSON.stringify(resource),\n            headers: {\n                \"content-type\": \"application/json\",\n                ...(requestOptions || {}).headers\n            }\n        });\n    }\n\n    /**\n     * Creates a new current version for an existing resource or creates an\n     * initial version if no resource already exists for the given id.\n     * @see http://hl7.org/fhir/http.html#update\n     * @param resource A FHIR resource to be updated\n     * @param requestOptions Any options to be passed to the fetch call.\n     * Note that `method` and `body` will be ignored.\n     * @category Request\n     */\n    async update<R = fhirclient.FHIR.Resource, O extends fhirclient.FetchOptions = {}>(\n        resource: fhirclient.FHIR.Resource,\n        requestOptions?: O\n    ): Promise<O[\"includeResponse\"] extends true ? fhirclient.CombinedFetchResult<R> : R>\n    {\n        return this.fhirRequest(`${resource.resourceType}/${resource.id}`, {\n            ...requestOptions,\n            method: \"PUT\",\n            body: JSON.stringify(resource),\n            headers: {\n                \"content-type\": \"application/json\",\n                ...(requestOptions || {}).headers\n            }\n        });\n    }\n\n    /**\n     * Removes an existing resource.\n     * @see http://hl7.org/fhir/http.html#delete\n     * @param url Relative URI of the FHIR resource to be deleted\n     * (format: `resourceType/id`)\n     * @param requestOptions Any options (except `method` which will be fixed\n     * to `DELETE`) to be passed to the fetch call.\n     * @category Request\n     */\n    async delete<R = unknown>(url: string, requestOptions: fhirclient.FetchOptions = {}): Promise<R>\n    {\n        return this.fhirRequest<R>(url, { ...requestOptions, method: \"DELETE\" });\n    }\n\n    /**\n     * Makes a JSON Patch to the given resource\n     * @see http://hl7.org/fhir/http.html#patch\n     * @param url Relative URI of the FHIR resource to be patched\n     * (format: `resourceType/id`)\n     * @param patch A JSON Patch array to send to the server, For details\n     * see https://datatracker.ietf.org/doc/html/rfc6902\n     * @param requestOptions Any options to be passed to the fetch call,\n     * except for `method`, `url` and `body` which cannot be overridden.\n     * @since 2.4.0\n     * @category Request\n     * @typeParam ResolveType This method would typically resolve with the\n     * patched resource or reject with an OperationOutcome. However, this may\n     * depend on the server implementation or even on the request headers.\n     * For that reason, if the default resolve type (which is\n     * [[fhirclient.FHIR.Resource]]) does not work for you, you can pass\n     * in your own resolve type parameter.\n     */\n    async patch<ResolveType=fhirclient.FHIR.Resource>(\n        url: string,\n        patch: fhirclient.JsonPatch,\n        requestOptions: fhirclient.FetchOptions = {}\n    ): Promise<ResolveType>\n    {\n        assertJsonPatch(patch);\n        return this.fhirRequest<ResolveType>(url, {\n            ...requestOptions,\n            method: \"PATCH\",\n            body: JSON.stringify(patch),\n            headers: {\n                \"prefer\": \"return=presentation\",\n                \"content-type\": \"application/json-patch+json; charset=UTF-8\",\n                ...requestOptions.headers,\n            }\n        });\n    }\n\n    private async resolveRef(\n        obj  : Resource,\n        path : string,\n        graph: boolean,\n        cache: Record<string, any>,\n        requestOptions: Omit<fhirclient.RequestOptions, \"url\"> = {}\n    ) {\n        const node = getPath(obj, path);\n        if (node) {\n            const isArray = Array.isArray(node);\n            return Promise.all(makeArray(node).filter(Boolean).map((item, i) => {\n                const ref = item.reference;\n                if (ref) {\n                    return this.fhirRequest(ref, { ...requestOptions, includeResponse: false, cacheMap: cache }).then(sub => {\n                        if (graph) {\n                            if (isArray) {\n                                if (path.indexOf(\"..\") > -1) {\n                                    setPath(obj, `${path.replace(\"..\", `.${i}.`)}`, sub);    \n                                } else {\n                                    setPath(obj, `${path}.${i}`, sub);\n                                }\n                            } else {\n                                setPath(obj, path, sub);\n                            }\n                        }\n                    }).catch((ex) => {\n                        if (ex?.status === 404) {\n                            console.warn(`Missing reference ${ref}. ${ex}`)\n                        } else {\n                            throw ex;\n                        }\n                    });\n                }\n            }));\n        }\n    }\n\n    /**\n     * Fetches all references in the given resource, ignoring duplicates, and\n     * then modifies the resource by \"mounting\" the resolved references in place\n     */\n    async resolveReferences(\n        resource: Resource,\n        references: string[],\n        requestOptions: Omit<fhirclient.RequestOptions, \"url\"> = {}\n    ): Promise<void> {\n        await this.fetchReferences(resource, references, true, {}, requestOptions)\n    }\n\n    protected async fetchReferences(\n        resource: Resource,\n        references: string[],\n        graph: boolean,\n        cache: Record<string, any> = {},\n        requestOptions: Omit<fhirclient.RequestOptions, \"url\"> = {}\n    ): Promise<Record<string, any>> {\n\n        if (resource.resourceType == \"Bundle\") {\n            for (const item of ((resource as Bundle).entry || [])) {\n                if (item.resource) {\n                    await this.fetchReferences(item.resource, references, graph, cache, requestOptions)\n                }\n            }\n            return cache\n        }\n        \n        // 1. Sanitize paths, remove any invalid ones\n        let paths = references.map(path => String(path).trim()).filter(Boolean);\n\n        // 2. Remove duplicates\n        paths = paths.reduce((prev, cur) => {\n            if (prev.includes(cur)) {\n                debug(\"Duplicated reference path \\\"%s\\\"\", cur);\n            } else {\n                prev.push(cur)\n            }\n            return prev\n        }, [] as string[]);\n\n        // 3. Early exit if no valid paths are found\n        if (!paths.length) {\n            return Promise.resolve(cache);\n        }\n\n        // 4. Group the paths by depth so that child refs are looked up\n        // after their parents!\n        const groups: Record<string, any> = {};\n        paths.forEach(path => {\n            const len = path.split(\".\").length;\n            if (!groups[len]) {\n                groups[len] = [];\n            }\n            groups[len].push(path);\n        });\n\n        // 5. Execute groups sequentially! Paths within same group are\n        // fetched in parallel!\n        let task: Promise<any> = Promise.resolve();\n        Object.keys(groups).sort().forEach(len => {\n            const group = groups[len];\n            task = task.then(() => Promise.all(group.map((path: string) => {\n                return this.resolveRef(resource, path, graph, cache, requestOptions);\n            })));\n        });\n        await task;\n        return cache\n    }\n\n    /**\n     * Fetches all references in the given resource, ignoring duplicates\n     */\n    async getReferences(\n        resource: Resource,\n        references: string[],\n        requestOptions: Omit<fhirclient.RequestOptions, \"url\"> = {}\n    ): Promise<Record<string, Resource>> {\n        const refs = await this.fetchReferences(resource, references, false, {}, requestOptions)\n        const out: any = {}\n        for (const key in refs) {\n            out[key] = await refs[key]\n        }\n        return out\n    }\n\n    /**\n     * Given a FHIR Bundle or a URL pointing to a bundle, iterates over all\n     * entry resources. Note that this will also automatically crawl through\n     * further pages (if any)\n     */\n    async *resources(bundleOrUrl: Bundle | string | URL, options?: RequestOptions) {\n        let count = 0\n        for await(const page of this.pages(bundleOrUrl, options)) {\n            for (const entry of (page.entry || [])) {\n                if (options?.limit && ++count > options.limit) {\n                    return\n                }\n                yield entry.resource\n            }\n        }\n    }\n\n    /**\n     * Given a FHIR Bundle or a URL pointing to a bundle, iterates over all\n     * pages. Note that this will automatically crawl through\n     * further pages (if any) but it will not detect previous pages. It is\n     * designed to be called on the first page and fetch any followup pages.\n     */\n    async *pages(\n        bundleOrUrl    : Bundle | string | URL,\n        requestOptions?: RequestOptions\n    ) {\n        const { limit, ...options } = requestOptions || {}\n        \n        const fetchPage = (url: string | URL) => this.fhirRequest(url, options)\n\n        let page: Bundle = typeof bundleOrUrl === \"string\" || bundleOrUrl instanceof URL ?\n            await fetchPage(bundleOrUrl) :\n            bundleOrUrl;\n\n        let count = 0\n  \n        while (page && page.resourceType === \"Bundle\" && (!limit || ++count <= limit)) {\n            \n            // Yield the current page\n            yield page;\n        \n            // If caller aborted, stop crawling\n            if (options?.signal?.aborted) {\n                break;\n            }\n        \n            // Find the \"next\" link\n            const nextLink = (page.link ?? []).find(\n                (l: BundleLink) => l.relation === 'next' && typeof l.url === 'string'\n            );\n        \n            if (!nextLink) {\n                break; // no more pages\n            }\n    \n            // Fetch the next page\n            page = await fetchPage(nextLink.url!);\n        }\n    }\n\n    /**\n     * The method responsible for making all http requests\n     */\n    async fhirRequest<T = any>(uri: string | URL, options: RequestOptions = {}): Promise<T>\n    {\n        assert(options, \"fhirRequest requires a uri as first argument\");\n\n        const path = uri + \"\"\n        const url  = absolute(path, this.fhirBaseUrl);\n        const { cacheMap } = options\n\n        if (cacheMap) {\n            if (!(path in cacheMap)) {\n                cacheMap[path] = request<T>(url, options)\n                .then(res => {\n                    cacheMap[path] = res;\n                    return res;\n                })\n                .catch(error => {\n                    delete cacheMap[path];\n                    throw error;\n                });\n            }\n            return cacheMap[path];\n        }\n        return request<T>(url, options)\n    }\n\n    /**\n     * Returns a promise that will be resolved with the fhir version as defined\n     * in the CapabilityStatement.\n     */\n    async getFhirVersion(): Promise<string>\n    {\n        return fetchConformanceStatement(this.fhirBaseUrl)\n            .then((metadata) => metadata.fhirVersion);\n    }\n\n    /**\n     * Returns a promise that will be resolved with the numeric fhir version\n     * - 2 for DSTU2\n     * - 3 for STU3\n     * - 4 for R4\n     * - 0 if the version is not known\n     */\n    async getFhirRelease(): Promise<number>\n    {\n        return this.getFhirVersion().then(v => (fhirVersions as any)[v] ?? 0);\n    }\n}\n", "import { fhirclient } from \"./types\";\n\n\nexport default class HttpError extends <PERSON>rror\n{\n    /**\n     * The HTTP status code for this error\n     */\n    statusCode: number;\n\n    /**\n     * The HTTP status code for this error.\n     * Note that this is the same as `status`, i.e. the code is available\n     * through any of these.\n     */\n    status: number;\n\n    /**\n     * The HTTP status text corresponding to this error\n     */\n    statusText: string;\n\n    /**\n     * Reference to the HTTP Response object\n     */\n    response: Response;\n\n    constructor(response: Response) {\n        super(`${response.status} ${response.statusText}\\nURL: ${response.url}`);\n        this.name       = \"HttpError\";\n        this.response   = response;\n        this.statusCode = response.status;\n        this.status     = response.status;\n        this.statusText = response.statusText;\n    }\n\n    async parse()\n    {\n        if (!this.response.bodyUsed) {\n            try {\n                const type = this.response.headers.get(\"content-type\") || \"text/plain\";\n                if (type.match(/\\bjson\\b/i)) {\n                    let body = await this.response.json();\n                    if (body.error) {\n                        this.message += \"\\n\" + body.error;\n                        if (body.error_description) {\n                            this.message += \": \" + body.error_description;\n                        }\n                    }\n                    else {\n                        this.message += \"\\n\\n\" + JSON.stringify(body, null, 4);\n                    }\n                }\n                else if (type.match(/^text\\//i)) {\n                    let body = await this.response.text();\n                    if (body) {\n                        this.message += \"\\n\\n\" + body;\n                    }\n                }\n            } catch {\n                // ignore\n            }\n        }\n\n        return this;\n    }\n\n    toJSON() {\n        return {\n            name      : this.name,\n            statusCode: this.statusCode,\n            status    : this.status,\n            statusText: this.statusText,\n            message   : this.message\n        };\n    }\n}\n", "import { ready, authorize, init } from \"../smart\";\nimport Client from \"../Client\";\nimport BrowserStorage from \"../storage/BrowserStorage\";\nimport { fhirclient } from \"../types\";\nimport * as security from \"../security/browser\"\nimport { encodeURL, decode, fromUint8Array } from \"js-base64\"\n\n/**\n * Browser Adapter\n */\nexport default class BrowserAdapter implements fhirclient.Adapter\n{\n    /**\n     * Stores the URL instance associated with this adapter\n     */\n    private _url: URL | null = null;\n\n    /**\n     * Holds the Storage instance associated with this instance\n     */\n    private _storage: fhirclient.Storage | null = null;\n\n    /**\n     * Environment-specific options\n     */\n    options: fhirclient.BrowserFHIRSettings;\n\n    security = security;\n\n    /**\n     * @param options Environment-specific options\n     */\n    constructor(options: fhirclient.BrowserFHIRSettings = {})\n    {\n        this.options = {\n            // Replaces the browser's current URL\n            // using window.history.replaceState API or by reloading.\n            replaceBrowserHistory: true,\n\n            // When set to true, this variable will fully utilize\n            // HTML5 sessionStorage API.\n            // This variable can be overridden to false by setting\n            // FHIR.oauth2.settings.fullSessionStorageSupport = false.\n            // When set to false, the sessionStorage will be keyed\n            // by a state variable. This is to allow the embedded IE browser\n            // instances instantiated on a single thread to continue to\n            // function without having sessionStorage data shared\n            // across the embedded IE instances.\n            fullSessionStorageSupport: true,\n\n            // Do we want to send cookies while making a request to the token\n            // endpoint in order to obtain new access token using existing\n            // refresh token. In rare cases the auth server might require the\n            // client to send cookies along with those requests. In this case\n            // developers will have to change this before initializing the app\n            // like so:\n            // `FHIR.oauth2.settings.refreshTokenWithCredentials = \"include\";`\n            // or\n            // `FHIR.oauth2.settings.refreshTokenWithCredentials = \"same-origin\";`\n            // Can be one of:\n            // \"include\"     - always send cookies\n            // \"same-origin\" - only send cookies if we are on the same domain (default)\n            // \"omit\"        - do not send cookies\n            refreshTokenWithCredentials: \"same-origin\",\n\n            ...options\n        };\n    }\n\n    /**\n     * Given a relative path, returns an absolute url using the instance base URL\n     */\n    relative(path: string): string\n    {\n        return new URL(path, this.getUrl().href).href;\n    }\n\n    /**\n     * In browsers we need to be able to (dynamically) check if fhir.js is\n     * included in the page. If it is, it should have created a \"fhir\" variable\n     * in the global scope.\n     */\n    get fhir()\n    {\n        // @ts-ignore\n        return typeof fhir === \"function\" ? fhir : null;\n    }\n\n    /**\n     * Given the current environment, this method must return the current url\n     * as URL instance\n     */\n    getUrl(): URL\n    {\n        if (!this._url) {\n            this._url = new URL(location + \"\");\n        }\n        return this._url;\n    }\n\n    /**\n     * Given the current environment, this method must redirect to the given\n     * path\n     */\n    redirect(to: string): void\n    {\n        location.href = to;\n    }\n\n    /**\n     * Returns a BrowserStorage object which is just a wrapper around\n     * sessionStorage\n     */\n    getStorage(): BrowserStorage\n    {\n        if (!this._storage) {\n            this._storage = new BrowserStorage();\n        }\n        return this._storage;\n    }\n\n    /**\n     * Returns a reference to the AbortController constructor. In browsers,\n     * AbortController will always be available as global (native or polyfilled)\n     */\n    getAbortController()\n    {\n        return AbortController;\n    }\n\n    /**\n     * ASCII string to Base64\n     */\n    atob(str: string): string\n    {\n        return window.atob(str);\n    }\n\n    /**\n     * Base64 to ASCII string\n     */\n    btoa(str: string): string\n    {\n        return window.btoa(str);\n    }\n\n    base64urlencode(input: string | Uint8Array)\n    {\n        if (typeof input == \"string\") {\n            return encodeURL(input)\n        }\n        return fromUint8Array(input, true)\n    }\n\n    base64urldecode(input: string)\n    {\n        return decode(input)\n    }\n\n    /**\n     * Creates and returns adapter-aware SMART api. Not that while the shape of\n     * the returned object is well known, the arguments to this function are not.\n     * Those who override this method are free to require any environment-specific\n     * arguments. For example in node we will need a request, a response and\n     * optionally a storage or storage factory function.\n     */\n    getSmartApi(): fhirclient.SMART\n    {\n        return {\n            ready    : (...args: any[]) => ready(this, ...args),\n            authorize: options => authorize(this, options),\n            init     : options => init(this, options),\n            client   : (state: string | fhirclient.ClientState) => new Client(this, state),\n            options  : this.options,\n            utils: {\n                security\n            }\n        };\n    }\n}\n", "\n// Note: the following 2 imports appear as unused but they affect how tsc is\n// generating type definitions!\nimport { fhirclient } from \"../types\";\nimport Client from \"../Client\";\n\n// In Browsers we create an adapter, get the SMART api from it and build the\n// global FHIR object\nimport BrowserAdapter from \"../adapters/BrowserAdapter\";\nimport FhirClient from \"../FhirClient\";\n\nconst adapter = new BrowserAdapter();\nconst { ready, authorize, init, client, options, utils } = adapter.getSmartApi();\n\n// We have two kinds of browser builds - \"pure\" for new browsers and \"legacy\"\n// for old ones. In pure builds we assume that the browser supports everything\n// we need. In legacy mode, the library also acts as a polyfill. Babel will\n// automatically polyfill everything except \"fetch\", which we have to handle\n// manually.\n// @ts-ignore\nif (typeof FHIRCLIENT_PURE == \"undefined\") {\n    const fetch = require(\"cross-fetch\");\n    require(\"abortcontroller-polyfill/dist/abortcontroller-polyfill-only\");\n    if (!window.fetch) {\n        window.fetch    = fetch.default;\n        window.Headers  = fetch.Headers;\n        window.Request  = fetch.Request;\n        window.Response = fetch.Response;\n    }\n}\n\n// $lab:coverage:off$\nconst FHIR = {\n    AbortController: window.AbortController,\n\n    client,\n\n    /**\n     * Using this class if you are connecting to open server that does not\n     * require authorization.\n     */\n    FhirClient,\n\n    utils,\n    oauth2: {\n        settings: options,\n        ready,\n        authorize,\n        init\n    }\n};\n\nexport = FHIR;\n// $lab:coverage:on$\n", "/*\n * This file contains some shared functions. They are used by other modules, but\n * are defined here so that tests can import this library and test them.\n */\n\nimport HttpError from \"./HttpError\";\nimport { patientParams } from \"./settings\";\nimport { fhirclient } from \"./types\";\nconst debug = require(\"debug\");\n\n// $lab:coverage:off$\n// @ts-ignore\nconst { fetch } = typeof FHIRCLIENT_PURE !== \"undefined\" ? window : require(\"cross-fetch\");\n// $lab:coverage:on$\n\nconst _debug     = debug(\"FHIR\");\nexport { _debug as debug };\n\n/**\n * The cache for the `getAndCache` function\n */\nconst cache: Record<string, any> = {};\n\n/**\n * A namespace with functions for converting between different measurement units\n */\nexport const units = {\n    cm({ code, value }: fhirclient.CodeValue) {\n        ensureNumerical({ code, value });\n        if (code == \"cm\"     ) return value;\n        if (code == \"m\"      ) return value *   100;\n        if (code == \"in\"     ) return value *  2.54;\n        if (code == \"[in_us]\") return value *  2.54;\n        if (code == \"[in_i]\" ) return value *  2.54;\n        if (code == \"ft\"     ) return value * 30.48;\n        if (code == \"[ft_us]\") return value * 30.48;\n        throw new Error(\"Unrecognized length unit: \" + code);\n    },\n    kg({ code, value }: fhirclient.CodeValue){\n        ensureNumerical({ code, value });\n        if (code == \"kg\"    ) return value;\n        if (code == \"g\"     ) return value / 1000;\n        if (code.match(/lb/)) return value / 2.20462;\n        if (code.match(/oz/)) return value / 35.274;\n        throw new Error(\"Unrecognized weight unit: \" + code);\n    },\n    any(pq: fhirclient.CodeValue){\n        ensureNumerical(pq);\n        return pq.value;\n    }\n};\n\n/**\n * Assertion function to guard arguments for `units` functions\n */\nfunction ensureNumerical({ value, code }: fhirclient.CodeValue) {\n    if (typeof value !== \"number\") {\n        throw new Error(\"Found a non-numerical unit: \" + value + \" \" + code);\n    }\n}\n\n/**\n * Used in fetch Promise chains to reject if the \"ok\" property is not true\n */\nexport async function checkResponse(resp: Response): Promise<Response> {\n    if (!resp.ok) {\n        const error = new HttpError(resp);\n        await error.parse();\n        throw error;\n    }\n    return resp;\n}\n\n/**\n * Used in fetch Promise chains to return the JSON version of the response.\n * Note that `resp.json()` will throw on empty body so we use resp.text()\n * instead.\n */\nexport function responseToJSON(resp: Response): Promise<object|string> {\n    return resp.text().then(text => text.length ? JSON.parse(text) : \"\");\n}\n\nexport function loweCaseKeys<T=Record<string, any> | any[] | undefined>(obj: T): T {\n    \n    // Can be undefined to signal that this key should be removed\n    if (!obj) {\n        return obj as T\n    }\n\n    // Arrays are valid values in case of recursive calls\n    if (Array.isArray(obj)) {\n        return obj.map(v => v && typeof v === \"object\" ? loweCaseKeys(v) : v) as unknown as T;\n    }\n\n    // Plain object\n    let out: Record<string, any> = {};\n    Object.keys(obj).forEach(key => {\n        const lowerKey = key.toLowerCase()\n        const v = (obj as Record<string, any>)[key]\n        out[lowerKey] = v && typeof v == \"object\" ? loweCaseKeys(v) : v;\n    });\n    return out as T;\n}\n\n/**\n * This is our built-in request function. It does a few things by default\n * (unless told otherwise):\n * - Makes CORS requests\n * - Sets accept header to \"application/json\"\n * - Handles errors\n * - If the response is json return the json object\n * - If the response is text return the result text\n * - Otherwise return the response object on which we call stuff like `.blob()`\n */\nexport function request<T = fhirclient.FetchResult>(\n    url: string | Request,\n    requestOptions: fhirclient.FetchOptions = {}\n): Promise<T>\n{\n    const { includeResponse, ...options } = requestOptions;\n    return fetch(url, {\n        mode: \"cors\",\n        ...options,\n        headers: {\n            accept: \"application/json\",\n            ...loweCaseKeys(options.headers)\n        }\n    })\n    .then(checkResponse)\n    .then((res: Response) => {\n        const type = res.headers.get(\"content-type\") + \"\";\n        if (type.match(/\\bjson\\b/i)) {\n            return responseToJSON(res).then(body => ({ res, body }));\n        }\n        if (type.match(/^text\\//i)) {\n            return res.text().then(body => ({ res, body }));\n        }\n        return { res };\n    })\n    .then(({res, body}: {res:Response, body?:fhirclient.JsonObject|string}) => {\n\n        // Some servers will reply after CREATE with json content type but with\n        // empty body. In this case check if a location header is received and\n        // fetch that to use it as the final result.\n        if (!body && res.status == 201) {\n            const location = res.headers.get(\"location\");\n            if (location) {\n                return request(location, { ...options, method: \"GET\", body: null, includeResponse });\n            }\n        }\n\n        if (includeResponse) {\n            return { body, response: res };\n        }\n\n        // For any non-text and non-json response return the Response object.\n        // This to let users decide if they want to call text(), blob() or\n        // something else on it\n        if (body === undefined) {\n            return res;\n        }\n\n        // Otherwise just return the parsed body (can also be \"\" or null)\n        return body;\n    });\n}\n\n/**\n * Makes a request using `fetch` and stores the result in internal memory cache.\n * The cache is cleared when the page is unloaded.\n * @param url The URL to request\n * @param requestOptions Request options\n * @param force If true, reload from source and update the cache, even if it has\n * already been cached.\n */\nexport function getAndCache(url: string, requestOptions?: RequestInit, force: boolean = process.env.NODE_ENV === \"test\"): Promise<any> {\n    if (force || !cache[url]) {\n        cache[url] = request(url, requestOptions);\n        return cache[url];\n    }\n    return Promise.resolve(cache[url]);\n}\n\n/**\n * Fetches the conformance statement from the given base URL.\n * Note that the result is cached in memory (until the page is reloaded in the\n * browser) because it might have to be re-used by the client\n * @param baseUrl The base URL of the FHIR server\n * @param [requestOptions] Any options passed to the fetch call\n */\nexport function fetchConformanceStatement(baseUrl = \"/\", requestOptions?: RequestInit): Promise<fhirclient.FHIR.CapabilityStatement>\n{\n    const url = String(baseUrl).replace(/\\/*$/, \"/\") + \"metadata\";\n    return getAndCache(url, requestOptions).catch((ex: Error) => {\n        throw new Error(\n            `Failed to fetch the conformance statement from \"${url}\". ${ex}`\n        );\n    });\n}\n\n\n/**\n * Walks through an object (or array) and returns the value found at the\n * provided path. This function is very simple so it intentionally does not\n * support any argument polymorphism, meaning that the path can only be a\n * dot-separated string. If the path is invalid returns undefined.\n * @param obj The object (or Array) to walk through\n * @param path The path (eg. \"a.b.4.c\")\n * @returns {*} Whatever is found in the path or undefined\n */\nexport function getPath(obj: Record<string, any>, path = \"\"): any {\n    path = path.trim();\n    if (!path) {\n        return obj;\n    }\n\n    let segments = path.split(\".\");\n    let result = obj;\n\n    while (result && segments.length) {\n        const key = segments.shift();\n        if (!key && Array.isArray(result)) {\n            return result.map(o => getPath(o, segments.join(\".\")));\n        } else {\n            result = result[key as string];\n        }\n    }\n\n    return result;\n}\n\n/**\n * Like getPath, but if the node is found, its value is set to @value\n * @param obj The object (or Array) to walk through\n * @param path The path (eg. \"a.b.4.c\")\n * @param value The value to set\n * @param createEmpty If true, create missing intermediate objects or arrays\n * @returns The modified object\n */\nexport function setPath(obj: Record<string, any>, path: string, value: any, createEmpty = false): Record<string, any> {\n    path.trim().split(\".\").reduce(\n        (out, key, idx, arr) => {\n            if (out && idx === arr.length - 1) {\n                out[key] = value;\n            }\n            else {\n                if (out && out[key] === undefined && createEmpty) {\n                    out[key] = arr[idx + 1].match(/^[0-9]+$/) ? [] : {};\n                }\n                return out ? out[key] : undefined;\n            }\n        },\n        obj\n    );\n    return obj;\n}\n\n/**\n * If the argument is an array returns it as is. Otherwise puts it in an array\n * (`[arg]`) and returns the result\n * @param arg The element to test and possibly convert to array\n * @category Utility\n */\nexport function makeArray<T = any>(arg: any): T[] {\n    if (Array.isArray(arg)) {\n        return arg;\n    }\n    return [arg];\n}\n\n/**\n * Given a path, converts it to absolute url based on the `baseUrl`. If baseUrl\n * is not provided, the result would be a rooted path (one that starts with `/`).\n * @param path The path to convert\n * @param baseUrl The base URL\n */\nexport function absolute(path: string, baseUrl?: string): string\n{\n    if (path.match(/^http/)) return path;\n    if (path.match(/^urn/)) return path;\n    return String(baseUrl || \"\").replace(/\\/+$/, \"\") + \"/\" + path.replace(/^\\/+/, \"\");\n}\n\n/**\n * Generates random strings. By default this returns random 8 characters long\n * alphanumeric strings.\n * @param strLength The length of the output string. Defaults to 8.\n * @param charSet A string containing all the possible characters.\n *     Defaults to all the upper and lower-case letters plus digits.\n * @category Utility\n */\nexport function randomString(\n    strLength = 8,\n    charSet = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\"\n): string\n{\n    const result = [];\n    const len = charSet.length;\n    while (strLength--) {\n        result.push(charSet.charAt(Math.floor(Math.random() * len)));\n    }\n    return result.join(\"\");\n}\n\n/**\n * Decodes a JWT token and returns it's body.\n * @param token The token to read\n * @param env An `Adapter` or any other object that has an `atob` method\n * @category Utility\n */\nexport function jwtDecode(token: string, env: fhirclient.Adapter): Record<string, any> | null\n{\n    const payload = token.split(\".\")[1];\n    return payload ? JSON.parse(env.atob(payload)) : null;\n}\n\n/**\n * Add a supplied number of seconds to the supplied Date, returning\n * an integer number of seconds since the epoch\n * @param secondsAhead How far ahead, in seconds (defaults to 120 seconds)\n * @param from Initial time (defaults to current time)\n */\nexport function getTimeInFuture(secondsAhead: number = 120, from?: Date | number): number {\n    return Math.floor(+(from || new Date()) / 1000 + secondsAhead) \n}\n\n/**\n * Given a token response, computes and returns the expiresAt timestamp.\n * Note that this should only be used immediately after an access token is\n * received, otherwise the computed timestamp will be incorrect.\n * @param tokenResponse \n * @param env \n */\nexport function getAccessTokenExpiration(tokenResponse: fhirclient.TokenResponse, env: fhirclient.Adapter): number\n{\n    const now = Math.floor(Date.now() / 1000);\n\n    // Option 1 - using the expires_in property of the token response\n    if (tokenResponse.expires_in) {\n        return now + tokenResponse.expires_in;\n    }\n\n    // Option 2 - using the exp property of JWT tokens (must not assume JWT!)\n    if (tokenResponse.access_token) {\n        let tokenBody = jwtDecode(tokenResponse.access_token, env);\n        if (tokenBody && tokenBody.exp) {\n            return tokenBody.exp;\n        }\n    }\n\n    // Option 3 - if none of the above worked set this to 5 minutes after now\n    return now + 300;\n}\n\n/**\n * Groups the observations by code. Returns a map that will look like:\n * ```js\n * const map = client.byCodes(observations, \"code\");\n * // map = {\n * //     \"55284-4\": [ observation1, observation2 ],\n * //     \"6082-2\": [ observation3 ]\n * // }\n * ```\n * @param observations Array of observations\n * @param property The name of a CodeableConcept property to group by\n */\nexport function byCode(\n    observations: fhirclient.FHIR.Observation | fhirclient.FHIR.Observation[],\n    property: string\n): fhirclient.ObservationMap\n{\n    const ret: fhirclient.ObservationMap = {};\n\n    function handleCodeableConcept(concept: fhirclient.FHIR.CodeableConcept, observation: fhirclient.FHIR.Observation) {\n        if (concept && Array.isArray(concept.coding)) {\n            concept.coding.forEach(({ code }) => {\n                if (code) {\n                    ret[code] = ret[code] || [] as fhirclient.FHIR.Observation[];\n                    ret[code].push(observation);\n                }\n            });\n        }\n    }\n\n    makeArray(observations).forEach(o => {\n        if (o.resourceType === \"Observation\" && o[property]) {\n            if (Array.isArray(o[property])) {\n                o[property].forEach((concept: fhirclient.FHIR.CodeableConcept) => handleCodeableConcept(concept, o));\n            } else {\n                handleCodeableConcept(o[property], o);\n            }\n        }\n    });\n\n    return ret;\n}\n\n/**\n * First groups the observations by code using `byCode`. Then returns a function\n * that accepts codes as arguments and will return a flat array of observations\n * having that codes. Example:\n * ```js\n * const filter = client.byCodes(observations, \"category\");\n * filter(\"laboratory\") // => [ observation1, observation2 ]\n * filter(\"vital-signs\") // => [ observation3 ]\n * filter(\"laboratory\", \"vital-signs\") // => [ observation1, observation2, observation3 ]\n * ```\n * @param observations Array of observations\n * @param property The name of a CodeableConcept property to group by\n */\nexport function byCodes(\n    observations: fhirclient.FHIR.Observation | fhirclient.FHIR.Observation[],\n    property: string\n): (...codes: string[]) => any[]\n{\n    const bank = byCode(observations, property);\n    return (...codes) => codes\n        .filter(code => (code + \"\") in bank)\n        .reduce(\n            (prev, code) => prev.concat(bank[code + \"\"]),\n            [] as fhirclient.FHIR.Observation[]\n        );\n}\n\n/**\n * Given a conformance statement and a resource type, returns the name of the\n * URL parameter that can be used to scope the resource type by patient ID.\n */\nexport function getPatientParam(conformance: fhirclient.FHIR.CapabilityStatement, resourceType: string): string\n{\n    // Find what resources are supported by this server\n    const resources = getPath(conformance, \"rest.0.resource\") || [];\n\n    // Check if this resource is supported\n    const meta = resources.find((r: any) => r.type === resourceType);\n    if (!meta) {\n        throw new Error(`Resource \"${resourceType}\" is not supported by this FHIR server`);\n    }\n\n    // Check if any search parameters are available for this resource\n    if (!Array.isArray(meta.searchParam)) {\n        throw new Error(`No search parameters supported for \"${resourceType}\" on this FHIR server`);\n    }\n\n    // This is a rare case but could happen in generic workflows\n    if (resourceType == \"Patient\" && meta.searchParam.find((x: any) => x.name == \"_id\")) {\n        return \"_id\";\n    }\n\n    // Now find the first possible parameter name\n    const out = patientParams.find(p => meta.searchParam.find((x: any) => x.name == p));\n\n    // If there is no match\n    if (!out) {\n        throw new Error(\"I don't know what param to use for \" + resourceType);\n    }\n\n    return out;\n}\n\n/**\n * Resolves a reference to target window. It may also open new window or tab if\n * the `target = \"popup\"` or `target = \"_blank\"`.\n * @param target\n * @param width Only used when `target = \"popup\"`\n * @param height Only used when `target = \"popup\"`\n */\nexport async function getTargetWindow(target: fhirclient.WindowTarget, width: number = 800, height: number = 720): Promise<Window>\n{\n    // The target can be a function that returns the target. This can be\n    // used to open a layer pop-up with an iframe and then return a reference\n    // to that iframe (or its name)\n    if (typeof target == \"function\") {\n        target = await target();\n    }\n\n    // The target can be a window reference\n    if (target && typeof target == \"object\") {\n        return target;\n    }\n\n    // At this point target must be a string\n    if (typeof target != \"string\") {\n        _debug(\"Invalid target type '%s'. Failing back to '_self'.\", typeof target);\n        return self;\n    }\n\n    // Current window\n    if (target == \"_self\") {\n        return self;\n    }\n\n    // The parent frame\n    if (target == \"_parent\") {\n        return parent;\n    }\n\n    // The top window\n    if (target == \"_top\") {\n        return top || self;\n    }\n\n    // New tab or window\n    if (target == \"_blank\") {\n        let error, targetWindow: Window | null = null;\n        try {\n            targetWindow = window.open(\"\", \"SMARTAuthPopup\");\n            if (!targetWindow) {\n                throw new Error(\"Perhaps window.open was blocked\");\n            }\n        } catch (e) {\n            error = e;\n        }\n\n        if (!targetWindow) {\n            _debug(\"Cannot open window. Failing back to '_self'. %s\", error);\n            return self;\n        } else {\n            return targetWindow;\n        }\n    }\n\n    // Popup window\n    if (target == \"popup\") {\n        let error, targetWindow: Window | null = null;\n        // if (!targetWindow || targetWindow.closed) {\n        try {\n            targetWindow = window.open(\"\", \"SMARTAuthPopup\", [\n                \"height=\" + height,\n                \"width=\" + width,\n                \"menubar=0\",\n                \"resizable=1\",\n                \"status=0\",\n                \"top=\" + (screen.height - height) / 2,\n                \"left=\" + (screen.width - width) / 2\n            ].join(\",\"));\n            if (!targetWindow) {\n                throw new Error(\"Perhaps the popup window was blocked\");\n            }\n        } catch (e) {\n            error = e;\n        }\n\n        if (!targetWindow) {\n            _debug(\"Cannot open window. Failing back to '_self'. %s\", error);\n            return self;\n        } else {\n            return targetWindow;\n        }\n    }\n\n    // Frame or window by name\n    const winOrFrame: Window = frames[target as any];\n    if (winOrFrame) {\n        return winOrFrame;\n    }\n\n    _debug(\"Unknown target '%s'. Failing back to '_self'.\", target);\n    return self;\n}\n\nexport function assert(condition: any, message: string): asserts condition {\n    if (!(condition)) {\n        throw new Error(message)\n    }\n}\n\nexport function assertJsonPatch(patch: fhirclient.JsonPatch): asserts patch {\n    assert(Array.isArray(patch), \"The JSON patch must be an array\")\n    assert(patch.length > 0, \"The JSON patch array should not be empty\")\n    patch.forEach((operation: fhirclient.JsonPatchOperation) => {\n        assert(\n            [\"add\", \"replace\", \"test\", \"move\", \"copy\", \"remove\"].indexOf(operation.op) > -1,\n            'Each patch operation must have an \"op\" property which must be one of: \"add\", \"replace\", \"test\", \"move\", \"copy\", \"remove\"'\n        )\n        assert(operation.path && typeof operation.path, `Invalid \"${operation.op}\" operation. Missing \"path\" property`)\n        \n        if (operation.op == \"add\" || operation.op == \"replace\" || operation.op == \"test\") {\n            assert(\"value\" in operation, `Invalid \"${operation.op}\" operation. Missing \"value\" property`)\n            assert(Object.keys(operation).length == 3, `Invalid \"${operation.op}\" operation. Contains unknown properties`)\n        }\n\n        else if (operation.op == \"move\" || operation.op == \"copy\") {\n            assert(typeof operation.from == \"string\", `Invalid \"${operation.op}\" operation. Requires a string \"from\" property`)\n            assert(Object.keys(operation).length == 3, `Invalid \"${operation.op}\" operation. Contains unknown properties`)\n        }\n\n        else {\n            assert(Object.keys(operation).length == 2, `Invalid \"${operation.op}\" operation. Contains unknown properties`)\n        }\n    })\n}\n", "import { encodeURL, fromUint8Array } from \"js-base64\"\nimport { fhirclient }                from \"../types\"\n\n\nconst crypto: Crypto = typeof globalThis === \"object\" && globalThis.crypto ?\n    globalThis.crypto :\n    require(\"isomorphic-webcrypto\").default;\n\nconst subtle = () => {\n    if (!crypto.subtle) {\n        if (!globalThis.isSecureContext) {\n            throw new Error(\n                \"Some of the required subtle crypto functionality is not \" +\n                \"available unless you run this app in secure context (using \" +\n                \"HTTPS or running locally). See \" +\n                \"https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts\"\n            )\n        }\n        throw new Error(\n            \"Some of the required subtle crypto functionality is not \" +\n            \"available in the current environment (no crypto.subtle)\"\n        )\n    }\n    return crypto.subtle\n}\n\n\ninterface PkcePair {\n    codeChallenge: string\n    codeVerifier: string\n}\n\nconst ALGS = {\n    ES384: {\n        name: \"ECDSA\",\n        namedCurve: \"P-384\"\n    } as EcKeyGenParams,\n    RS384: {\n        name: \"RSASSA-PKCS1-v1_5\",\n        modulusLength: 4096,\n        publicExponent: new Uint8Array([1, 0, 1]),\n        hash: {\n            name: 'SHA-384'\n        }\n    } as RsaHashedKeyGenParams\n};\n\nexport function randomBytes(count: number): Uint8Array {\n    return crypto.getRandomValues(new Uint8Array(count));\n}\n\nexport async function digestSha256(payload: string): Promise<Uint8Array> {\n    const prepared = new TextEncoder().encode(payload);\n    const hash = await subtle().digest('SHA-256', prepared);\n    return new Uint8Array(hash);\n}\n\nexport const generatePKCEChallenge = async (entropy = 96): Promise<PkcePair> => {\n    const inputBytes    = randomBytes(entropy)\n    const codeVerifier  = fromUint8Array(inputBytes, true)\n    const codeChallenge = fromUint8Array(await digestSha256(codeVerifier), true)\n    return { codeChallenge, codeVerifier }\n}\n\nexport async function importJWK(jwk: fhirclient.JWK): Promise<CryptoKey> {\n    // alg is optional in JWK but we need it here!\n    if (!jwk.alg) {\n        throw new Error('The \"alg\" property of the JWK must be set to \"ES384\" or \"RS384\"')\n    }\n\n    // Use of the \"key_ops\" member is OPTIONAL, unless the application requires its presence.\n    // https://www.rfc-editor.org/rfc/rfc7517.html#section-4.3\n    // \n    // In our case the app will only import private keys so we can assume \"sign\"\n    if (!Array.isArray(jwk.key_ops)) {\n        jwk.key_ops = [\"sign\"]\n    }\n\n    // In this case the JWK has a \"key_ops\" array and \"sign\" is not listed\n    if (!jwk.key_ops.includes(\"sign\")) {\n        throw new Error('The \"key_ops\" property of the JWK does not contain \"sign\"')\n    }\n\n    try {\n        return await subtle().importKey(\n            \"jwk\",\n            jwk,\n            ALGS[jwk.alg],\n            jwk.ext === true,\n            jwk.key_ops// || ['sign']\n        )\n    } catch (e) {\n        throw new Error(`The ${jwk.alg} is not supported by this browser: ${e}`)\n    }\n}\n\nexport async function signCompactJws(alg: keyof typeof ALGS, privateKey: CryptoKey, header: any, payload: any): Promise<string> {\n\n    const jwtHeader  = JSON.stringify({ ...header, alg });\n    const jwtPayload = JSON.stringify(payload);\n    const jwtAuthenticatedContent = `${encodeURL(jwtHeader)}.${encodeURL(jwtPayload)}`;\n\n    const signature = await subtle().sign(\n        { ...privateKey.algorithm, hash: 'SHA-384' },\n        privateKey,\n        new TextEncoder().encode(jwtAuthenticatedContent)\n    );\n\n    return `${jwtAuthenticatedContent}.${fromUint8Array(new Uint8Array(signature), true)}`\n}\n", "/**\n * Combined list of FHIR resource types accepting patient parameter in FHIR R2-R4\n */\nexport const patientCompartment = [\n    \"Account\",\n    \"AdverseEvent\",\n    \"AllergyIntolerance\",\n    \"Appointment\",\n    \"AppointmentResponse\",\n    \"AuditEvent\",\n    \"Basic\",\n    \"BodySite\",\n    \"BodyStructure\",\n    \"CarePlan\",\n    \"CareTeam\",\n    \"ChargeItem\",\n    \"Claim\",\n    \"ClaimResponse\",\n    \"ClinicalImpression\",\n    \"Communication\",\n    \"CommunicationRequest\",\n    \"Composition\",\n    \"Condition\",\n    \"Consent\",\n    \"Coverage\",\n    \"CoverageEligibilityRequest\",\n    \"CoverageEligibilityResponse\",\n    \"DetectedIssue\",\n    \"DeviceRequest\",\n    \"DeviceUseRequest\",\n    \"DeviceUseStatement\",\n    \"DiagnosticOrder\",\n    \"DiagnosticReport\",\n    \"DocumentManifest\",\n    \"DocumentReference\",\n    \"EligibilityRequest\",\n    \"Encounter\",\n    \"EnrollmentRequest\",\n    \"EpisodeOfCare\",\n    \"ExplanationOfBenefit\",\n    \"FamilyMemberHistory\",\n    \"Flag\",\n    \"Goal\",\n    \"Group\",\n    \"ImagingManifest\",\n    \"ImagingObjectSelection\",\n    \"ImagingStudy\",\n    \"Immunization\",\n    \"ImmunizationEvaluation\",\n    \"ImmunizationRecommendation\",\n    \"Invoice\",\n    \"List\",\n    \"MeasureReport\",\n    \"Media\",\n    \"MedicationAdministration\",\n    \"MedicationDispense\",\n    \"MedicationOrder\",\n    \"MedicationRequest\",\n    \"MedicationStatement\",\n    \"MolecularSequence\",\n    \"NutritionOrder\",\n    \"Observation\",\n    \"Order\",\n    \"Patient\",\n    \"Person\",\n    \"Procedure\",\n    \"ProcedureRequest\",\n    \"Provenance\",\n    \"QuestionnaireResponse\",\n    \"ReferralRequest\",\n    \"RelatedPerson\",\n    \"RequestGroup\",\n    \"ResearchSubject\",\n    \"RiskAssessment\",\n    \"Schedule\",\n    \"ServiceRequest\",\n    \"Specimen\",\n    \"SupplyDelivery\",\n    \"SupplyRequest\",\n    \"VisionPrescription\"\n];\n\n/**\n * Map of FHIR releases and their abstract version as number\n */\nexport const fhirVersions = {\n    \"0.4.0\": 2,\n    \"0.5.0\": 2,\n    \"1.0.0\": 2,\n    \"1.0.1\": 2,\n    \"1.0.2\": 2,\n    \"1.1.0\": 3,\n    \"1.4.0\": 3,\n    \"1.6.0\": 3,\n    \"1.8.0\": 3,\n    \"3.0.0\": 3,\n    \"3.0.1\": 3,\n    \"3.3.0\": 4,\n    \"3.5.0\": 4,\n    \"4.0.0\": 4,\n    \"4.0.1\": 4\n};\n\n/**\n * Combined (FHIR R2-R4) list of search parameters that can be used to scope\n * a request by patient ID.\n */\nexport const patientParams = [\n    \"patient\",\n    \"subject\",\n    \"requester\",\n    \"member\",\n    \"actor\",\n    \"beneficiary\"\n];\n\n/**\n * The name of the sessionStorage entry that contains the current key\n */\nexport const SMART_KEY = \"SMART_KEY\";\n", "/* global window */\nimport {\n    debug as _debug,\n    request,\n    getPath,\n    getTimeInFuture,\n    randomString,\n    getAndCache,\n    fetchConformanceStatement,\n    getAccessTokenExpiration,\n    getTargetWindow,\n    assert\n} from \"./lib\";\nimport Client from \"./Client\";\nimport { SMART_KEY } from \"./settings\";\nimport { fhirclient } from \"./types\";\n\nconst debug = _debug.extend(\"oauth2\");\n\nexport { SMART_KEY as KEY };\n\nfunction isBrowser() {\n    return typeof window === \"object\";\n}\n\n/**\n * Fetches the well-known json file from the given base URL.\n * Note that the result is cached in memory (until the page is reloaded in the\n * browser) because it might have to be re-used by the client\n * @param baseUrl The base URL of the FHIR server\n */\nexport function fetchWellKnownJson(baseUrl = \"/\", requestOptions?: RequestInit): Promise<fhirclient.WellKnownSmartConfiguration>\n{\n    const url = String(baseUrl).replace(/\\/*$/, \"/\") + \".well-known/smart-configuration\";\n    return getAndCache(url, requestOptions).catch((ex: Error) => {\n        throw new Error(`Failed to fetch the well-known json \"${url}\". ${ex.message}`);\n    });\n}\n\n/**\n * Fetch a \"WellKnownJson\" and extract the SMART endpoints from it\n */\nfunction getSecurityExtensionsFromWellKnownJson(baseUrl = \"/\", requestOptions?: RequestInit): Promise<fhirclient.OAuthSecurityExtensions>\n{\n    return fetchWellKnownJson(baseUrl, requestOptions).then(meta => {\n        if (!meta.authorization_endpoint || !meta.token_endpoint) {\n            throw new Error(\"Invalid wellKnownJson\");\n        }\n        return {\n            registrationUri     : meta.registration_endpoint  || \"\",\n            authorizeUri        : meta.authorization_endpoint,\n            tokenUri            : meta.token_endpoint,\n            codeChallengeMethods: meta.code_challenge_methods_supported || []\n        };\n    });\n}\n\n/**\n * Fetch a `CapabilityStatement` and extract the SMART endpoints from it\n */\nfunction getSecurityExtensionsFromConformanceStatement(baseUrl = \"/\", requestOptions?: RequestInit): Promise<fhirclient.OAuthSecurityExtensions>\n{\n    return fetchConformanceStatement(baseUrl, requestOptions).then(meta => {\n        const nsUri = \"http://fhir-registry.smarthealthit.org/StructureDefinition/oauth-uris\";\n        const extensions = ((getPath(meta || {}, \"rest.0.security.extension\") || []) as Array<fhirclient.FHIR.Extension<\"valueUri\">>)\n            .filter(e => e.url === nsUri)\n            .map(o => o.extension)[0];\n\n        const out:fhirclient.OAuthSecurityExtensions = {\n            registrationUri     : \"\",\n            authorizeUri        : \"\",\n            tokenUri            : \"\",\n            codeChallengeMethods: [],\n        };\n\n        if (extensions) {\n            extensions.forEach(ext => {\n                if (ext.url === \"register\") {\n                    out.registrationUri = ext.valueUri;\n                }\n                if (ext.url === \"authorize\") {\n                    out.authorizeUri = ext.valueUri;\n                }\n                if (ext.url === \"token\") {\n                    out.tokenUri = ext.valueUri;\n                }\n            });\n        }\n\n        return out;\n    });\n}\n\n\n/**\n * Given a FHIR server, returns an object with it's Oauth security endpoints\n * that we are interested in. This will try to find the info in both the\n * `CapabilityStatement` and the `.well-known/smart-configuration`. Whatever\n * Arrives first will be used and the other request will be aborted.\n * @param [baseUrl = \"/\"] Fhir server base URL\n */\nexport function getSecurityExtensions(baseUrl = \"/\"): Promise<fhirclient.OAuthSecurityExtensions>\n{\n    return getSecurityExtensionsFromWellKnownJson(baseUrl)\n        .catch(() => getSecurityExtensionsFromConformanceStatement(baseUrl));\n}\n\n/**\n * Starts the SMART Launch Sequence.\n * > **IMPORTANT**:\n *   `authorize()` will end up redirecting you to the authorization server.\n *    This means that you should not add anything to the returned promise chain.\n *    Any code written directly after the authorize() call might not be executed\n *    due to that redirect!\n * @param env\n * @param [params]\n */\nexport async function authorize(\n    env: fhirclient.Adapter,\n    params: fhirclient.AuthorizeParams | fhirclient.AuthorizeParams[] = {}\n): Promise<string|void>\n{\n    const url = env.getUrl();\n\n    // Multiple config for EHR launches ---------------------------------------\n    if (Array.isArray(params)) {\n        const urlISS = url.searchParams.get(\"iss\") || url.searchParams.get(\"fhirServiceUrl\");\n        if (!urlISS) {\n            throw new Error(\n                'Passing in an \"iss\" url parameter is required if authorize ' +\n                'uses multiple configurations'\n            );\n        }\n        // pick the right config\n        const cfg = params.find(x => {\n            if (x.issMatch) {\n                if (typeof x.issMatch === \"function\") {\n                    return !!x.issMatch(urlISS);\n                }\n                if (typeof x.issMatch === \"string\") {\n                    return x.issMatch === urlISS;\n                }\n                if (x.issMatch instanceof RegExp) {\n                    return x.issMatch.test(urlISS);\n                }\n            }\n            return false;\n        });\n        assert(cfg, `No configuration found matching the current \"iss\" parameter \"${urlISS}\"`);\n        return await authorize(env, cfg);\n    }\n    // ------------------------------------------------------------------------\n\n    // Obtain input\n    const {\n        clientSecret,\n        fakeTokenResponse,\n        encounterId,\n        target,\n        width,\n        height,\n        pkceMode,\n        clientPublicKeySetUrl,\n        // Two deprecated values to use as fall-back values later\n        redirect_uri,\n        client_id,\n    } = params;\n    \n    let {\n        iss,\n        launch,\n        patientId,\n        fhirServiceUrl,\n        redirectUri,\n        noRedirect,\n        scope = \"\",\n        clientId,\n        completeInTarget,\n        clientPrivateJwk,\n        stateKey\n    } = params;\n\n    const storage = env.getStorage();\n\n    // For these, a url param takes precedence over inline option\n    iss            = url.searchParams.get(\"iss\")            || iss;\n    fhirServiceUrl = url.searchParams.get(\"fhirServiceUrl\") || fhirServiceUrl;\n    launch         = url.searchParams.get(\"launch\")         || launch;\n    patientId      = url.searchParams.get(\"patientId\")      || patientId;\n    clientId       = url.searchParams.get(\"clientId\")       || clientId;\n\n    // If there's still no clientId or redirectUri, check deprecated params \n    if (!clientId) {\n        clientId = client_id;\n    }\n    if (!redirectUri) {\n        redirectUri = redirect_uri;\n    }\n\n    if (!redirectUri) {\n        redirectUri = env.relative(\".\");\n    } else if (!redirectUri.match(/^https?\\:\\/\\//)) {\n        redirectUri = env.relative(redirectUri);\n    }\n\n    const serverUrl = String(iss || fhirServiceUrl || \"\");\n\n    // Validate input\n    if (!serverUrl) {\n        throw new Error(\n            \"No server url found. It must be specified as `iss` or as \" +\n            \"`fhirServiceUrl` parameter\"\n        );\n    }\n\n    if (iss) {\n        debug(\"Making %s launch...\", launch ? \"EHR\" : \"standalone\");\n    }\n\n    // append launch scope if needed\n    if (launch && !scope.match(/launch/)) {\n        scope += \" launch\";\n    }\n\n    if (isBrowser()) {\n        const inFrame = isInFrame();\n        const inPopUp = isInPopUp();\n\n        if ((inFrame || inPopUp) && completeInTarget !== true && completeInTarget !== false) {\n            \n            // completeInTarget will default to true if authorize is called from\n            // within an iframe. This is to avoid issues when the entire app\n            // happens to be rendered in an iframe (including in some EHRs),\n            // even though that was not how the app developer's intention.\n            completeInTarget = inFrame;\n\n            // In this case we can't always make the best decision so ask devs\n            // to be explicit in their configuration.\n            console.warn(\n                'Your app is being authorized from within an iframe or popup ' +\n                'window. Please be explicit and provide a \"completeInTarget\" ' +\n                'option. Use \"true\" to complete the authorization in the '     +\n                'same window, or \"false\" to try to complete it in the parent ' +\n                'or the opener window. See http://docs.smarthealthit.org/client-js/api.html'\n            );\n        }\n    }\n\n    // If `authorize` is called, make sure we clear any previous state (in case\n    // this is a re-authorize)\n    const oldKey = await storage.get(SMART_KEY);\n    await storage.unset(oldKey);\n\n    stateKey = stateKey ?? randomString(16);\n\n    // Create initial state\n    const state: fhirclient.ClientState = {\n        clientId,\n        scope,\n        redirectUri,\n        serverUrl,\n        clientSecret,\n        clientPrivateJwk,\n        tokenResponse: {},\n        key: stateKey,\n        completeInTarget,\n        clientPublicKeySetUrl\n    };\n\n    const fullSessionStorageSupport = isBrowser() ?\n        getPath(env, \"options.fullSessionStorageSupport\") :\n        true;\n\n    if (fullSessionStorageSupport) {\n        await storage.set(SMART_KEY, stateKey);\n    }\n\n    // fakeTokenResponse to override stuff (useful in development)\n    if (fakeTokenResponse) {\n        Object.assign(state.tokenResponse!, fakeTokenResponse);\n    }\n\n    // Fixed patientId (useful in development)\n    if (patientId) {\n        Object.assign(state.tokenResponse!, { patient: patientId });\n    }\n\n    // Fixed encounterId (useful in development)\n    if (encounterId) {\n        Object.assign(state.tokenResponse!, { encounter: encounterId });\n    }\n\n    let redirectUrl = redirectUri + \"?state=\" + encodeURIComponent(stateKey);\n\n    // bypass oauth if fhirServiceUrl is used (but iss takes precedence)\n    if (fhirServiceUrl && !iss) {\n        debug(\"Making fake launch...\");\n        await storage.set(stateKey, state);\n        if (noRedirect) {\n            return redirectUrl;\n        }\n        return await env.redirect(redirectUrl);\n    }\n\n    // Get oauth endpoints and add them to the state\n    const extensions = await getSecurityExtensions(serverUrl);\n    Object.assign(state, extensions);\n    await storage.set(stateKey, state);\n\n    // If this happens to be an open server and there is no authorizeUri\n    if (!state.authorizeUri) {\n        if (noRedirect) {\n            return redirectUrl;\n        }\n        return await env.redirect(redirectUrl);\n    }\n\n    // build the redirect uri\n    const redirectParams = [\n        \"response_type=code\",\n        \"client_id=\"    + encodeURIComponent(clientId || \"\"),\n        \"scope=\"        + encodeURIComponent(scope),\n        \"redirect_uri=\" + encodeURIComponent(redirectUri),\n        \"aud=\"          + encodeURIComponent(serverUrl),\n        \"state=\"        + encodeURIComponent(stateKey)\n    ];\n\n    // also pass this in case of EHR launch\n    if (launch) {\n        redirectParams.push(\"launch=\" + encodeURIComponent(launch));\n    }\n\n    if (shouldIncludeChallenge(extensions.codeChallengeMethods.includes('S256'), pkceMode)) {\n        let codes = await env.security.generatePKCEChallenge()\n        Object.assign(state, codes);\n        await storage.set(stateKey, state);\n        redirectParams.push(\"code_challenge=\" + state.codeChallenge);// note that the challenge is ALREADY encoded properly\n        redirectParams.push(\"code_challenge_method=S256\");\n    }\n  \n    redirectUrl = state.authorizeUri + \"?\" + redirectParams.join(\"&\");\n\n    if (noRedirect) {\n        return redirectUrl;\n    }\n\n    if (target && isBrowser()) {\n        let win: Window;\n\n        win = await getTargetWindow(target, width, height);\n\n        if (win !== self) {\n            try {\n                // Also remove any old state from the target window and then\n                // transfer the current state there\n                win.sessionStorage.removeItem(oldKey);\n                win.sessionStorage.setItem(stateKey, JSON.stringify(state));\n            } catch (ex) {\n                _debug(`Failed to modify window.sessionStorage. Perhaps it is from different origin?. Failing back to \"_self\". %s`, ex);\n                win = self;\n            }\n        }\n\n        if (win !== self) {\n            try {\n                win.location.href = redirectUrl;\n                self.addEventListener(\"message\", onMessage);\n            } catch (ex) {\n                _debug(`Failed to modify window.location. Perhaps it is from different origin?. Failing back to \"_self\". %s`, ex);\n                self.location.href = redirectUrl;\n            }\n        } else {\n            self.location.href = redirectUrl;\n        }\n\n        return;\n    }\n    else {\n        return await env.redirect(redirectUrl);\n    }\n}\n\nfunction shouldIncludeChallenge(S256supported: boolean, pkceMode?: string) {\n    if (pkceMode === \"disabled\") {\n        return false;\n    }\n    if (pkceMode === \"unsafeV1\") {\n        return true;\n    }\n    if (pkceMode === \"required\") {\n        if (!S256supported) {\n            throw new Error(\"Required PKCE code challenge method (`S256`) was not found in the server's codeChallengeMethods declaration.\");\n        }\n        return true;\n    }\n    return S256supported;\n}\n\n/**\n * Checks if called within a frame. Only works in browsers!\n * If the current window has a `parent` or `top` properties that refer to\n * another window, returns true. If trying to access `top` or `parent` throws an\n * error, returns true. Otherwise returns `false`.\n */\nexport function isInFrame() {\n    try {\n        return self !== top && parent !== self;\n    } catch (e) {\n        return true;\n    }\n}\n\n/**\n * Checks if called within another window (popup or tab). Only works in browsers!\n * To consider itself called in a new window, this function verifies that:\n * 1. `self === top` (not in frame)\n * 2. `!!opener && opener !== self` The window has an opener\n * 3. `!!window.name` The window has a `name` set\n */\nexport function isInPopUp() {\n    try {\n        return self === top &&\n               !!opener &&\n               opener !== self &&\n               !!window.name;\n    } catch (e) {\n        return false;\n    }\n}\n\n/**\n * Another window can send a \"completeAuth\" message to this one, making it to\n * navigate to e.data.url\n * @param e The message event\n */\nexport function onMessage(e: MessageEvent) {\n    if (e.data.type == \"completeAuth\" && e.origin === new URL(self.location.href).origin) {\n        window.removeEventListener(\"message\", onMessage);\n        window.location.href = e.data.url;\n    }\n}\n\n/**\n * The ready function should only be called on the page that represents\n * the redirectUri. We typically land there after a redirect from the\n * authorization server, but this code will also be executed upon subsequent\n * navigation or page refresh.\n */\nexport async function ready(env: fhirclient.Adapter, options: fhirclient.ReadyOptions = {}): Promise<Client>\n{\n    const url = env.getUrl();\n    const Storage = env.getStorage();\n    const params = url.searchParams;\n\n    let key                    = params.get(\"state\") || options.stateKey;\n    const code                 = params.get(\"code\")  || options.code;\n    const authError            = params.get(\"error\");\n    const authErrorDescription = params.get(\"error_description\");\n\n    if (!key) {\n        key = await Storage.get(SMART_KEY);\n    }\n\n    // Start by checking the url for `error` and `error_description` parameters.\n    // This happens when the auth server rejects our authorization attempt. In\n    // this case it has no other way to tell us what the error was, other than\n    // appending these parameters to the redirect url.\n    // From client's point of view, this is not very reliable (because we can't\n    // know how we have landed on this page - was it a redirect or was it loaded\n    // manually). However, if `ready()` is being called, we can assume\n    // that the url comes from the auth server (otherwise the app won't work\n    // anyway).\n    if (authError || authErrorDescription) {\n        throw new Error([\n            authError,\n            authErrorDescription\n        ].filter(Boolean).join(\": \"));\n    }\n\n    debug(\"key: %s, code: %s\", key, code);\n\n    // key might be coming from the page url so it might be empty or missing\n    assert(key, \"No 'state' parameter found. Please (re)launch the app.\");\n\n    // Check if we have a previous state\n    let state = (await Storage.get(key)) as fhirclient.ClientState;\n\n    const fullSessionStorageSupport = isBrowser() ?\n        getPath(env, \"options.fullSessionStorageSupport\") :\n        true;\n\n    // If we are in a popup window or an iframe and the authorization is\n    // complete, send the location back to our opener and exit.\n    if (isBrowser() && state && !state.completeInTarget) {\n\n        const inFrame = isInFrame();\n        const inPopUp = isInPopUp();\n\n        // we are about to return to the opener/parent where completeAuth will\n        // be called again. In rare cases the opener or parent might also be\n        // a frame or popup. Then inFrame or inPopUp will be true but we still\n        // have to stop going up the chain. To guard against that weird form of\n        // recursion we pass one additional parameter to the url which we later\n        // remove.\n        if ((inFrame || inPopUp) && !url.searchParams.get(\"complete\")) {\n            url.searchParams.set(\"complete\", \"1\");\n            const { href, origin } = url;\n            if (inFrame) {\n                parent.postMessage({ type: \"completeAuth\", url: href }, origin);\n            }\n            if (inPopUp) {\n                opener.postMessage({ type: \"completeAuth\", url: href }, origin);\n                window.close();\n            }\n\n            return new Promise(() => { /* leave it pending!!! */ });\n        }\n    }\n\n    url.searchParams.delete(\"complete\");\n\n    // Do we have to remove the `code` and `state` params from the URL?\n    const hasState = params.has(\"state\") || options.stateKey ? true : false;\n\n    if (isBrowser() && getPath(env, \"options.replaceBrowserHistory\") && (code || hasState)) {\n        // `code` is the flag that tell us to request an access token.\n        // We have to remove it, otherwise the page will authorize on\n        // every load!\n        if (code) {\n            params.delete(\"code\");\n            debug(\"Removed code parameter from the url.\");\n        }\n\n        // If we have `fullSessionStorageSupport` it means we no longer\n        // need the `state` key. It will be stored to a well know\n        // location - sessionStorage[SMART_KEY]. However, no\n        // fullSessionStorageSupport means that this \"well know location\"\n        // might be shared between windows and tabs. In this case we\n        // MUST keep the `state` url parameter.\n        if (hasState && fullSessionStorageSupport) {\n            params.delete(\"state\");\n            debug(\"Removed state parameter from the url.\");\n        }\n\n        // If the browser does not support the replaceState method for the\n        // History Web API, the \"code\" parameter cannot be removed. As a\n        // consequence, the page will (re)authorize on every load. The\n        // workaround is to reload the page to new location without those\n        // parameters. If that is not acceptable replaceBrowserHistory\n        // should be set to false.\n        if (window.history.replaceState) {\n            window.history.replaceState({}, \"\", url.href);\n        }\n    }\n\n    // If the state does not exist, it means the page has been loaded directly.\n    assert(state, \"No state found! Please (re)launch the app.\");\n\n    // Assume the client has already completed a token exchange when\n    // there is no code (but we have a state) or access token is found in state\n    const authorized = !code || state.tokenResponse?.access_token;\n\n    // If we are authorized already, then this is just a reload.\n    // Otherwise, we have to complete the code flow\n    if (!authorized && state.tokenUri) {\n\n        assert(code, \"'code' url parameter is required\");\n\n        debug(\"Preparing to exchange the code for access token...\");\n        const requestOptions = await buildTokenRequest(env, {\n            code,\n            state,\n            clientPublicKeySetUrl: options.clientPublicKeySetUrl,\n            privateKey: options.privateKey || state.clientPrivateJwk\n        });\n        debug(\"Token request options: %O\", requestOptions);\n\n        // The EHR authorization server SHALL return a JSON structure that\n        // includes an access token or a message indicating that the\n        // authorization request has been denied.\n        const tokenResponse = await request<fhirclient.TokenResponse>(state.tokenUri, requestOptions);\n        debug(\"Token response: %O\", tokenResponse);\n        assert(tokenResponse.access_token, \"Failed to obtain access token.\");\n\n        // Now we need to determine when is this authorization going to expire\n        state.expiresAt = getAccessTokenExpiration(tokenResponse, env);\n\n        // save the tokenResponse so that we don't have to re-authorize on\n        // every page reload\n        state = { ...state, tokenResponse };\n        await Storage.set(key, state);\n        debug(\"Authorization successful!\");\n    }\n    else {\n        debug(state.tokenResponse?.access_token ?\n            \"Already authorized\" :\n            \"No authorization needed\"\n        );\n    }\n\n    if (fullSessionStorageSupport) {\n        await Storage.set(SMART_KEY, key);\n    }\n\n    const client = new Client(env, state);\n    debug(\"Created client instance: %O\", client);\n    return client;\n}\n\n/**\n * Builds the token request options. Does not make the request, just\n * creates it's configuration and returns it in a Promise.\n */\nexport async function buildTokenRequest(\n    env: fhirclient.Adapter,\n    {\n        code,\n        state,\n        clientPublicKeySetUrl,\n        privateKey\n    }: {\n        /**\n         * The `code` URL parameter received from the auth redirect\n         */\n        code: string,\n        \n        /**\n         * The app state\n         */\n        state: fhirclient.ClientState\n\n        /**\n         * If provided overrides the `clientPublicKeySetUrl` from the authorize\n         * options (if any). Used for `jku` token header in case of asymmetric auth.\n         */\n        clientPublicKeySetUrl?: string\n\n        /**\n         * Can be a private JWK, or an object with alg, kid and key properties,\n         * where `key` is an un-extractable private CryptoKey object.\n         */\n        privateKey?: fhirclient.JWK | {\n            key: CryptoKey\n            alg: \"RS384\" | \"ES384\"\n            kid: string\n        }\n    }\n): Promise<RequestInit>\n{\n    const { redirectUri, clientSecret, tokenUri, clientId, codeVerifier } = state;\n\n    assert(redirectUri, \"Missing state.redirectUri\");\n    assert(tokenUri, \"Missing state.tokenUri\");\n    assert(clientId, \"Missing state.clientId\");\n\n    const requestOptions: Record<string, any> = {\n        method: \"POST\",\n        headers: { \"content-type\": \"application/x-www-form-urlencoded\" },\n        body: `code=${code}&grant_type=authorization_code&redirect_uri=${\n            encodeURIComponent(redirectUri)}`\n    };\n\n    // For public apps, authentication is not possible (and thus not required),\n    // since a client with no secret cannot prove its identity when it issues a\n    // call. (The end-to-end system can still be secure because the client comes\n    // from a known, https protected endpoint specified and enforced by the\n    // redirect uri.) For confidential apps, an Authorization header using HTTP\n    // Basic authentication is required, where the username is the app’s\n    // client_id and the password is the app’s client_secret (see example).\n    if (clientSecret) {\n        requestOptions.headers.authorization = \"Basic \" + env.btoa(\n            clientId + \":\" + clientSecret\n        );\n        debug(\n            \"Using state.clientSecret to construct the authorization header: %s\",\n            requestOptions.headers.authorization\n        );\n    }\n    \n    // Asymmetric auth\n    else if (privateKey) {\n\n        const pk = \"key\" in privateKey ?\n            privateKey.key as CryptoKey:\n            await env.security.importJWK(privateKey as fhirclient.JWK)\n\n        const jwtHeaders = {\n            typ: \"JWT\",\n            kid: privateKey.kid,\n            jku: clientPublicKeySetUrl || state.clientPublicKeySetUrl\n        };\n\n        const jwtClaims = {\n            iss: clientId,\n            sub: clientId,\n            aud: tokenUri,\n            jti: env.base64urlencode(env.security.randomBytes(32)),\n            exp: getTimeInFuture(120) // two minutes in the future\n        };\n        \n        const clientAssertion = await env.security.signCompactJws(privateKey.alg, pk, jwtHeaders, jwtClaims);\n        requestOptions.body += `&client_assertion_type=${encodeURIComponent(\"urn:ietf:params:oauth:client-assertion-type:jwt-bearer\")}`;\n        requestOptions.body += `&client_assertion=${encodeURIComponent(clientAssertion)}`;\n        debug(\"Using state.clientPrivateJwk to add a client_assertion to the POST body\")\n    }\n    \n    // Public client\n    else {\n        debug(\"Public client detected; adding state.clientId to the POST body\");\n        requestOptions.body += `&client_id=${encodeURIComponent(clientId)}`;\n    }\n\n    if (codeVerifier) {\n      debug(\"Found state.codeVerifier, adding to the POST body\")\n      // Note that the codeVerifier is ALREADY encoded properly  \n      requestOptions.body += \"&code_verifier=\" + codeVerifier;\n    }\n  \n    return requestOptions as RequestInit;\n}\n\n/**\n * This function can be used when you want to handle everything in one page\n * (no launch endpoint needed). You can think of it as if it does:\n * ```js\n * authorize(options).then(ready)\n * ```\n *\n * **Be careful with init()!** There are some details you need to be aware of:\n *\n * 1. It will only work if your launch_uri is the same as your redirect_uri.\n *    While this should be valid, we can’t promise that every EHR will allow you\n *    to register client with such settings.\n * 2. Internally, `init()` will be called twice. First it will redirect to the\n *    EHR, then the EHR will redirect back to the page where init() will be\n *    called again to complete the authorization. This is generally fine,\n *    because the returned promise will only be resolved once, after the second\n *    execution, but please also consider the following:\n *    - You should wrap all your app’s code in a function that is only executed\n *      after `init()` resolves!\n *    - Since the page will be loaded twice, you must be careful if your code\n *      has global side effects that can persist between page reloads\n *      (for example writing to localStorage).\n * 3. For standalone launch, only use init in combination with offline_access\n *    scope. Once the access_token expires, if you don’t have a refresh_token\n *    there is no way to re-authorize properly. We detect that and delete the\n *    expired access token, but it still means that the user will have to\n *    refresh the page twice to re-authorize.\n * @param env The adapter\n * @param authorizeOptions The authorize options\n */\nexport async function init(\n    env: fhirclient.Adapter,\n    authorizeOptions: fhirclient.AuthorizeParams,\n    readyOptions?: fhirclient.ReadyOptions\n): Promise<Client|never>\n{\n    const url   = env.getUrl();\n    const code  = url.searchParams.get(\"code\");\n    const state = url.searchParams.get(\"state\");\n\n    // if `code` and `state` params are present we need to complete the auth flow\n    if (code && state) {\n        return ready(env, readyOptions);\n    }\n\n    // Check for existing client state. If state is found, it means a client\n    // instance have already been created in this session and we should try to\n    // \"revive\" it.\n    const storage = env.getStorage();\n    const key     = state || await storage.get(SMART_KEY);\n    const cached  = await storage.get(key);\n    if (cached) {\n        return new Client(env, cached);\n    }\n\n    // Otherwise try to launch\n    return authorize(env, authorizeOptions).then(() => {\n        // `init` promises a Client but that cannot happen in this case. The\n        // browser will be redirected (unload the page and be redirected back\n        // to it later and the same init function will be called again). On\n        // success, authorize will resolve with the redirect url but we don't\n        // want to return that from this promise chain because it is not a\n        // Client instance. At the same time, if authorize fails, we do want to\n        // pass the error to those waiting for a client instance.\n        return new Promise(() => { /* leave it pending!!! */ });\n    });\n}\n", "export default class Storage\n{\n    /**\n     * Gets the value at `key`. Returns a promise that will be resolved\n     * with that value (or undefined for missing keys).\n     */\n    async get(key: string): Promise<any>\n    {\n        const value = sessionStorage[key];\n        if (value) {\n            return JSON.parse(value);\n        }\n        return null;\n    }\n\n    /**\n     * Sets the `value` on `key` and returns a promise that will be resolved\n     * with the value that was set.\n     */\n    async set(key: string, value: any): Promise<any>\n    {\n        sessionStorage[key] = JSON.stringify(value);\n        return value;\n    }\n\n    /**\n     * Deletes the value at `key`. Returns a promise that will be resolved\n     * with true if the key was deleted or with false if it was not (eg. if\n     * did not exist).\n     */\n    async unset(key: string): Promise<boolean>\n    {\n        if (key in sessionStorage) {\n            delete sessionStorage[key];\n            return true;\n        }\n        return false;\n    }\n\n}\n", "// This map contains reusable debug messages (only those used in multiple places)\nexport default {\n    expired      : \"Session expired! Please re-launch the app\",\n    noScopeForId : \"Trying to get the ID of the selected %s. Please add 'launch' or 'launch/%s' to the requested scopes and try again.\",\n    noIfNoAuth   : \"You are trying to get %s but the app is not authorized yet.\",\n    noFreeContext: \"Please don't use open fhir servers if you need to access launch context items like the %S.\"\n};\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tlet i;\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t\tconst len = split.length;\n\n\t\tfor (i = 0; i < len; i++) {\n\t\t\tif (!split[i]) {\n\t\t\t\t// ignore empty strings\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tnamespaces = split[i].replace(/\\*/g, '.*?');\n\n\t\t\tif (namespaces[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(new RegExp('^' + namespaces.slice(1) + '$'));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(new RegExp('^' + namespaces + '$'));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names.map(toNamespace),\n\t\t\t...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tif (name[name.length - 1] === '*') {\n\t\t\treturn true;\n\t\t}\n\n\t\tlet i;\n\t\tlet len;\n\n\t\tfor (i = 0, len = createDebug.skips.length; i < len; i++) {\n\t\t\tif (createDebug.skips[i].test(name)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0, len = createDebug.names.length; i < len; i++) {\n\t\t\tif (createDebug.names[i].test(name)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/\n\tfunction toNamespace(regexp) {\n\t\treturn regexp.toString()\n\t\t\t.substring(2, regexp.toString().length - 2)\n\t\t\t.replace(/\\.\\*\\?$/, '*');\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "//\n// THIS FILE IS AUTOMATICALLY GENERATED! DO NOT EDIT BY HAND!\n//\n;\n(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined'\n        ? module.exports = factory()\n        : typeof define === 'function' && define.amd\n            ? define(factory) :\n            // cf. https://github.com/dankogai/js-base64/issues/119\n            (function () {\n                // existing version for noConflict()\n                var _Base64 = global.Base64;\n                var gBase64 = factory();\n                gBase64.noConflict = function () {\n                    global.Base64 = _Base64;\n                    return gBase64;\n                };\n                if (global.Meteor) { // Meteor.js\n                    Base64 = gBase64;\n                }\n                global.Base64 = gBase64;\n            })();\n}((typeof self !== 'undefined' ? self\n    : typeof window !== 'undefined' ? window\n        : typeof global !== 'undefined' ? global\n            : this), function () {\n    'use strict';\n    /**\n     *  base64.ts\n     *\n     *  Licensed under the BSD 3-Clause License.\n     *    http://opensource.org/licenses/BSD-3-Clause\n     *\n     *  References:\n     *    http://en.wikipedia.org/wiki/Base64\n     *\n     * <AUTHOR> (https://github.com/dankogai)\n     */\n    var version = '3.7.7';\n    /**\n     * @deprecated use lowercase `version`.\n     */\n    var VERSION = version;\n    var _hasBuffer = typeof Buffer === 'function';\n    var _TD = typeof TextDecoder === 'function' ? new TextDecoder() : undefined;\n    var _TE = typeof TextEncoder === 'function' ? new TextEncoder() : undefined;\n    var b64ch = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n    var b64chs = Array.prototype.slice.call(b64ch);\n    var b64tab = (function (a) {\n        var tab = {};\n        a.forEach(function (c, i) { return tab[c] = i; });\n        return tab;\n    })(b64chs);\n    var b64re = /^(?:[A-Za-z\\d+\\/]{4})*?(?:[A-Za-z\\d+\\/]{2}(?:==)?|[A-Za-z\\d+\\/]{3}=?)?$/;\n    var _fromCC = String.fromCharCode.bind(String);\n    var _U8Afrom = typeof Uint8Array.from === 'function'\n        ? Uint8Array.from.bind(Uint8Array)\n        : function (it) { return new Uint8Array(Array.prototype.slice.call(it, 0)); };\n    var _mkUriSafe = function (src) { return src\n        .replace(/=/g, '').replace(/[+\\/]/g, function (m0) { return m0 == '+' ? '-' : '_'; }); };\n    var _tidyB64 = function (s) { return s.replace(/[^A-Za-z0-9\\+\\/]/g, ''); };\n    /**\n     * polyfill version of `btoa`\n     */\n    var btoaPolyfill = function (bin) {\n        // console.log('polyfilled');\n        var u32, c0, c1, c2, asc = '';\n        var pad = bin.length % 3;\n        for (var i = 0; i < bin.length;) {\n            if ((c0 = bin.charCodeAt(i++)) > 255 ||\n                (c1 = bin.charCodeAt(i++)) > 255 ||\n                (c2 = bin.charCodeAt(i++)) > 255)\n                throw new TypeError('invalid character found');\n            u32 = (c0 << 16) | (c1 << 8) | c2;\n            asc += b64chs[u32 >> 18 & 63]\n                + b64chs[u32 >> 12 & 63]\n                + b64chs[u32 >> 6 & 63]\n                + b64chs[u32 & 63];\n        }\n        return pad ? asc.slice(0, pad - 3) + \"===\".substring(pad) : asc;\n    };\n    /**\n     * does what `window.btoa` of web browsers do.\n     * @param {String} bin binary string\n     * @returns {string} Base64-encoded string\n     */\n    var _btoa = typeof btoa === 'function' ? function (bin) { return btoa(bin); }\n        : _hasBuffer ? function (bin) { return Buffer.from(bin, 'binary').toString('base64'); }\n            : btoaPolyfill;\n    var _fromUint8Array = _hasBuffer\n        ? function (u8a) { return Buffer.from(u8a).toString('base64'); }\n        : function (u8a) {\n            // cf. https://stackoverflow.com/questions/12710001/how-to-convert-uint8-array-to-base64-encoded-string/12713326#12713326\n            var maxargs = 0x1000;\n            var strs = [];\n            for (var i = 0, l = u8a.length; i < l; i += maxargs) {\n                strs.push(_fromCC.apply(null, u8a.subarray(i, i + maxargs)));\n            }\n            return _btoa(strs.join(''));\n        };\n    /**\n     * converts a Uint8Array to a Base64 string.\n     * @param {boolean} [urlsafe] URL-and-filename-safe a la RFC4648 §5\n     * @returns {string} Base64 string\n     */\n    var fromUint8Array = function (u8a, urlsafe) {\n        if (urlsafe === void 0) { urlsafe = false; }\n        return urlsafe ? _mkUriSafe(_fromUint8Array(u8a)) : _fromUint8Array(u8a);\n    };\n    // This trick is found broken https://github.com/dankogai/js-base64/issues/130\n    // const utob = (src: string) => unescape(encodeURIComponent(src));\n    // reverting good old fationed regexp\n    var cb_utob = function (c) {\n        if (c.length < 2) {\n            var cc = c.charCodeAt(0);\n            return cc < 0x80 ? c\n                : cc < 0x800 ? (_fromCC(0xc0 | (cc >>> 6))\n                    + _fromCC(0x80 | (cc & 0x3f)))\n                    : (_fromCC(0xe0 | ((cc >>> 12) & 0x0f))\n                        + _fromCC(0x80 | ((cc >>> 6) & 0x3f))\n                        + _fromCC(0x80 | (cc & 0x3f)));\n        }\n        else {\n            var cc = 0x10000\n                + (c.charCodeAt(0) - 0xD800) * 0x400\n                + (c.charCodeAt(1) - 0xDC00);\n            return (_fromCC(0xf0 | ((cc >>> 18) & 0x07))\n                + _fromCC(0x80 | ((cc >>> 12) & 0x3f))\n                + _fromCC(0x80 | ((cc >>> 6) & 0x3f))\n                + _fromCC(0x80 | (cc & 0x3f)));\n        }\n    };\n    var re_utob = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFFF]|[^\\x00-\\x7F]/g;\n    /**\n     * @deprecated should have been internal use only.\n     * @param {string} src UTF-8 string\n     * @returns {string} UTF-16 string\n     */\n    var utob = function (u) { return u.replace(re_utob, cb_utob); };\n    //\n    var _encode = _hasBuffer\n        ? function (s) { return Buffer.from(s, 'utf8').toString('base64'); }\n        : _TE\n            ? function (s) { return _fromUint8Array(_TE.encode(s)); }\n            : function (s) { return _btoa(utob(s)); };\n    /**\n     * converts a UTF-8-encoded string to a Base64 string.\n     * @param {boolean} [urlsafe] if `true` make the result URL-safe\n     * @returns {string} Base64 string\n     */\n    var encode = function (src, urlsafe) {\n        if (urlsafe === void 0) { urlsafe = false; }\n        return urlsafe\n            ? _mkUriSafe(_encode(src))\n            : _encode(src);\n    };\n    /**\n     * converts a UTF-8-encoded string to URL-safe Base64 RFC4648 §5.\n     * @returns {string} Base64 string\n     */\n    var encodeURI = function (src) { return encode(src, true); };\n    // This trick is found broken https://github.com/dankogai/js-base64/issues/130\n    // const btou = (src: string) => decodeURIComponent(escape(src));\n    // reverting good old fationed regexp\n    var re_btou = /[\\xC0-\\xDF][\\x80-\\xBF]|[\\xE0-\\xEF][\\x80-\\xBF]{2}|[\\xF0-\\xF7][\\x80-\\xBF]{3}/g;\n    var cb_btou = function (cccc) {\n        switch (cccc.length) {\n            case 4:\n                var cp = ((0x07 & cccc.charCodeAt(0)) << 18)\n                    | ((0x3f & cccc.charCodeAt(1)) << 12)\n                    | ((0x3f & cccc.charCodeAt(2)) << 6)\n                    | (0x3f & cccc.charCodeAt(3)), offset = cp - 0x10000;\n                return (_fromCC((offset >>> 10) + 0xD800)\n                    + _fromCC((offset & 0x3FF) + 0xDC00));\n            case 3:\n                return _fromCC(((0x0f & cccc.charCodeAt(0)) << 12)\n                    | ((0x3f & cccc.charCodeAt(1)) << 6)\n                    | (0x3f & cccc.charCodeAt(2)));\n            default:\n                return _fromCC(((0x1f & cccc.charCodeAt(0)) << 6)\n                    | (0x3f & cccc.charCodeAt(1)));\n        }\n    };\n    /**\n     * @deprecated should have been internal use only.\n     * @param {string} src UTF-16 string\n     * @returns {string} UTF-8 string\n     */\n    var btou = function (b) { return b.replace(re_btou, cb_btou); };\n    /**\n     * polyfill version of `atob`\n     */\n    var atobPolyfill = function (asc) {\n        // console.log('polyfilled');\n        asc = asc.replace(/\\s+/g, '');\n        if (!b64re.test(asc))\n            throw new TypeError('malformed base64.');\n        asc += '=='.slice(2 - (asc.length & 3));\n        var u24, bin = '', r1, r2;\n        for (var i = 0; i < asc.length;) {\n            u24 = b64tab[asc.charAt(i++)] << 18\n                | b64tab[asc.charAt(i++)] << 12\n                | (r1 = b64tab[asc.charAt(i++)]) << 6\n                | (r2 = b64tab[asc.charAt(i++)]);\n            bin += r1 === 64 ? _fromCC(u24 >> 16 & 255)\n                : r2 === 64 ? _fromCC(u24 >> 16 & 255, u24 >> 8 & 255)\n                    : _fromCC(u24 >> 16 & 255, u24 >> 8 & 255, u24 & 255);\n        }\n        return bin;\n    };\n    /**\n     * does what `window.atob` of web browsers do.\n     * @param {String} asc Base64-encoded string\n     * @returns {string} binary string\n     */\n    var _atob = typeof atob === 'function' ? function (asc) { return atob(_tidyB64(asc)); }\n        : _hasBuffer ? function (asc) { return Buffer.from(asc, 'base64').toString('binary'); }\n            : atobPolyfill;\n    //\n    var _toUint8Array = _hasBuffer\n        ? function (a) { return _U8Afrom(Buffer.from(a, 'base64')); }\n        : function (a) { return _U8Afrom(_atob(a).split('').map(function (c) { return c.charCodeAt(0); })); };\n    /**\n     * converts a Base64 string to a Uint8Array.\n     */\n    var toUint8Array = function (a) { return _toUint8Array(_unURI(a)); };\n    //\n    var _decode = _hasBuffer\n        ? function (a) { return Buffer.from(a, 'base64').toString('utf8'); }\n        : _TD\n            ? function (a) { return _TD.decode(_toUint8Array(a)); }\n            : function (a) { return btou(_atob(a)); };\n    var _unURI = function (a) { return _tidyB64(a.replace(/[-_]/g, function (m0) { return m0 == '-' ? '+' : '/'; })); };\n    /**\n     * converts a Base64 string to a UTF-8 string.\n     * @param {String} src Base64 string.  Both normal and URL-safe are supported\n     * @returns {string} UTF-8 string\n     */\n    var decode = function (src) { return _decode(_unURI(src)); };\n    /**\n     * check if a value is a valid Base64 string\n     * @param {String} src a value to check\n      */\n    var isValid = function (src) {\n        if (typeof src !== 'string')\n            return false;\n        var s = src.replace(/\\s+/g, '').replace(/={0,2}$/, '');\n        return !/[^\\s0-9a-zA-Z\\+/]/.test(s) || !/[^\\s0-9a-zA-Z\\-_]/.test(s);\n    };\n    //\n    var _noEnum = function (v) {\n        return {\n            value: v, enumerable: false, writable: true, configurable: true\n        };\n    };\n    /**\n     * extend String.prototype with relevant methods\n     */\n    var extendString = function () {\n        var _add = function (name, body) { return Object.defineProperty(String.prototype, name, _noEnum(body)); };\n        _add('fromBase64', function () { return decode(this); });\n        _add('toBase64', function (urlsafe) { return encode(this, urlsafe); });\n        _add('toBase64URI', function () { return encode(this, true); });\n        _add('toBase64URL', function () { return encode(this, true); });\n        _add('toUint8Array', function () { return toUint8Array(this); });\n    };\n    /**\n     * extend Uint8Array.prototype with relevant methods\n     */\n    var extendUint8Array = function () {\n        var _add = function (name, body) { return Object.defineProperty(Uint8Array.prototype, name, _noEnum(body)); };\n        _add('toBase64', function (urlsafe) { return fromUint8Array(this, urlsafe); });\n        _add('toBase64URI', function () { return fromUint8Array(this, true); });\n        _add('toBase64URL', function () { return fromUint8Array(this, true); });\n    };\n    /**\n     * extend Builtin prototypes with relevant methods\n     */\n    var extendBuiltins = function () {\n        extendString();\n        extendUint8Array();\n    };\n    var gBase64 = {\n        version: version,\n        VERSION: VERSION,\n        atob: _atob,\n        atobPolyfill: atobPolyfill,\n        btoa: _btoa,\n        btoaPolyfill: btoaPolyfill,\n        fromBase64: decode,\n        toBase64: encode,\n        encode: encode,\n        encodeURI: encodeURI,\n        encodeURL: encodeURI,\n        utob: utob,\n        btou: btou,\n        decode: decode,\n        isValid: isValid,\n        fromUint8Array: fromUint8Array,\n        toUint8Array: toUint8Array,\n        extendString: extendString,\n        extendUint8Array: extendUint8Array,\n        extendBuiltins: extendBuiltins\n    };\n    //\n    // export Base64 to the namespace\n    //\n    // ES5 is yet to have Object.assign() that may make transpilers unhappy.\n    // gBase64.Base64 = Object.assign({}, gBase64);\n    gBase64.Base64 = {};\n    Object.keys(gBase64).forEach(function (k) { return gBase64.Base64[k] = gBase64[k]; });\n    return gBase64;\n}));\n", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "import './webcrypto-shim.mjs'\nexport default window.crypto\n", "/**\n * @file Web Cryptography API shim\n * <AUTHOR> <v<PERSON><PERSON><PERSON>@gmail.com>\n * @license MIT\n */\n(function (global, factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define([], function () {\n            return factory(global);\n        });\n    } else if (typeof module === 'object' && module.exports) {\n        // CommonJS-like environments that support module.exports\n        module.exports = factory(global);\n    } else {\n        factory(global);\n    }\n}(typeof self !== 'undefined' ? self : this, function (global) {\n    'use strict';\n\n    if ( typeof Promise !== 'function' )\n        throw \"Promise support required\";\n\n    var _crypto = global.crypto || global.msCrypto;\n    if ( !_crypto ) return;\n\n    var _subtle = _crypto.subtle || _crypto.webkitSubtle;\n    if ( !_subtle ) return;\n\n    var _Crypto     = global.Crypto || _crypto.constructor || Object,\n        _SubtleCrypto = global.SubtleCrypto || _subtle.constructor || Object,\n        _CryptoKey  = global.CryptoKey || global.Key || Object;\n\n    var isEdge = global.navigator.userAgent.indexOf('Edge/') > -1;\n    var isIE    = !!global.msCrypto && !isEdge;\n    var isWebkit = !_crypto.subtle && !!_crypto.webkitSubtle;\n    if ( !isIE && !isWebkit ) return;\n\n    function s2a ( s ) {\n        return btoa(s).replace(/\\=+$/, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n    }\n\n    function a2s ( s ) {\n        s += '===', s = s.slice( 0, -s.length % 4 );\n        return atob( s.replace(/-/g, '+').replace(/_/g, '/') );\n    }\n\n    function s2b ( s ) {\n        var b = new Uint8Array(s.length);\n        for ( var i = 0; i < s.length; i++ ) b[i] = s.charCodeAt(i);\n        return b;\n    }\n\n    function b2s ( b ) {\n        if ( b instanceof ArrayBuffer ) b = new Uint8Array(b);\n        return String.fromCharCode.apply( String, b );\n    }\n\n    function alg ( a ) {\n        var r = { 'name': (a.name || a || '').toUpperCase().replace('V','v') };\n        switch ( r.name ) {\n            case 'SHA-1':\n            case 'SHA-256':\n            case 'SHA-384':\n            case 'SHA-512':\n                break;\n            case 'AES-CBC':\n            case 'AES-GCM':\n            case 'AES-KW':\n                if ( a.length ) r['length'] = a.length;\n                break;\n            case 'HMAC':\n                if ( a.hash ) r['hash'] = alg(a.hash);\n                if ( a.length ) r['length'] = a.length;\n                break;\n            case 'RSAES-PKCS1-v1_5':\n                if ( a.publicExponent ) r['publicExponent'] = new Uint8Array(a.publicExponent);\n                if ( a.modulusLength ) r['modulusLength'] = a.modulusLength;\n                break;\n            case 'RSASSA-PKCS1-v1_5':\n            case 'RSA-OAEP':\n                if ( a.hash ) r['hash'] = alg(a.hash);\n                if ( a.publicExponent ) r['publicExponent'] = new Uint8Array(a.publicExponent);\n                if ( a.modulusLength ) r['modulusLength'] = a.modulusLength;\n                break;\n            default:\n                throw new SyntaxError(\"Bad algorithm name\");\n        }\n        return r;\n    };\n\n    function jwkAlg ( a ) {\n        return {\n            'HMAC': {\n                'SHA-1': 'HS1',\n                'SHA-256': 'HS256',\n                'SHA-384': 'HS384',\n                'SHA-512': 'HS512',\n            },\n            'RSASSA-PKCS1-v1_5': {\n                'SHA-1': 'RS1',\n                'SHA-256': 'RS256',\n                'SHA-384': 'RS384',\n                'SHA-512': 'RS512',\n            },\n            'RSAES-PKCS1-v1_5': {\n                '': 'RSA1_5',\n            },\n            'RSA-OAEP': {\n                'SHA-1': 'RSA-OAEP',\n                'SHA-256': 'RSA-OAEP-256',\n            },\n            'AES-KW': {\n                '128': 'A128KW',\n                '192': 'A192KW',\n                '256': 'A256KW',\n            },\n            'AES-GCM': {\n                '128': 'A128GCM',\n                '192': 'A192GCM',\n                '256': 'A256GCM',\n            },\n            'AES-CBC': {\n                '128': 'A128CBC',\n                '192': 'A192CBC',\n                '256': 'A256CBC',\n            },\n        }[a.name][ ( a.hash || {} ).name || a.length || '' ];\n    }\n\n    function b2jwk ( k ) {\n        if ( k instanceof ArrayBuffer || k instanceof Uint8Array ) k = JSON.parse( decodeURIComponent( escape( b2s(k) ) ) );\n        var jwk = { 'kty': k.kty, 'alg': k.alg, 'ext': k.ext || k.extractable };\n        switch ( jwk.kty ) {\n            case 'oct':\n                jwk.k = k.k;\n            case 'RSA':\n                [ 'n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qi', 'oth' ].forEach( function ( x ) { if ( x in k ) jwk[x] = k[x] } );\n                break;\n            default:\n                throw new TypeError(\"Unsupported key type\");\n        }\n        return jwk;\n    }\n\n    function jwk2b ( k ) {\n        var jwk = b2jwk(k);\n        if ( isIE ) jwk['extractable'] = jwk.ext, delete jwk.ext;\n        return s2b( unescape( encodeURIComponent( JSON.stringify(jwk) ) ) ).buffer;\n    }\n\n    function pkcs2jwk ( k ) {\n        var info = b2der(k), prv = false;\n        if ( info.length > 2 ) prv = true, info.shift(); // remove version from PKCS#8 PrivateKeyInfo structure\n        var jwk = { 'ext': true };\n        switch ( info[0][0] ) {\n            case '1.2.840.113549.1.1.1':\n                var rsaComp = [ 'n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qi' ],\n                    rsaKey  = b2der( info[1] );\n                if ( prv ) rsaKey.shift(); // remove version from PKCS#1 RSAPrivateKey structure\n                for ( var i = 0; i < rsaKey.length; i++ ) {\n                    if ( !rsaKey[i][0] ) rsaKey[i] = rsaKey[i].subarray(1);\n                    jwk[ rsaComp[i] ] = s2a( b2s( rsaKey[i] ) );\n                }\n                jwk['kty'] = 'RSA';\n                break;\n            default:\n                throw new TypeError(\"Unsupported key type\");\n        }\n        return jwk;\n    }\n\n    function jwk2pkcs ( k ) {\n        var key, info = [ [ '', null ] ], prv = false;\n        switch ( k.kty ) {\n            case 'RSA':\n                var rsaComp = [ 'n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qi' ],\n                    rsaKey = [];\n                for ( var i = 0; i < rsaComp.length; i++ ) {\n                    if ( !( rsaComp[i] in k ) ) break;\n                    var b = rsaKey[i] = s2b( a2s( k[ rsaComp[i] ] ) );\n                    if ( b[0] & 0x80 ) rsaKey[i] = new Uint8Array(b.length + 1), rsaKey[i].set( b, 1 );\n                }\n                if ( rsaKey.length > 2 ) prv = true, rsaKey.unshift( new Uint8Array([0]) ); // add version to PKCS#1 RSAPrivateKey structure\n                info[0][0] = '1.2.840.113549.1.1.1';\n                key = rsaKey;\n                break;\n            default:\n                throw new TypeError(\"Unsupported key type\");\n        }\n        info.push( new Uint8Array( der2b(key) ).buffer );\n        if ( !prv ) info[1] = { 'tag': 0x03, 'value': info[1] };\n        else info.unshift( new Uint8Array([0]) ); // add version to PKCS#8 PrivateKeyInfo structure\n        return new Uint8Array( der2b(info) ).buffer;\n    }\n\n    var oid2str = { 'KoZIhvcNAQEB': '1.2.840.113549.1.1.1' },\n        str2oid = { '1.2.840.113549.1.1.1': 'KoZIhvcNAQEB' };\n\n    function b2der ( buf, ctx ) {\n        if ( buf instanceof ArrayBuffer ) buf = new Uint8Array(buf);\n        if ( !ctx ) ctx = { pos: 0, end: buf.length };\n\n        if ( ctx.end - ctx.pos < 2 || ctx.end > buf.length ) throw new RangeError(\"Malformed DER\");\n\n        var tag = buf[ctx.pos++],\n            len = buf[ctx.pos++];\n\n        if ( len >= 0x80 ) {\n            len &= 0x7f;\n            if ( ctx.end - ctx.pos < len ) throw new RangeError(\"Malformed DER\");\n            for ( var xlen = 0; len--; ) xlen <<= 8, xlen |= buf[ctx.pos++];\n            len = xlen;\n        }\n\n        if ( ctx.end - ctx.pos < len ) throw new RangeError(\"Malformed DER\");\n\n        var rv;\n\n        switch ( tag ) {\n            case 0x02: // Universal Primitive INTEGER\n                rv = buf.subarray( ctx.pos, ctx.pos += len );\n                break;\n            case 0x03: // Universal Primitive BIT STRING\n                if ( buf[ctx.pos++] ) throw new Error( \"Unsupported bit string\" );\n                len--;\n            case 0x04: // Universal Primitive OCTET STRING\n                rv = new Uint8Array( buf.subarray( ctx.pos, ctx.pos += len ) ).buffer;\n                break;\n            case 0x05: // Universal Primitive NULL\n                rv = null;\n                break;\n            case 0x06: // Universal Primitive OBJECT IDENTIFIER\n                var oid = btoa( b2s( buf.subarray( ctx.pos, ctx.pos += len ) ) );\n                if ( !( oid in oid2str ) ) throw new Error( \"Unsupported OBJECT ID \" + oid );\n                rv = oid2str[oid];\n                break;\n            case 0x30: // Universal Constructed SEQUENCE\n                rv = [];\n                for ( var end = ctx.pos + len; ctx.pos < end; ) rv.push( b2der( buf, ctx ) );\n                break;\n            default:\n                throw new Error( \"Unsupported DER tag 0x\" + tag.toString(16) );\n        }\n\n        return rv;\n    }\n\n    function der2b ( val, buf ) {\n        if ( !buf ) buf = [];\n\n        var tag = 0, len = 0,\n            pos = buf.length + 2;\n\n        buf.push( 0, 0 ); // placeholder\n\n        if ( val instanceof Uint8Array ) {  // Universal Primitive INTEGER\n            tag = 0x02, len = val.length;\n            for ( var i = 0; i < len; i++ ) buf.push( val[i] );\n        }\n        else if ( val instanceof ArrayBuffer ) { // Universal Primitive OCTET STRING\n            tag = 0x04, len = val.byteLength, val = new Uint8Array(val);\n            for ( var i = 0; i < len; i++ ) buf.push( val[i] );\n        }\n        else if ( val === null ) { // Universal Primitive NULL\n            tag = 0x05, len = 0;\n        }\n        else if ( typeof val === 'string' && val in str2oid ) { // Universal Primitive OBJECT IDENTIFIER\n            var oid = s2b( atob( str2oid[val] ) );\n            tag = 0x06, len = oid.length;\n            for ( var i = 0; i < len; i++ ) buf.push( oid[i] );\n        }\n        else if ( val instanceof Array ) { // Universal Constructed SEQUENCE\n            for ( var i = 0; i < val.length; i++ ) der2b( val[i], buf );\n            tag = 0x30, len = buf.length - pos;\n        }\n        else if ( typeof val === 'object' && val.tag === 0x03 && val.value instanceof ArrayBuffer ) { // Tag hint\n            val = new Uint8Array(val.value), tag = 0x03, len = val.byteLength;\n            buf.push(0); for ( var i = 0; i < len; i++ ) buf.push( val[i] );\n            len++;\n        }\n        else {\n            throw new Error( \"Unsupported DER value \" + val );\n        }\n\n        if ( len >= 0x80 ) {\n            var xlen = len, len = 4;\n            buf.splice( pos, 0, (xlen >> 24) & 0xff, (xlen >> 16) & 0xff, (xlen >> 8) & 0xff, xlen & 0xff );\n            while ( len > 1 && !(xlen >> 24) ) xlen <<= 8, len--;\n            if ( len < 4 ) buf.splice( pos, 4 - len );\n            len |= 0x80;\n        }\n\n        buf.splice( pos - 2, 2, tag, len );\n\n        return buf;\n    }\n\n    function CryptoKey ( key, alg, ext, use ) {\n        Object.defineProperties( this, {\n            _key: {\n                value: key\n            },\n            type: {\n                value: key.type,\n                enumerable: true,\n            },\n            extractable: {\n                value: (ext === undefined) ? key.extractable : ext,\n                enumerable: true,\n            },\n            algorithm: {\n                value: (alg === undefined) ? key.algorithm : alg,\n                enumerable: true,\n            },\n            usages: {\n                value: (use === undefined) ? key.usages : use,\n                enumerable: true,\n            },\n        });\n    }\n\n    function isPubKeyUse ( u ) {\n        return u === 'verify' || u === 'encrypt' || u === 'wrapKey';\n    }\n\n    function isPrvKeyUse ( u ) {\n        return u === 'sign' || u === 'decrypt' || u === 'unwrapKey';\n    }\n\n    [ 'generateKey', 'importKey', 'unwrapKey' ]\n        .forEach( function ( m ) {\n            var _fn = _subtle[m];\n\n            _subtle[m] = function ( a, b, c ) {\n                var args = [].slice.call(arguments),\n                    ka, kx, ku;\n\n                switch ( m ) {\n                    case 'generateKey':\n                        ka = alg(a), kx = b, ku = c;\n                        break;\n                    case 'importKey':\n                        ka = alg(c), kx = args[3], ku = args[4];\n                        if ( a === 'jwk' ) {\n                            b = b2jwk(b);\n                            if ( !b.alg ) b.alg = jwkAlg(ka);\n                            if ( !b.key_ops ) b.key_ops = ( b.kty !== 'oct' ) ? ( 'd' in b ) ? ku.filter(isPrvKeyUse) : ku.filter(isPubKeyUse) : ku.slice();\n                            args[1] = jwk2b(b);\n                        }\n                        break;\n                    case 'unwrapKey':\n                        ka = args[4], kx = args[5], ku = args[6];\n                        args[2] = c._key;\n                        break;\n                }\n\n                if ( m === 'generateKey' && ka.name === 'HMAC' && ka.hash ) {\n                    ka.length = ka.length || { 'SHA-1': 512, 'SHA-256': 512, 'SHA-384': 1024, 'SHA-512': 1024 }[ka.hash.name];\n                    return _subtle.importKey( 'raw', _crypto.getRandomValues( new Uint8Array( (ka.length+7)>>3 ) ), ka, kx, ku );\n                }\n\n                if ( isWebkit && m === 'generateKey' && ka.name === 'RSASSA-PKCS1-v1_5' && ( !ka.modulusLength || ka.modulusLength >= 2048 ) ) {\n                    a = alg(a), a.name = 'RSAES-PKCS1-v1_5', delete a.hash;\n                    return _subtle.generateKey( a, true, [ 'encrypt', 'decrypt' ] )\n                        .then( function ( k ) {\n                            return Promise.all([\n                                _subtle.exportKey( 'jwk', k.publicKey ),\n                                _subtle.exportKey( 'jwk', k.privateKey ),\n                            ]);\n                        })\n                        .then( function ( keys ) {\n                            keys[0].alg = keys[1].alg = jwkAlg(ka);\n                            keys[0].key_ops = ku.filter(isPubKeyUse), keys[1].key_ops = ku.filter(isPrvKeyUse);\n                            return Promise.all([\n                                _subtle.importKey( 'jwk', keys[0], ka, true, keys[0].key_ops ),\n                                _subtle.importKey( 'jwk', keys[1], ka, kx, keys[1].key_ops ),\n                            ]);\n                        })\n                        .then( function ( keys ) {\n                            return {\n                                publicKey: keys[0],\n                                privateKey: keys[1],\n                            };\n                        });\n                }\n\n                if ( ( isWebkit || ( isIE && ( ka.hash || {} ).name === 'SHA-1' ) )\n                        && m === 'importKey' && a === 'jwk' && ka.name === 'HMAC' && b.kty === 'oct' ) {\n                    return _subtle.importKey( 'raw', s2b( a2s(b.k) ), c, args[3], args[4] );\n                }\n\n                if ( isWebkit && m === 'importKey' && ( a === 'spki' || a === 'pkcs8' ) ) {\n                    return _subtle.importKey( 'jwk', pkcs2jwk(b), c, args[3], args[4] );\n                }\n\n                if ( isIE && m === 'unwrapKey' ) {\n                    return _subtle.decrypt( args[3], c, b )\n                        .then( function ( k ) {\n                            return _subtle.importKey( a, k, args[4], args[5], args[6] );\n                        });\n                }\n\n                var op;\n                try {\n                    op = _fn.apply( _subtle, args );\n                }\n                catch ( e ) {\n                    return Promise.reject(e);\n                }\n\n                if ( isIE ) {\n                    op = new Promise( function ( res, rej ) {\n                        op.onabort =\n                        op.onerror =    function ( e ) { rej(e)               };\n                        op.oncomplete = function ( r ) { res(r.target.result) };\n                    });\n                }\n\n                op = op.then( function ( k ) {\n                    if ( ka.name === 'HMAC' ) {\n                        if ( !ka.length ) ka.length = 8 * k.algorithm.length;\n                    }\n                    if ( ka.name.search('RSA') == 0 ) {\n                        if ( !ka.modulusLength ) ka.modulusLength = (k.publicKey || k).algorithm.modulusLength;\n                        if ( !ka.publicExponent ) ka.publicExponent = (k.publicKey || k).algorithm.publicExponent;\n                    }\n                    if ( k.publicKey && k.privateKey ) {\n                        k = {\n                            publicKey: new CryptoKey( k.publicKey, ka, kx, ku.filter(isPubKeyUse) ),\n                            privateKey: new CryptoKey( k.privateKey, ka, kx, ku.filter(isPrvKeyUse) ),\n                        };\n                    }\n                    else {\n                        k = new CryptoKey( k, ka, kx, ku );\n                    }\n                    return k;\n                });\n\n                return op;\n            }\n        });\n\n    [ 'exportKey', 'wrapKey' ]\n        .forEach( function ( m ) {\n            var _fn = _subtle[m];\n\n            _subtle[m] = function ( a, b, c ) {\n                var args = [].slice.call(arguments);\n\n                switch ( m ) {\n                    case 'exportKey':\n                        args[1] = b._key;\n                        break;\n                    case 'wrapKey':\n                        args[1] = b._key, args[2] = c._key;\n                        break;\n                }\n\n                if ( ( isWebkit || ( isIE && ( b.algorithm.hash || {} ).name === 'SHA-1' ) )\n                        && m === 'exportKey' && a === 'jwk' && b.algorithm.name === 'HMAC' ) {\n                    args[0] = 'raw';\n                }\n\n                if ( isWebkit && m === 'exportKey' && ( a === 'spki' || a === 'pkcs8' ) ) {\n                    args[0] = 'jwk';\n                }\n\n                if ( isIE && m === 'wrapKey' ) {\n                    return _subtle.exportKey( a, b )\n                        .then( function ( k ) {\n                            if ( a === 'jwk' ) k = s2b( unescape( encodeURIComponent( JSON.stringify( b2jwk(k) ) ) ) );\n                            return  _subtle.encrypt( args[3], c, k );\n                        });\n                }\n\n                var op;\n                try {\n                    op = _fn.apply( _subtle, args );\n                }\n                catch ( e ) {\n                    return Promise.reject(e);\n                }\n\n                if ( isIE ) {\n                    op = new Promise( function ( res, rej ) {\n                        op.onabort =\n                        op.onerror =    function ( e ) { rej(e)               };\n                        op.oncomplete = function ( r ) { res(r.target.result) };\n                    });\n                }\n\n                if ( m === 'exportKey' && a === 'jwk' ) {\n                    op = op.then( function ( k ) {\n                        if ( ( isWebkit || ( isIE && ( b.algorithm.hash || {} ).name === 'SHA-1' ) )\n                                && b.algorithm.name === 'HMAC') {\n                            return { 'kty': 'oct', 'alg': jwkAlg(b.algorithm), 'key_ops': b.usages.slice(), 'ext': true, 'k': s2a( b2s(k) ) };\n                        }\n                        k = b2jwk(k);\n                        if ( !k.alg ) k['alg'] = jwkAlg(b.algorithm);\n                        if ( !k.key_ops ) k['key_ops'] = ( b.type === 'public' ) ? b.usages.filter(isPubKeyUse) : ( b.type === 'private' ) ? b.usages.filter(isPrvKeyUse) : b.usages.slice();\n                        return k;\n                    });\n                }\n\n                if ( isWebkit && m === 'exportKey' && ( a === 'spki' || a === 'pkcs8' ) ) {\n                    op = op.then( function ( k ) {\n                        k = jwk2pkcs( b2jwk(k) );\n                        return k;\n                    });\n                }\n\n                return op;\n            }\n        });\n\n    [ 'encrypt', 'decrypt', 'sign', 'verify' ]\n        .forEach( function ( m ) {\n            var _fn = _subtle[m];\n\n            _subtle[m] = function ( a, b, c, d ) {\n                if ( isIE && ( !c.byteLength || ( d && !d.byteLength ) ) )\n                    throw new Error(\"Empy input is not allowed\");\n\n                var args = [].slice.call(arguments),\n                    ka = alg(a);\n\n                if ( isIE && m === 'decrypt' && ka.name === 'AES-GCM' ) {\n                    var tl = a.tagLength >> 3;\n                    args[2] = (c.buffer || c).slice( 0, c.byteLength - tl ),\n                    a.tag = (c.buffer || c).slice( c.byteLength - tl );\n                }\n\n                args[1] = b._key;\n\n                var op;\n                try {\n                    op = _fn.apply( _subtle, args );\n                }\n                catch ( e ) {\n                    return Promise.reject(e);\n                }\n\n                if ( isIE ) {\n                    op = new Promise( function ( res, rej ) {\n                        op.onabort =\n                        op.onerror = function ( e ) {\n                            rej(e);\n                        };\n\n                        op.oncomplete = function ( r ) {\n                            var r = r.target.result;\n\n                            if ( m === 'encrypt' && r instanceof AesGcmEncryptResult ) {\n                                var c = r.ciphertext, t = r.tag;\n                                r = new Uint8Array( c.byteLength + t.byteLength );\n                                r.set( new Uint8Array(c), 0 );\n                                r.set( new Uint8Array(t), c.byteLength );\n                                r = r.buffer;\n                            }\n\n                            res(r);\n                        };\n                    });\n                }\n\n                return op;\n            }\n        });\n\n    if ( isIE ) {\n        var _digest = _subtle.digest;\n\n        _subtle['digest'] = function ( a, b ) {\n            if ( !b.byteLength )\n                throw new Error(\"Empy input is not allowed\");\n\n            var op;\n            try {\n                op = _digest.call( _subtle, a, b );\n            }\n            catch ( e ) {\n                return Promise.reject(e);\n            }\n\n            op = new Promise( function ( res, rej ) {\n                op.onabort =\n                op.onerror =    function ( e ) { rej(e)               };\n                op.oncomplete = function ( r ) { res(r.target.result) };\n            });\n\n            return op;\n        };\n\n        global.crypto = Object.create( _crypto, {\n            getRandomValues: { value: function ( a ) { return _crypto.getRandomValues(a) } },\n            subtle:          { value: _subtle },\n        });\n\n        global.CryptoKey = CryptoKey;\n    }\n\n    if ( isWebkit ) {\n        _crypto.subtle = _subtle;\n\n        global.Crypto = _Crypto;\n        global.SubtleCrypto = _SubtleCrypto;\n        global.CryptoKey = CryptoKey;\n    }\n}));\n\n export default {} // section modified by isomorphic-webcrypto build \n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(952);\n"], "names": ["lib_1", "require", "strings_1", "settings_1", "FhirClient_1", "Response", "window", "debug", "extend", "Client", "default", "constructor", "environment", "state", "_state", "serverUrl", "assert", "match", "super", "units", "this", "_refreshTask", "client", "patient", "id", "getPatientId", "read", "requestOptions", "request", "url", "Promise", "reject", "Error", "fhirOptions", "options", "async", "base", "absolute", "contextualURL", "_url", "resourceType", "pathname", "split", "pop", "patientCompartment", "indexOf", "conformance", "fetchConformanceStatement", "searchParam", "getPatientParam", "searchParams", "set", "href", "URL", "contextualize", "encounter", "getEncounterId", "user", "fhirUser", "getFhirUser", "getUserId", "getUserType", "connect", "fhir", "fhirJs", "baseUrl", "replace", "accessToken", "getState", "auth", "token", "username", "password", "pass", "api", "patientId", "tokenResponse", "scope", "noScopeForId", "authorize<PERSON><PERSON>", "noIfNoAuth", "noFreeContext", "getIdToken", "idToken", "id_token", "hasOpenid", "hasProfile", "has<PERSON>hir<PERSON>ser", "jwtDecode", "slice", "join", "profile", "getAuthorizationHeader", "btoa", "_clearState", "storage", "getStorage", "key", "get", "SMART_KEY", "unset", "_resolvedRefs", "debugRequest", "String", "graph", "flat", "pageLimit", "_a", "resolveReferences", "makeArray", "useRefreshToken", "onPage", "undefined", "signal", "refreshIfNeeded", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "response", "headers", "authorization", "fhirRequest", "then", "result", "includeResponse", "body", "catch", "error", "status", "message", "expired", "data", "fetchReferences", "resolve", "_data", "links", "link", "entry", "map", "resource", "next", "find", "l", "relation", "nextPage", "length", "Object", "assign", "references", "concat", "refreshToken", "expiresAt", "Date", "now", "refresh", "debugRefresh", "_b", "refresh_token", "tokenUri", "scopes", "hasOfflineAccess", "search", "hasOnlineAccess", "encodeURIComponent", "refreshTokenWithClientId", "clientId", "refreshRequestOptions", "credentials", "refreshTokenWithCredentials", "method", "mode", "clientSecret", "access_token", "getAccessTokenExpiration", "finally", "byCode", "observations", "property", "byCodes", "<PERSON><PERSON><PERSON>", "obj", "path", "exports", "fhirBaseUrl", "create", "JSON", "stringify", "update", "delete", "patch", "assertJsonPatch", "resolveRef", "cache", "node", "isArray", "Array", "all", "filter", "Boolean", "item", "i", "ref", "reference", "cacheMap", "sub", "set<PERSON>ath", "ex", "console", "warn", "paths", "trim", "reduce", "prev", "cur", "includes", "push", "groups", "for<PERSON>ach", "len", "task", "keys", "sort", "group", "getReferences", "refs", "out", "resources", "bundleOrUrl", "count", "page", "pages", "limit", "fetchPage", "aborted", "nextLink", "uri", "res", "getFhirVersion", "metadata", "fhirVersion", "getFhirRelease", "v", "fhirVersions", "HttpError", "statusText", "name", "statusCode", "parse", "bodyUsed", "type", "json", "error_description", "text", "toJSON", "smart_1", "Client_1", "BrowserStorage_1", "security", "js_base64_1", "_storage", "replaceBrowserHistory", "fullSessionStorageSupport", "relative", "getUrl", "location", "redirect", "to", "getAbortController", "AbortController", "atob", "str", "base64urlencode", "input", "encodeURL", "fromUint8Array", "base64urldecode", "decode", "getSmartApi", "ready", "args", "authorize", "init", "utils", "BrowserAdapter_1", "adapter", "FHIR", "FhirClient", "oauth2", "settings", "module", "HttpError_1", "fetch", "_debug", "ensureNumerical", "value", "code", "checkResponse", "resp", "ok", "responseToJSON", "loweCaseKeys", "lowerKey", "toLowerCase", "accept", "getAndCache", "force", "process", "segments", "shift", "o", "arg", "env", "payload", "ret", "handleCodeableConcept", "concept", "observation", "coding", "condition", "cm", "kg", "any", "pq", "createEmpty", "idx", "arr", "str<PERSON><PERSON><PERSON>", "charSet", "char<PERSON>t", "Math", "floor", "random", "secondsAhead", "from", "expires_in", "tokenBody", "exp", "bank", "codes", "meta", "r", "x", "patientParams", "p", "target", "width", "height", "self", "parent", "top", "targetWindow", "open", "e", "screen", "frames", "operation", "op", "crypto", "globalThis", "subtle", "isSecureContext", "ALGS", "ES384", "namedCurve", "RS384", "modulus<PERSON>ength", "publicExponent", "Uint8Array", "hash", "randomBytes", "getRandomValues", "digestSha256", "prepared", "TextEncoder", "encode", "digest", "entropy", "inputBytes", "codeVerifier", "codeChallenge", "jwk", "alg", "key_ops", "importKey", "ext", "privateKey", "header", "jwtHeader", "jwtPayload", "jwtAuthenticatedContent", "signature", "sign", "algorithm", "enumerable", "<PERSON><PERSON><PERSON><PERSON>", "fetchWellKnownJson", "getSecurityExtensions", "authorization_endpoint", "token_endpoint", "registrationUri", "registration_endpoint", "codeChallengeMethods", "code_challenge_methods_supported", "getSecurityExtensionsFromWellKnownJson", "extensions", "extension", "valueUri", "getSecurityExtensionsFromConformanceStatement", "params", "urlISS", "cfg", "issMatch", "RegExp", "test", "fakeTokenResponse", "encounterId", "pkceMode", "clientPublicKeySetUrl", "redirect_uri", "client_id", "iss", "launch", "fhirServiceUrl", "redirectUri", "noRedirect", "completeInTarget", "clientPrivateJwk", "stateKey", "inFrame", "isInFrame", "inPopUp", "isInPopUp", "<PERSON><PERSON><PERSON>", "randomString", "redirectUrl", "redirectParams", "S256supported", "shouldIncludeChallenge", "generatePKCEChallenge", "win", "getTargetWindow", "sessionStorage", "removeItem", "setItem", "addEventListener", "onMessage", "opener", "origin", "removeEventListener", "Storage", "authError", "authErrorDescription", "postMessage", "close", "hasState", "has", "history", "replaceState", "buildTokenRequest", "pk", "importJWK", "jwtHeaders", "typ", "kid", "jku", "jwtClaims", "aud", "jti", "getTimeInFuture", "clientAssertion", "signCompactJws", "authorizeOptions", "readyOptions", "cached", "formatArgs", "useColors", "namespace", "humanize", "diff", "c", "color", "splice", "index", "lastC", "save", "namespaces", "load", "getItem", "DEBUG", "__nwjs", "navigator", "userAgent", "m", "document", "documentElement", "style", "WebkitAppearance", "firebug", "exception", "table", "parseInt", "localStorage", "localstorage", "destroy", "warned", "colors", "log", "formatters", "j", "createDebug", "prevTime", "namespacesCache", "enabledCache", "enableOverride", "enabled", "curr", "Number", "ms", "coerce", "unshift", "format", "formatter", "val", "call", "apply", "selectColor", "defineProperty", "configurable", "delimiter", "newDebug", "toNamespace", "regexp", "toString", "substring", "stack", "disable", "names", "skips", "enable", "charCodeAt", "abs", "g", "tab", "version", "VERSION", "_hasBuffer", "<PERSON><PERSON><PERSON>", "_TD", "TextDecoder", "_TE", "b64chs", "prototype", "b64tab", "b64re", "_fromCC", "fromCharCode", "bind", "_U8Afrom", "it", "_mkUriSafe", "src", "m0", "_tidyB64", "s", "btoaPolyfill", "bin", "u32", "c0", "c1", "c2", "asc", "pad", "TypeError", "_btoa", "_fromUint8Array", "u8a", "strs", "subarray", "urlsafe", "cb_utob", "cc", "re_utob", "utob", "u", "_encode", "encodeURI", "re_btou", "cb_btou", "cccc", "offset", "btou", "b", "atobPolyfill", "u24", "r1", "r2", "_atob", "_toUint8Array", "a", "toUint8Array", "_unURI", "_decode", "_noEnum", "writable", "extendString", "_add", "extendUint8Array", "gBase64", "fromBase64", "toBase64", "<PERSON><PERSON><PERSON><PERSON>", "extendBuiltins", "k", "Base64", "factory", "h", "d", "w", "plural", "msAbs", "n", "isPlural", "round", "exec", "parseFloat", "isFinite", "long", "fmtShort", "global", "_crypto", "msCrypto", "_subtle", "webkitSubtle", "_Crypto", "Crypto", "_SubtleCrypto", "SubtleCrypto", "isEdge", "CryptoKey", "Key", "isIE", "isWebkit", "oid2str", "str2oid", "_fn", "ka", "kx", "ku", "arguments", "b2jwk", "jwkAlg", "kty", "isPrvKeyUse", "isPubKeyUse", "s2b", "unescape", "buffer", "_key", "<PERSON><PERSON>ey", "exportKey", "public<PERSON>ey", "a2s", "info", "b2der", "prv", "rsaComp", "rsaKey", "s2a", "b2s", "pkcs2jwk", "decrypt", "rej", "<PERSON>ab<PERSON>", "onerror", "oncomplete", "encrypt", "usages", "der2b", "jwk2pkcs", "byteLength", "tl", "tag<PERSON><PERSON><PERSON>", "tag", "AesGcmEncryptResult", "ciphertext", "t", "_digest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toUpperCase", "SyntaxError", "decodeURIComponent", "escape", "extractable", "buf", "ctx", "pos", "end", "RangeError", "rv", "xlen", "oid", "use", "defineProperties", "define", "amd", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "definition", "Function", "prop", "hasOwnProperty", "__webpack_exports__"], "sourceRoot": ""}