<!DOCTYPE html>
<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <link rel='stylesheet' type='text/css' href='./src/css/example-smart-app.css'>
  <!--
      Temporarily disable cerner-smart-embeddable-lib
      <link rel='stylesheet' type='text/css' href='./lib/css/cerner-smart-embeddable-lib-1.0.0.min.css'>
    -->
  <title>seb Example-SMART-App v1B</title>
</head>

<body>
  <!-- Required JS files to enable this page to embed within an MPage -->
  <!--
      Temporarily disable cerner-smart-embeddable-lib
      <script src='https://cdnjs.cloudflare.com/ajax/libs/babel-polyfill/6.26.0/polyfill.min.js'></script>
      <script src='./lib/js/cerner-smart-embeddable-lib-1.0.0.min.js'></script>
    -->

  <!-- FHIR Client JS Library -->
  <script src='./lib/js/fhir-client.js'></script>

  <!-- Prevent session bleed caused by single threaded embedded browser and sessionStorage API -->
  <!-- https://github.com/cerner/fhir-client-cerner-additions -->
  <!-- <script src='./lib/js/fhir-client-cerner-additions-1.0.0.js'></script> -->
  <script>
    localStorage.debug = "FHIR.*"
    //cern tut v2 pub
    let clientId = "b1bd6e92-ec3e-49d5-98e5-597d74da28ff";
    //cern tut v2 pub patient
    // clientId = "70ee3a81-af63-4ff1-93fc-6929bcb6e946";

    debugger;
    FHIR.oauth2.authorize({
      'client_id': clientId,
      'scope': 'patient/*.rs patient/Patient.rs patient/Observation.rs fhirUser launch online_access openid profile'
    });
    debugger;
  </script>
  <div class="spinner">
    <div class="bounce1"></div>
    <div class="bounce2"></div>
    <div class="bounce3"></div>
  </div>
</body>

</html>