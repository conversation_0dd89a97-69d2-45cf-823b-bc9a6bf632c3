<!DOCTYPE html>
<html lang="en">

<head>
  <meta http-equiv='X-UA-Compatible' content='IE=edge' />
  <meta http-equiv='Content-Type' content='text/html; charset=utf-8' />
  <title>Sebbans Example-SMART-App v1B</title>

  <link rel='stylesheet' type='text/css' href='./src/css/example-smart-app.css'>
  <!--
      Temporarily disable cerner-smart-embeddable-lib
      <link rel='stylesheet' type='text/css' href='./lib/css/cerner-smart-embeddable-lib-1.0.0.min.css'>
    -->
</head>

<body>
  <h1>Fhir index page</h1>
  <div id='errors'>
  </div>
  <div id="loading" class="spinner">
    <div class="bounce1"></div>
    <div class="bounce2"></div>
    <div class="bounce3"></div>
  </div>
  < id='holder'>
    <h2>Example-SMART-App</h2>

    <h2>Patient Resource</h2>
    <table>
      <tr>
        <th>First Name:</th>
        <td id='fname'></td>
      </tr>
      <tr>
        <th>Last Name:</th>
        <td id='lname'></td>
      </tr>
      <tr>
        <th>Gender:</th>
        <td id='gender'></td>
      </tr>
      <tr>
        <th>Date of Birth:</th>
        <td id='birthdate'></td>
      </tr>
    </table>
    <h2>Observation Resource</h2>
    <table>
      <tr>
        <th>Height:</th>
        <td id='height'></td>
      </tr>
      <tr>
        <th>Systolic Blood Pressure:</th>
        <td id='systolicbp'></td>

      </tr>
      <tr>
        <th>Diastolic Blood Pressure:</th>
        <td id='diastolicbp'></td>
      </tr>
      <tr>
        <th>LDL:</th>
        <td id='ldl'></td>
      </tr>
      <tr>
        <th>HDL:</th>
        <td id='hdl'></td>
      </tr>
    </table>
    <div>
      <p>
        <code id="patient"></code>
      </p>
    </div>
    </div>
    <!-- Required JS files to enable this page to embed within an MPage -->
    <!--
      Temporarily disable cerner-smart-embeddable-lib
      <script src='https://cdnjs.cloudflare.com/ajax/libs/babel-polyfill/6.26.0/polyfill.min.js'></script>
      <script src='./lib/js/cerner-smart-embeddable-lib-1.0.0.min.js'></script>
    -->

    <!-- Application-level javascript-->
    <script src='./src/js/example-smart-app.js'></script>

    <!-- FHIR Client JS Library -->
    <script src='./lib/js/fhir-client.js'></script>

    <!-- Prevent session bleed caused by single threaded embedded browser and sessionStorage API -->
    <!-- https://github.com/cerner/fhir-client-cerner-additions -->
    <script src='./lib/js/fhir-client-cerner-additions-1.0.0.js'></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script>
      localStorage.debug = "FHIR.*"
      debugger;
      extractData().then(
        //Display Patient Demographics and Observations if extractData was success
        function (p) {
          drawVisualization(p);
        },

        //Display 'Failed to call FHIR Service' if extractData failed
        function () {
          $('#loading').hide();
          $('#errors').html('<p> Failed to call FHIR Service </p>');
        }
      );
      // debugger;

    </script>
</body>

</html>

<!-- https://smart.cerner.com/smart/ec2458f2-1e24-41c8-b71b-0e701af7583d/apps/dd1deca5-e6fd-49c4-bb30-8b31bba2d3fa?PAT_PersonId=12724065&VIS_EncntrId=97953483&USR_PersonId=12742069&username=portal&need_patient_banner=true -->